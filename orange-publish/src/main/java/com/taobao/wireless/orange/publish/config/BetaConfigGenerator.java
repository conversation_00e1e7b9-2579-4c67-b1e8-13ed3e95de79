package com.taobao.wireless.orange.publish.config;

import com.taobao.wireless.orange.common.constant.enums.ConfigType;
import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import com.taobao.wireless.orange.publish.config.model.Condition;
import com.taobao.wireless.orange.publish.config.model.GrayConfig;
import com.taobao.wireless.orange.publish.config.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.publish.config.model.Parameter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * BETA 配置生成器
 *
 * <AUTHOR>
 */
@Component
public class BetaConfigGenerator extends GrayConfigGenerator {
    @Override
    protected ResourceType getResourceType() {
        return ResourceType.BETA_CONFIG;
    }

    /**
     * 获取待生成 Beta 配置的发布版本
     *
     * @param namespaceId 命名空间ID
     * @return 发布单版本号(beta 场景只会有一个)
     */
    @Override
    protected List<String> getReleaseVersions(String namespaceId, String releaseVersion) {
        return Collections.singletonList(releaseVersion);
    }

    @Override
    protected GrayConfig buildResult(NamespaceIdNameRecord namespace,
                                     List<Parameter> updateParameters,
                                     List<OParameterVersionDO> offlineParameterDOs,
                                     List<Condition> conditions) {
        GrayConfig grayConfig = super.buildResult(namespace, updateParameters, offlineParameterDOs, conditions);
        grayConfig.setType(ConfigType.BETA);
        return grayConfig;
    }
}
