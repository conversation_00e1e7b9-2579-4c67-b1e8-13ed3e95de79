package com.taobao.wireless.orange.publish.index;

import com.alibaba.fastjson.JSON;
import com.taobao.mtop.commons.utils.CollectionUtil;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.external.switchcenter.SwitchConfig;
import com.taobao.wireless.orange.oswitch.dal.dao.*;
import com.taobao.wireless.orange.oswitch.dal.entity.*;
import com.taobao.wireless.orange.publish.config.ConfigGenerateService;
import com.taobao.wireless.orange.publish.config.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.publish.index.model.Index;
import com.taobao.wireless.orange.publish.resource.ResourceService;
import com.taobao.wireless.orange.publish.resource.model.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class IndexGenerateService {
    @Autowired
    private ConfigGenerateService configGenerateService;

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private OIndexDAO indexDAO;

    @Autowired
    private OResourceDAO resourceDAO;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private ONamespaceVersionResourceDAO namespaceVersionResourceDAO;

    @Value("${orange.switch.cdn.domain}")
    private String cdnDomain;

    /**
     * 生成指定应用的索引文件(包含全量和差量)
     *
     * @param appKey 应用标识
     * @return 生成的索引文件列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<OIndexDO> generate(String appKey, List<ONamespaceVersionDO> namespaceVersions, String indexVersion) {
        if (CollectionUtil.isEmpty(namespaceVersions)) {
            return List.of();
        }

        // 1. 生成全量索引文件
        Index fullIndex = generateFullIndex(appKey, namespaceVersions, indexVersion);
        Resource fullResource = resourceService.create(fullIndex, ResourceType.FULL_INDEX);

        List<OIndexDO> newIndices = new ArrayList<>();
        OIndexDO fullIndexDO = OIndexDO.builder()
                .indexVersion(indexVersion)
                .appKey(appKey)
                .baseIndexVersion("0")
                .isAvailable(Available.y)
                .indexResourceId(fullResource.getResourceId())
                .build();
        newIndices.add(fullIndexDO);

        // 2. 生成差量索引文件
        // 计算需要生成的差量区间起点 changeVersion 列表
        List<String> baseIndexVersions = groupNamespaceVersionsByTimeGaps(System.currentTimeMillis(), namespaceVersions, SwitchConfig.diffProbeGapMinutes);

        for (int i = 0; i < baseIndexVersions.size(); i++) {
            String baseIndexVersion = baseIndexVersions.get(i);
            // 生成差量索引
            Index incrementalIndex = generateIncrementalIndex(fullIndex, Pair.of(baseIndexVersion, indexVersion));
            Resource indexResource = resourceService.create(fullResource.getResourceId() + "-" + i, incrementalIndex, ResourceType.INCREMENTAL_INDEX);

            OIndexDO diffIndexDO = BeanUtil.createFromProperties(fullIndexDO, OIndexDO.class);
            diffIndexDO.setBaseIndexVersion(baseIndexVersion);
            diffIndexDO.setIndexResourceId(indexResource.getResourceId());
            newIndices.add(diffIndexDO);
        }

        // 3.数据库新增索引记录
        createIndices(appKey, newIndices);

        return newIndices;
    }

    /**
     * 生成全量索引
     *
     * @param appKey
     * @param namespaceVersions
     * @return
     */
    private Index generateFullIndex(String appKey, List<ONamespaceVersionDO> namespaceVersions, String indexVersion) {
        List<String> namespaceIds = namespaceVersions.stream().map(ONamespaceVersionDO::getNamespaceId).collect(Collectors.toList());

        Map<String, NamespaceIdNameRecord> namespaceId2Obj = getNamespaceId2IdNamePair(namespaceIds);

        Map<String, Map<ConfigType, NamespaceVersionContentResource>> namespaceId2ResourceMap = getNamespaceVersionContentResources(namespaceIds);

        // fixme: 存在数据一致性问题，最好进行加锁
        Map<String, List<OReleaseOrderDO>> namespaceId2Orders = getInProgressReleaseOrdersGroupedByNamespaceId(namespaceIds);

        List<Index.Namespace> namespaces = namespaceVersions.stream().map(namespace -> {
                    //  namespace 第一次创建发布单时，type2ContentResource 可能为空
                    Map<ConfigType, NamespaceVersionContentResource> type2ContentResource = namespaceId2ResourceMap.get(namespace.getNamespaceId());

                    // 组装 releaseConfig 配置
                    Index.Config releaseConfig = buildConfigFromContentResource(type2ContentResource, ConfigType.RELEASE);

                    // 组装 grayConfig 配置
                    List<OReleaseOrderDO> inProgressOrders = namespaceId2Orders.get(namespace.getNamespaceId());
                    Index.GrayConfig grayConfig = buildGrayConfigFromContentResource(type2ContentResource, inProgressOrders);

                    return Index.Namespace.builder()
                            .name(namespaceId2Obj.get(namespace.getNamespaceId()).name())
                            .changeVersion(Long.parseLong(namespace.getNamespaceChangeVersion()))
                            .release(releaseConfig)
                            .gray(grayConfig)
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return Index.builder()
                .schemaVersion("1.0")
                .comboPolicy(JSON.parseObject(SwitchConfig.comboPolicy, Index.ComboPolicy.class))
                .cdn(cdnDomain)
                .appKey(appKey)
                .baseVersion(0L)
                .version(Long.parseLong(indexVersion))
                .offlineNamespaces(null)
                .strategy(ConfigStrategy.FULL)
                .namespaces(namespaces)
                .build();
    }

    /**
     * 生成差量索引文件
     *
     * @param fullIndex
     * @param changeVersionRange
     * @return
     */
    private Index generateIncrementalIndex(Index fullIndex, Pair<String, String> changeVersionRange) {
        String appKey = fullIndex.getAppKey();
        Pair<List<NamespaceIdNameRecord>, List<String>> onlineAndOffline = getOnlineNamespaceRecordsAndOfflineNames(appKey, changeVersionRange);

        Map<String, Index.Namespace> namespaceName2FullConfig = fullIndex.getNamespaces()
                .stream()
                .collect(Collectors.toMap(Index.Namespace::getName, Function.identity()));

        String baseIndexVersion = changeVersionRange.getLeft();
        List<String> offlineNamespaceNames = onlineAndOffline.getRight();

        List<Index.Namespace> onlineNamespaces = onlineAndOffline.getLeft().stream()
                .filter(namespace -> !offlineNamespaceNames.contains(namespace.name()))
                .map(namespace -> {
                    Index.Namespace fullNamespaceConfig = namespaceName2FullConfig.get(namespace.name());

                    // namespace 第一次创建发布单时存在没有对应资源的情况
                    if (fullNamespaceConfig == null) {
                        return null;
                    }

                    Index.Namespace namespaceConfig = Index.Namespace.builder()
                            .name(fullNamespaceConfig.getName())
                            .changeVersion(fullNamespaceConfig.getChangeVersion())
                            // 灰度和实验都只使用全量配置
                            .gray(fullNamespaceConfig.getGray())
                            .experiment(fullNamespaceConfig.getExperiment())
                            .build();

                    // 有正式发布产物
                    if (fullNamespaceConfig.getRelease() != null) {
                        long releaseVersion = fullNamespaceConfig.getRelease().getVersion();
                        // 生成差量发布配置
                        Resource releaseConfig = this.configGenerateService.generate(namespace.namespaceId(), ResourceType.INCREMENTAL_RELEASE_CONFIG, baseIndexVersion);
                        Index.Config release = Optional.ofNullable(releaseConfig).map(r -> {
                                    Index.Config config = new Index.Config();
                                    config.setResourceId(r.getResourceId());
                                    config.setResourceSize(r.getResourceSize());
                                    config.setVersion(releaseVersion);
                                    return config;
                                })
                                .orElse(null);
                        namespaceConfig.setRelease(release);
                    }
                    return namespaceConfig;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Index index = BeanUtil.createFromProperties(fullIndex, Index.class);
        index.setStrategy(ConfigStrategy.INCREMENTAL);
        index.setBaseVersion(Long.parseLong(baseIndexVersion));
        index.setOfflineNamespaces(offlineNamespaceNames);
        index.setNamespaces(onlineNamespaces);

        return index;
    }

    /**
     * 新增索引记录
     *
     * @param appKey
     * @param indices
     */
    private void createIndices(String appKey, List<OIndexDO> indices) {
        // 失效历史索引
        indexDAO.lambdaUpdate()
                .eq(OIndexDO::getAppKey, appKey)
                .set(OIndexDO::getIsAvailable, Available.n)
                .update();
        // 新增索引
        indexDAO.saveBatch(indices);
    }

    /**
     * 获取进行中的发布单记录，按namespaceId分组
     *
     * @param namespaceIds namespace ID列表
     * @return 按namespaceId分组的发布单记录
     */
    private Map<String, List<OReleaseOrderDO>> getInProgressReleaseOrdersGroupedByNamespaceId(List<String> namespaceIds) {
        return releaseOrderDAO.lambdaQuery()
                .in(OReleaseOrderDO::getNamespaceId, namespaceIds)
                .eq(OReleaseOrderDO::getStatus, ReleaseOrderStatus.IN_PROGRESS)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OReleaseOrderDO::getNamespaceId, Collectors.toList()));
    }


    /**
     * 构建灰度配置
     *
     * @param type2ContentResource 配置类型到内容资源的映射
     * @param releaseOrders        进行中的发布单列表
     * @return 灰度配置，如果没有进行中的发布单则返回null
     */
    private Index.GrayConfig buildGrayConfigFromContentResource(Map<ConfigType, NamespaceVersionContentResource> type2ContentResource,
                                                                List<OReleaseOrderDO> releaseOrders) {
        List<Index.Order> orders = Optional.ofNullable(releaseOrders)
                .orElse(List.of())
                .stream()
                .map(o -> {
                    return Index.Order.builder()
                            .version(Long.parseLong(o.getReleaseVersion()))
                            .grayRatio(o.getGrayRatio())
                            .build();
                }).toList();

        if (CollectionUtils.isEmpty(orders)) {
            return null;
        }

        Index.GrayConfig grayConfig = Optional.ofNullable((Index.GrayConfig) buildConfigFromContentResource(type2ContentResource, ConfigType.GRAY)).orElse(new Index.GrayConfig());
        grayConfig.setOrders(orders);
        return grayConfig;
    }

    private Map<String, Map<ConfigType, NamespaceVersionContentResource>> getNamespaceVersionContentResources(List<String> namespaceIds) {
        List<ONamespaceVersionResourceDO> contents = namespaceVersionResourceDAO.lambdaQuery()
                .in(ONamespaceVersionResourceDO::getNamespaceId, namespaceIds)
                .eq(ONamespaceVersionResourceDO::getIsAvailable, Available.y)
                .list();

        // 第一个命名空间第一次创建发布单后没有对应的资源
        if (CollectionUtils.isEmpty(contents)) {
            return Collections.emptyMap();
        }

        List<String> resourceIds = contents.stream()
                .map(ONamespaceVersionResourceDO::getResourceId)
                .collect(Collectors.toList());

        var id2Resource = resourceDAO.lambdaQuery()
                .in(OResourceDO::getResourceId, resourceIds)
                .list()
                .stream()
                .collect(Collectors.toMap(OResourceDO::getResourceId, Function.identity()));

        return contents.stream().collect(Collectors.groupingBy(ONamespaceVersionResourceDO::getNamespaceId,
                Collectors.toMap(ONamespaceVersionResourceDO::getType,
                        c -> new NamespaceVersionContentResource(c, id2Resource.get(c.getResourceId())))));
    }

    /**
     * 获取需要下线的命名空间名称
     *
     * @param appKey
     * @param changeVersionRange
     * @return
     */
    private Pair<List<NamespaceIdNameRecord>, List<String>> getOnlineNamespaceRecordsAndOfflineNames(String appKey,
                                                                                                    Pair<String, String> changeVersionRange) {
        // 在线命名空间版本（可用）
        List<ONamespaceVersionDO> availableNamespaceVersions = namespaceVersionDAO
                .getAvailableNamespaceVersionsByAppKey(appKey, changeVersionRange);

        List<String> onlineNamespaceIds = availableNamespaceVersions.stream()
                .map(ONamespaceVersionDO::getNamespaceId)
                .distinct()
                .toList();

        // 需要下线的命名空间ID（基于删除变更）
        List<String> offlineNamespaceIds = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .gt(ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getLeft())
                .le(ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getRight())
                .eq(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.DELETE_NAMESPACE)
                .list()
                .stream()
                .map(ONamespaceVersionDO::getNamespaceId)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(onlineNamespaceIds) && CollectionUtils.isEmpty(offlineNamespaceIds)) {
            return Pair.of(Collections.emptyList(), Collections.emptyList());
        }

        Set<String> unionIds = new HashSet<>();
        unionIds.addAll(onlineNamespaceIds);
        unionIds.addAll(offlineNamespaceIds);

        Map<String, String> id2Name = namespaceDAO.lambdaQuery()
                .select(ONamespaceDO::getNamespaceId, ONamespaceDO::getName)
                .in(ONamespaceDO::getNamespaceId, unionIds)
                .list()
                .stream()
                .collect(Collectors.toMap(ONamespaceDO::getNamespaceId, ONamespaceDO::getName));

        List<NamespaceIdNameRecord> onlineRecords = onlineNamespaceIds.stream()
                .map(id -> new NamespaceIdNameRecord(id, id2Name.get(id)))
                .toList();
        List<String> offlineNames = offlineNamespaceIds.stream()
                .map(id2Name::get)
                .filter(Objects::nonNull)
                .toList();

        return Pair.of(onlineRecords, offlineNames);
    }

    /**
     * 获取 namespace Id 到 NamespaceIdNamePairBO 的映射
     *
     * @param namespaceIds 命名空间ID列表
     * @return namespace Id 到 NamespaceIdNamePairBO 的映射
     */
    private Map<String, NamespaceIdNameRecord> getNamespaceId2IdNamePair(List<String> namespaceIds) {
        return namespaceDAO.lambdaQuery()
                .select(ONamespaceDO::getNamespaceId, ONamespaceDO::getName)
                .in(ONamespaceDO::getNamespaceId, namespaceIds)
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .list()
                .stream()
                .collect(Collectors.toMap(ONamespaceDO::getNamespaceId,
                        n -> new NamespaceIdNameRecord(n.getNamespaceId(), n.getName())));
    }

    /**
     * 根据时间间隔拆分命名空间版本
     *
     * @param now
     * @param namespaceVersions
     * @param timeGaps
     * @return
     */
    protected static List<String> groupNamespaceVersionsByTimeGaps(long now, List<ONamespaceVersionDO> namespaceVersions, List<Integer> timeGaps) {
        // 变更小于等于 5 则不做差量（和老 orange 保持一致）
        if (namespaceVersions == null || namespaceVersions.size() <= 5 || timeGaps == null || timeGaps.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用 LinkedHashSet 去重且保留插入顺序，保证结果顺序稳定（新到旧）
        Set<String> result = new LinkedHashSet<>(timeGaps.size());

        List<Long> leftValues = timeGaps.stream()
                .sorted()
                .map(timeGap -> now - (timeGap * 60 * 1000L))
                .toList();

        List<String> orderedNamespaceChangeVersions = namespaceVersions.stream()
                .map(ONamespaceVersionDO::getNamespaceChangeVersion)
                .sorted()
                .toList()
                .reversed();

        // 预计算每个 changeVersion 对应的时间戳，避免在循环中重复解析
        List<Long> orderedChangeTimes = orderedNamespaceChangeVersions.stream()
                .map(v -> SerializeUtil.parseGenerationTime(Long.parseLong(v)).getTime())
                .toList();

        int i = 0;
        for (Long leftValue : leftValues) {
            for (; i < orderedNamespaceChangeVersions.size() - 1; i++) {
                long largeTime = orderedChangeTimes.get(i);
                long smallTime = orderedChangeTimes.get(i + 1);

                if (largeTime <= leftValue) {
                    result.add(orderedNamespaceChangeVersions.get(i));
                    break;
                }
                if (largeTime > leftValue && smallTime <= leftValue) {
                    result.add(orderedNamespaceChangeVersions.get(i + 1));
                    i++;
                    break;
                }
            }
        }

        return new ArrayList<>(result);
    }

    record NamespaceVersionContentResource(ONamespaceVersionResourceDO content, OResourceDO resource) {
    }

    private Index.Config buildConfigFromContentResource(Map<ConfigType, NamespaceVersionContentResource> type2ContentResource, ConfigType configType) {
        if (type2ContentResource == null || type2ContentResource.get(configType) == null) {
            return null;
        }

        NamespaceVersionContentResource resourceDetail = type2ContentResource.get(configType);

        Index.Config config = ConfigType.GRAY.equals(configType) ? new Index.GrayConfig() : new Index.Config();
        config.setResourceId(resourceDetail.resource.getResourceId());
        config.setVersion(Long.parseLong(resourceDetail.content.getNamespaceChangeVersion()));
        // 基于 Base64 编码长度推导原始大小：(len - padding) * 3 / 4
        String encoded = resourceDetail.resource.getData();
        long padding = 0;
        if (encoded != null && !encoded.isEmpty()) {
            if (encoded.endsWith("==")) {
                padding = 2;
            } else if (encoded.endsWith("=")) {
                padding = 1;
            }
            long decodedSize = Math.max(0L, (encoded.length() - padding) * 3L / 4);
            config.setResourceSize(decodedSize);
        } else {
            config.setResourceSize(0L);
        }
        return config;
    }
}