package com.taobao.wireless.orange.publish.config.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class Expression {
    /**
     * 表达式的键，用于匹配环境变量或上下文
     * 示例值: "appVersion"
     */
    @JSONField(ordinal = 1)
    private String key;
    /**
     * 逻辑运算符，指定表达式的运算类型
     * 示例值: "AND", ">", "="
     */
    @JSONField(ordinal = 2)
    private String operator;

    /**
     * 表达式的值，与键一起确定条件是否满足
     * 示例值: "1.0.1"
     */
    @JSONField(ordinal = 3)
    private String value;

    /**
     * 子表达式列表，用于组成复杂的条件逻辑
     */
    @JSONField(ordinal = 4)
    private List<Expression> children;
}
