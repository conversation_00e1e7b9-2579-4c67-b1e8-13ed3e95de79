package com.taobao.wireless.orange.publish.config;

import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import com.taobao.wireless.orange.publish.config.model.Condition;
import com.taobao.wireless.orange.publish.config.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.publish.config.model.Parameter;
import com.taobao.wireless.orange.publish.config.model.ReleaseConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class IncrementalReleaseConfigGenerator extends AbstractConfigGenerator<ReleaseConfig> {

    @Override
    protected ResourceType getResourceType() {
        return ResourceType.INCREMENTAL_RELEASE_CONFIG;
    }

    @Override
    protected List<String> getReleaseVersions(String namespaceId, String baseVersion) {
        if (baseVersion == null) {
            return Collections.emptyList();
        }

        return namespaceVersionDAO.lambdaQuery()
                .select(ONamespaceVersionDO::getReleaseVersion)
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .gt(ONamespaceVersionDO::getNamespaceChangeVersion, baseVersion)
                .eq(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.FINISH_IMPACT_RELEASE)
                .list()
                .stream()
                .map(ONamespaceVersionDO::getReleaseVersion)
                .collect(Collectors.toList());
    }

    @Override
    protected List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> releaseVersions) {
        if (CollectionUtils.isEmpty(releaseVersions)) {
            return Collections.emptyList();
        }

        return parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .in(OParameterVersionDO::getReleaseVersion, releaseVersions)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .list();
    }

    @Override
    protected ReleaseConfig buildResult(NamespaceIdNameRecord namespace,
                                        List<Parameter> updateParameters,
                                        List<OParameterVersionDO> offlineParameterDOs,
                                        List<Condition> conditions) {

        List<String> offlineParameterKeys = offlineParameterDOs.stream()
                .map(OParameterVersionDO::getParameterKey)
                .collect(Collectors.toList());

        return ReleaseConfig.builder()
                .schemaVersion("1.0")
                .namespace(namespace.name())
                .strategy(ConfigStrategy.INCREMENTAL)
                .type(ConfigType.RELEASE)
                .conditions(conditions)
                .parameters(updateParameters != null ? updateParameters : Collections.emptyList())
                .offlineParameters(CollectionUtils.isNotEmpty(offlineParameterKeys) ? offlineParameterKeys : null)
                .build();
    }

    @Override
    protected List<VersionStatus> getVersionStatuses() {
        return List.of(VersionStatus.RELEASED);
    }
}
