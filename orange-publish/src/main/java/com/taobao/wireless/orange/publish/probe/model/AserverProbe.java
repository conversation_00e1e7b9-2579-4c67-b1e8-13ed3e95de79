package com.taobao.wireless.orange.publish.probe.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class AserverProbe {
    /**
     * 应用标识，用于鉴权和识别应用
     * 是否可选：否
     * 示例值：21380790
     */
    private String appkey;

    /**
     * 索引类型，指示配置的索引类别
     * 是否可选：否
     * 示例值：dp
     */
    private String indexType;

    /**
     * CDN 主机域名，用于资源下载
     * 是否可选：否
     * 示例值：dorangesource.alicdn.com
     */
    private String host;

    /**
     * 协议类型，指示访问协议
     * 是否可选：否
     * 示例值：https
     */
    private String protocol;

    /**
     * 探测版本号，用于版本控制和更新判断
     * 是否可选：否
     * 示例值：1120250519203202012
     */
    private String probeVersion;

    /**
     * 版本列表，包含具体的版本配置信息
     * 是否可选：否
     */
    private List<Version> versions;

    /**
     * 版本配置内部类
     * 包含基础版本、资源ID和MD5校验值
     */
    @Data
    public static class Version {

        /**
         * 基础版本号，用于版本依赖和增量更新
         * 是否可选：否
         * 示例值：1120250519193601077
         */
        private String baseVersion;

        /**
         * 资源标识符，指向具体的配置资源文件
         * 是否可选：否
         * 示例值：im817459686e834266b563deb303bf7dcb-1.json
         */
        private String resourceId;

        /**
         * 资源文件的MD5校验值，用于完整性验证
         * 是否可选：否
         * 示例值：0046fc9af26f548459142563e7db5fd9
         */
        private String md5;
    }

    public boolean equals(AserverProbe other) {
        if (other == null) {
            return false;
        }
        if (this == other) {
            return true;
        }

        return Objects.equals(this.getAppkey(), other.getAppkey()) &&
                Objects.equals(this.getIndexType(), other.getIndexType()) &&
                Objects.equals(this.getHost(), other.getHost()) &&
                Objects.equals(this.getProtocol(), other.getProtocol()) &&
                Objects.equals(this.getProbeVersion(), other.getProbeVersion()) &&
                equals(this.getVersions(), other.getVersions());
    }

    private boolean equals(List<Version> versions1, List<Version> versions2) {
        if (versions1 == null && versions2 == null) {
            return true;
        }
        if (versions1 == null || versions2 == null) {
            return false;
        }
        if (versions1.size() != versions2.size()) {
            return false;
        }
        List<Version> sortedVersions1 = versions1.stream().sorted(Comparator.comparing(Version::getBaseVersion)).toList();
        List<Version> sortedVersions2 = versions2.stream().sorted(Comparator.comparing(Version::getBaseVersion)).toList();
        for (int i = 0; i < sortedVersions1.size(); i++) {
            if (!Objects.equals(sortedVersions1.get(i), sortedVersions2.get(i))) {
                return false;
            }
        }
        return true;
    }
}
