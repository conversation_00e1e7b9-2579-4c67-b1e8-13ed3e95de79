package com.taobao.wireless.orange.publish.config;

import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.SerializableObject;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceDAO;
import com.taobao.wireless.orange.publish.config.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.publish.resource.ResourceService;
import com.taobao.wireless.orange.publish.resource.model.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ConfigGenerateService {

    @Autowired
    private ResourceService resourceService;
    @Autowired
    private ONamespaceDAO namespaceDAO;

    private final Map<ResourceType, AbstractConfigGenerator<?>> configGeneratorMap;

    @Autowired
    public ConfigGenerateService(List<AbstractConfigGenerator<?>> configGenerators) {
        this.configGeneratorMap = configGenerators.stream()
                .collect(Collectors.toMap(
                        AbstractConfigGenerator::getResourceType,
                        Function.identity()
                ));
    }

    public Resource generate(String namespaceId, ResourceType resourceType, String baseVersion) {
        NamespaceIdNameRecord namespace = getNamespaceRecord(namespaceId);
        AbstractConfigGenerator<?> generator = configGeneratorMap.get(resourceType);
        if (generator == null) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID);
        }
        SerializableObject config = generator.generate(namespace, baseVersion);
        if (config == null) {
            return null;
        }
        return resourceService.create(config, resourceType);
    }

    private NamespaceIdNameRecord getNamespaceRecord(String namespaceId) {
        return Optional.ofNullable(namespaceDAO.getByNamespaceId(namespaceId))
                .map(n ->
                        new NamespaceIdNameRecord(n.getNamespaceId(), n.getName()))
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
    }
}
