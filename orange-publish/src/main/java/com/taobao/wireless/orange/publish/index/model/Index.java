package com.taobao.wireless.orange.publish.index.model;


import com.taobao.wireless.orange.common.constant.enums.ConfigStrategy;
import com.taobao.wireless.orange.common.model.proto.*;
import com.taobao.wireless.orange.common.model.SerializableObject;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Index implements SerializableObject {
    /**
     * 索引协议版本，用于指导如何解析和消费配置文件，方便后续协议扩展和升级
     * 是否可选：否
     * 示例值：1.0
     */
    private String schemaVersion;

    /**
     * CDN 域名，用于资源下载
     * 是否可选：否
     * 示例值：dorangesource.alicdn.com
     */
    private String cdn;

    /**
     * 应用标识，用于鉴权
     * 是否可选：否
     * 示例值：21380790
     */
    private String appKey;

    /**
     * 索引版本号，用户更新判断
     * 是否可选：否
     * 示例值：1620241009140003886
     */
    private long version;

    /**
     * 基础版本号，"0"代表全量索引，其他代表差量索引
     * 是否可选：否
     * 示例值：dsaf321
     */
    private long baseVersion;

    /**
     * ER合并策略
     */
    private ComboPolicy comboPolicy;

    /**
     * 在一定阈值内需要下线的命名空间，仅适用于差量配置
     * 是否可选：是
     * 示例值：[]
     */
    private List<String> offlineNamespaces;

    /**
     * 配置发布策略，FULL/INCREMENTAL
     * 是否可选：否
     * 示例值：FULL
     */
    private ConfigStrategy strategy;

    /**
     * 命名空间数组，包含不同类型的配置数据
     * 是否可选：否
     * 类型：Array
     */
    private List<Namespace> namespaces;

    @Data
    public static class ComboPolicy {

        /**
         * 是否启用ER合并请求，用于降级
         */
        private boolean enable;

        /**
         * ER合并请求的URL
         */
        private String url;

        /**
         * 最小打包文件个数，小于minComboCount个请求不需要走ER合并
         */
        private int minComboCount;

        /**
         * 一次请求的文件不能超过该大小
         */
        private int maxComboCount;

        /**
         * 推荐一批文件的总大小，单位B
         */
        private long batchSize;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Namespace {
        /**
         * 命名空间名称
         * 是否可选：否
         * 示例值：network_harmony
         */
        private String name;

        /**
         * namespace 变更版本，如果版本大于本地版本则需要触发百分比计算
         * 是否可选：否
         * 示例值：1620241009140003886
         */
        private long changeVersion;

        /**
         * 正式发布配置，包括资源地址和 MD5 值
         * 是否可选：否
         */
        private Config release;

        /**
         * 灰度发布配置，包括资源地址和 MD5 值及发布单
         * 是否可选：是
         */
        private GrayConfig gray;

        /**
         * 实验配置，包括资源地址和 MD5 值
         * 是否可选：是
         */
        private Config experiment;
    }

    @Data
    public static class Config {
        /**
         * 资源地址
         * 是否可选：否
         * 示例值：nca82969ff08a84efeac17d6df181e26a0.json / rlt82969ff08a84sfeac17dwdf181e25a1.json
         */
        private String resourceId;

        /**
         * namespace 配置正式版本
         * 是否可选：否
         */
        private long version;

        /**
         * 资源大小
         */
        private long resourceSize;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class GrayConfig extends Config {
        /**
         * 发布单数组，用于灰度控制
         * 是否可选：否
         */
        private List<Order> orders;
    }

    @Data
    @Builder
    public static class Order {
        /**
         * 发布单版本，用于灰度打散设备
         * 是否可选：否
         * 示例值：433
         */
        private long version;

        /**
         * 灰度百分比(十万分之一为单位)，用于控制灰度覆盖
         * 是否可选：是
         * 示例值：100
         */
        private Integer grayRatio;
    }

    @Override
    public byte[] serialize() {
        IndexProto.Builder builder = IndexProto.newBuilder()
                .setSchemaVersion(this.schemaVersion)
                .setCdn(this.cdn)
                .setAppKey(this.appKey)
                .setVersion(this.version)
                .setBaseVersion(this.baseVersion)
                .setStrategy(convertConfigStrategy(this.strategy));

        if (this.comboPolicy != null) {
            builder.setComboPolicy(convertComboPolicy(this.comboPolicy));
        }

        if (this.offlineNamespaces != null) {
            builder.addAllOfflineNamespaces(this.offlineNamespaces);
        }

        if (this.namespaces != null) {
            List<NamespaceProto> namespaceProtos = this.namespaces.stream()
                    .map(this::convertNamespace)
                    .collect(Collectors.toList());
            builder.addAllNamespaces(namespaceProtos);
        }

        return builder.build().toByteArray();
    }

    private ConfigStrategyProto convertConfigStrategy(ConfigStrategy configStrategy) {
        return ConfigStrategyProto.valueOf(configStrategy.name());
    }

    private NamespaceProto convertNamespace(Namespace namespace) {
        NamespaceProto.Builder builder = NamespaceProto.newBuilder()
                .setName(namespace.getName())
                .setChangeVersion(namespace.getChangeVersion());

        if (namespace.getRelease() != null) {
            builder.setRelease(convertConfig(namespace.getRelease()));
        }

        if (namespace.getGray() != null) {
            builder.setGray(convertGrayConfig(namespace.getGray()));
        }

        return builder.build();
    }

    private ConfigProto convertConfig(Config config) {
        return ConfigProto.newBuilder()
                .setResourceId(config.getResourceId())
                .setVersion(config.getVersion())
                .setResourceSize(config.getResourceSize())
                .build();
    }

    private ConfigWithOrdersProto convertGrayConfig(GrayConfig grayConfig) {
        ConfigWithOrdersProto.Builder builder = ConfigWithOrdersProto.newBuilder();

        if (StringUtils.isNotBlank(grayConfig.getResourceId())) {
            builder.setResourceId(grayConfig.getResourceId());
            builder.setVersion(grayConfig.getVersion());
            builder.setResourceSize(grayConfig.getResourceSize());
        }

        if (grayConfig.getOrders() != null) {
            List<OrderProto> orderProtos = grayConfig.getOrders().stream()
                    .map(this::convertOrder)
                    .collect(Collectors.toList());
            builder.addAllOrders(orderProtos);
        }
        return builder.build();
    }

    private OrderProto convertOrder(Order order) {
        OrderProto.Builder builder = OrderProto.newBuilder()
                .setVersion(order.getVersion());

        if (order.getGrayRatio() != null) {
            builder.setGrayRatio(order.getGrayRatio());
        }

        return builder.build();
    }

    private ComboPolicyProto convertComboPolicy(ComboPolicy comboPolicy) {
        return ComboPolicyProto.newBuilder()
                .setEnable(comboPolicy.isEnable())
                .setUrl(comboPolicy.getUrl())
                .setMinComboCount(comboPolicy.getMinComboCount())
                .setMaxComboCount(comboPolicy.getMaxComboCount())
                .setBatchSize(comboPolicy.getBatchSize())
                .build();
    }
}