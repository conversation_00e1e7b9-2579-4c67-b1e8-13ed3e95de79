package com.taobao.wireless.orange.publish.config.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Parameter {
    /**
     * 参数标识符，描述配置项的唯一名称
     * 必填字段
     * 示例值: "user_config"
     */
    @JSONField(ordinal= 1)
    private String key;

    /**
     * 参数值类型，描述参数的值如何存储和解释
     * 必填字段
     * 示例值: "json"
     */
    @JSONField(ordinal= 2)
    private String valueType;

    /**
     * 参数版本号
     * 必填字段
     * 示例值: "0212025030712133421"
     */
    @JSONField(ordinal= 3)
    private long version;

    /**
     * 参数默认值，不满足任何条件时使用的值
     * 必填字段
     * 示例值: {"limit":1}
     */
    @JSONField(ordinal= 4)
    private Object defaultValue;

    /**
     * 条件值数组，根据条件应用不同的配置
     * 必填字段
     */
    @JSONField(ordinal= 5)
    private List<ConditionalValue> conditionalValues;
}
