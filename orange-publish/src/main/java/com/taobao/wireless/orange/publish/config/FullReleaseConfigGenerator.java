package com.taobao.wireless.orange.publish.config;

import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import com.taobao.wireless.orange.publish.config.model.Condition;
import com.taobao.wireless.orange.publish.config.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.publish.config.model.Parameter;
import com.taobao.wireless.orange.publish.config.model.ReleaseConfig;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 全量发布配置生成器
 * 负责生成包含所有已发布参数的完整配置
 */
@Component
public class FullReleaseConfigGenerator extends AbstractConfigGenerator<ReleaseConfig> {

    @Override
    protected ResourceType getResourceType() {
        return ResourceType.FULL_RELEASE_CONFIG;
    }

    /**
     * 获取发布版本列表
     * 全量发布模式不需要版本过滤，返回空字符串列表
     *
     * @return 发布版本列表
     */
    @Override
    protected List<String> getReleaseVersions(String namespaceId, String baseVersion) {
        // 全量模式不需要版本过滤，返回空列表
        return Collections.emptyList();
    }

    /**
     * 获取参数版本列表
     * 获取所有已发布状态的参数
     *
     * @param notUsed 发布版本列表
     * @return 参数版本列表
     */
    @Override
    protected List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> notUsed) {
        return parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .ne(OParameterVersionDO::getChangeType, ChangeType.DELETE)
                .list();
    }

    /**
     * 构建结果配置
     * 生成包含所有条件和参数的完整配置
     *
     * @param namespace           命名空间信息
     * @param updateParameters    参数列表
     * @param offlineParameterDOs 下线参数列表
     * @param conditions        条件列表
     * @return 完整的发布配置
     */
    @Override
    protected ReleaseConfig buildResult(NamespaceIdNameRecord namespace,
                                        List<Parameter> updateParameters,
                                        List<OParameterVersionDO> offlineParameterDOs,
                                        List<Condition> conditions) {
        return ReleaseConfig.builder()
                .schemaVersion("1.0")
                .namespace(namespace.name())
                .strategy(ConfigStrategy.FULL)
                .type(ConfigType.RELEASE)
                .conditions(conditions)
                .parameters(updateParameters)
                // 全量模式下，下线参数列表为空
                .offlineParameters(null)
                .build();
    }

    /**
     * 获取版本状态列表
     * 全量发布只关注已发布状态的配置
     *
     * @return 版本状态列表
     */
    @Override
    protected List<VersionStatus> getVersionStatuses() {
        return List.of(VersionStatus.RELEASED);
    }
}
