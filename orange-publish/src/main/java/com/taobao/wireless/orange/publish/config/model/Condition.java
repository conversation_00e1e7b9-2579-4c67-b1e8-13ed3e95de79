package com.taobao.wireless.orange.publish.config.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class Condition {
    /**
     * 条件标识符，唯一标识一个条件
     * 示例值：HuaWei001
     */
    @JSONField(ordinal= 1)
    private String id;

    /**
     * 条件表达式，用于描述满足何种条件
     * 示例值：{"operator":"AND","children":[{"key":"brand","operator":"=","value":"Huawei"}]}
     */
    @JSONField(ordinal= 2)
    private Expression expression;
}
