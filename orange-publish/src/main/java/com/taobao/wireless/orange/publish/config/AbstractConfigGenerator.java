package com.taobao.wireless.orange.publish.config;

import com.alibaba.fastjson.JSON;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.ParameterValueType;
import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.SerializableObject;
import com.taobao.wireless.orange.oswitch.dal.dao.*;
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import com.taobao.wireless.orange.publish.config.model.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.CONDITION_SEPARATOR;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;

/**
 * 配置生成器抽象类，使用模板方法模式实现配置生成的通用流程
 *
 * @param <T> 生成的配置类型
 */
public abstract class AbstractConfigGenerator<T extends SerializableObject> {
    @Autowired
    protected OConditionVersionDAO conditionVersionDAO;

    @Autowired
    protected OParameterVersionDAO parameterVersionDAO;

    @Autowired
    protected OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    protected ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    protected OReleaseOrderDAO releaseOrderDAO;

    /**
     * 配置生成的核心流程
     *
     * @param namespace   命名空间
     * @param baseVersion 发布版本号（可选）
     * @return 生成的配置对象
     */
    public final T generate(NamespaceIdNameRecord namespace, String baseVersion) {
        // 获取发布版本列表
        List<String> releaseVersions = getReleaseVersions(namespace.namespaceId(), baseVersion);

        // 获取参数版本列表
        List<OParameterVersionDO> parameterVersions = getParameterVersions(namespace.namespaceId(), releaseVersions);

        if (CollectionUtils.isEmpty(parameterVersions)) {
            return null;
        }

        // 分组参数（待更新/待下线）
        ParameterGroup group = groupParameters(parameterVersions);

        // 构建参数列表
        @SuppressWarnings("unchecked")
        List<Parameter> updateParameters = buildParametersWithConditions((List<OParameterVersionDO>) group.updateParameters());
        @SuppressWarnings("unchecked")
        List<OParameterVersionDO> offlineParameters = (List<OParameterVersionDO>) group.offlineParameters();

        // 构建参数关联条件列表
        @SuppressWarnings("unchecked")
        List<Condition> relatedConditions = buildConditions((List<OParameterVersionDO>) group.updateParameters());

        // 构建最终结果
        return buildResult(namespace, updateParameters, offlineParameters, relatedConditions);
    }

    protected abstract ResourceType getResourceType();

    /**
     * 获取发布版本列表
     *
     * @param baseVersion 基础版本号
     * @return 发布版本列表
     */
    protected abstract List<String> getReleaseVersions(String namespaceId, String baseVersion);

    /**
     * 获取参数版本列表
     *
     * @param releaseVersions 发布版本列表
     * @return 参数版本列表
     */
    protected abstract List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> releaseVersions);

    /**
     * 构建最终结果
     *
     * @param namespace           命名空间信息
     * @param updateParameters    参数列表
     * @param offlineParameterDOs 下线参数列表
     * @param conditions          条件映射
     * @return 配置结果
     */
    protected abstract T buildResult(NamespaceIdNameRecord namespace,
                                     List<Parameter> updateParameters,
                                     List<OParameterVersionDO> offlineParameterDOs,
                                     List<Condition> conditions);

    /**
     * 获取需要查询的版本记录状态列表（灰度的需要查询 INIT 和 RELEASED，正式的仅需查询 RELEASED）
     *
     * @return 状态列表
     */
    protected abstract List<VersionStatus> getVersionStatuses();

    /**
     * 构建参数列表
     *
     * @param updateParameters 在线参数列表
     * @return 参数列表
     */
    private List<Parameter> buildParametersWithConditions(List<OParameterVersionDO> updateParameters) {
        if (CollectionUtils.isEmpty(updateParameters)) {
            return Collections.emptyList();
        }

        // 提取参数ID
        List<String> parameterIds = updateParameters.stream()
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());

        // 获取参数条件映射
        var paramId2paramCondition = getExistParameterConditions(parameterIds);

        // 构建并过滤参数列表
        return updateParameters.stream()
                .map(p -> buildParameter(p, paramId2paramCondition.getOrDefault(p.getParameterId(), Collections.emptyList())))
                .filter(Objects::nonNull)
                // 客户端希望按照 key 排序，加速检索
                .sorted(Comparator.comparing(Parameter::getKey))
                .collect(Collectors.toList());
    }

    /**
     * 获取参数条件映射
     *
     * @param parameterIds 参数ID列表
     * @return 参数ID到条件列表的映射
     */
    private Map<String, List<OParameterConditionVersionDO>> getExistParameterConditions(List<String> parameterIds) {
        if (CollectionUtils.isEmpty(parameterIds)) {
            return Collections.emptyMap();
        }

        // 查询参数条件版本
        return parameterConditionVersionDAO.lambdaQuery()
                .in(OParameterConditionVersionDO::getParameterId, parameterIds)
                .in(OParameterConditionVersionDO::getStatus, getVersionStatuses())
                .ne(OParameterConditionVersionDO::getChangeType, ChangeType.DELETE)
                .list()
                .stream()
                .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId));
    }

    /**
     * 提取条件ID列表
     *
     * @param updateParameters 在线参数列表
     * @return 条件ID列表
     */
    private List<String> extractConditionIds(List<OParameterVersionDO> updateParameters) {
        if (CollectionUtils.isEmpty(updateParameters)) {
            return Collections.emptyList();
        }

        return updateParameters.stream()
                .filter(p -> StringUtils.isNotBlank(p.getConditionsOrder()))
                .flatMap(p -> Arrays.stream(p.getConditionsOrder().split(CONDITION_SEPARATOR)))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取参数关联条件版本列表
     *
     * @return 条件列表
     */
    private List<Condition> buildConditions(List<OParameterVersionDO> updateParameters) {
        if (CollectionUtils.isEmpty(updateParameters)) {
            return Collections.emptyList();
        }

        List<String> conditionIds = extractConditionIds(updateParameters);
        if (CollectionUtils.isEmpty(conditionIds)) {
            return Collections.emptyList();
        }

        // 查询条件版本
        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .in(OConditionVersionDO::getStatus, getVersionStatuses())
                .ne(OConditionVersionDO::getChangeType, ChangeType.DELETE)
                .orderByAsc(OConditionVersionDO::getConditionId)
                .list();

        // 转换为映射并处理冲突（优先使用 INIT 状态的版本）
        return conditions.stream()
                .collect(Collectors.toMap(
                        OConditionVersionDO::getConditionId,
                        Function.identity(),
                        // 有冲突只有可能是一个是 INIT 一个是 RELEASED，优先用 INIT
                        (v1, v2) -> VersionStatus.INIT.equals(v1.getStatus()) ? v1 : v2))
                .values()
                .stream()
                .map(c -> Condition.builder()
                        .id(c.getConditionId())
                        .expression(JSON.parseObject(c.getExpression(), Expression.class))
                        .build())
                // 端上依赖排序，加速检索
                .sorted(Comparator.comparing(Condition::getId))
                .collect(Collectors.toList());
    }

    /**
     * 构建参数对象
     *
     * @param parameter         参数版本
     * @param conditionVersions 参数条件版本列表
     * @return 参数配置
     */
    private Parameter buildParameter(OParameterVersionDO parameter,
                                     List<OParameterConditionVersionDO> conditionVersions) {
        if (parameter == null || CollectionUtils.isEmpty(conditionVersions)) {
            return null;
        }

        // 构建条件值映射（条件ID -> 参数条件版本）
        Map<String, OParameterConditionVersionDO> conditionIdToValue = buildConditionValueMap(conditionVersions);

        // 检查默认条件是否存在
        if (!conditionIdToValue.containsKey(DEFAULT_CONDITION_ID)) {
            throw CommonException.getDynamicException(ExceptionEnum.PARAM_INVALID, "参数【" + parameter.getParameterKey() + "】缺少默认条件");
        }

        // 构建参数条件值列表
        List<ConditionalValue> conditionalValues = buildConditionalValues(parameter.getConditionsOrder(), conditionIdToValue, parameter.getValueType());

        // 构建参数
        return Parameter.builder()
                .key(parameter.getParameterKey())
                .version(Long.parseLong(parameter.getReleaseVersion()))
                .valueType(parameter.getValueType().name().toLowerCase())
                .defaultValue(getValueObj(conditionIdToValue.get(DEFAULT_CONDITION_ID).getValue(), parameter.getValueType()))
                .conditionalValues(conditionalValues)
                .build();
    }

    protected Object getValueObj(String value, ParameterValueType valueType) {
        if (value == null) {
            return null;
        }

        try {
            return switch (valueType) {
                case BOOLEAN -> Boolean.parseBoolean(value);
                case DOUBLE -> Double.parseDouble(value);
                case LONG -> Long.parseLong(value);
                default -> value; // STRING, JSON
            };
        } catch (NumberFormatException ex) {
            throw CommonException.getDynamicException(
                    ExceptionEnum.PARAM_INVALID,
                    "参数值解析失败，期望类型 " + valueType + "，实际值: " + value
            );
        }
    }

    /**
     * 构建条件值映射
     *
     * @param conditionVersions 条件版本列表
     * @return 条件ID到条件值的映射
     */
    private Map<String, OParameterConditionVersionDO> buildConditionValueMap(List<OParameterConditionVersionDO> conditionVersions) {
        return conditionVersions.stream()
                .collect(Collectors.toMap(
                        OParameterConditionVersionDO::getConditionId,
                        Function.identity(),
                        // 优先使用初始状态（for 灰度配置）
                        (v1, v2) -> VersionStatus.INIT.equals(v1.getStatus()) ? v1 : v2
                ));
    }

    /**
     * 构建参数条件值列表
     *
     * @param conditionsOrder    条件顺序字符串
     * @param conditionIdToValue 条件ID到条件值的映射
     * @return 条件值列表
     */
    private List<ConditionalValue> buildConditionalValues(String conditionsOrder,
                                                          Map<String, OParameterConditionVersionDO> conditionIdToValue, ParameterValueType valueType) {
        if (StringUtils.isBlank(conditionsOrder)) {
            return Collections.emptyList();
        }

        return Arrays.stream(conditionsOrder.split(CONDITION_SEPARATOR))
                .filter(conditionIdToValue::containsKey)
                .filter(conditionId -> !DEFAULT_CONDITION_ID.equals(conditionId))
                .map(conditionId -> ConditionalValue.builder()
                        .conditionId(conditionId)
                        .value(getValueObj(conditionIdToValue.get(conditionId).getValue(), valueType))
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 将参数按照变更类型分组
     *
     * @param parameterVersions 参数版本列表
     * @return 参数分组（上线参数和下线参数）
     */
    private ParameterGroup groupParameters(List<? extends OParameterVersionDO> parameterVersions) {
        // 按照参数变更类型分组：true=下线参数，false=更新参数
        var parameterMap = parameterVersions
                .stream()
                .collect(Collectors.groupingBy(i -> ChangeType.DELETE.equals(i.getChangeType())));

        // 提取下线参数列表
        var offlineParameters = Optional.ofNullable(parameterMap.get(true)).orElse(Collections.emptyList());

        // 获取更新参数列表
        var updateParameters = Optional.ofNullable(parameterMap.get(false)).orElse(Collections.emptyList());

        return new ParameterGroup(updateParameters, offlineParameters);
    }
}
