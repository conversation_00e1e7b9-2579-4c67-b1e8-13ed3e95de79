package com.taobao.wireless.orange.publish.config;

import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.publish.config.model.Condition;
import com.taobao.wireless.orange.publish.config.model.GrayConfig;
import com.taobao.wireless.orange.publish.config.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.publish.config.model.Parameter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 灰度配置生成器
 * 负责生成包含灰度发布单信息的配置，用于客户端灰度控制
 *
 * <AUTHOR>
 */
@Component
public class GrayConfigGenerator extends AbstractConfigGenerator<GrayConfig> {
    @Override
    protected ResourceType getResourceType() {
        return ResourceType.FULL_GRAY_CONFIG;
    }

    /**
     * 获取灰度发布版本列表
     * 查询所有未完成且已设置灰度百分比的发布单版本
     *
     * @param namespaceId 命名空间ID
     * @param baseVersion 基础版本号（灰度场景下不使用）
     * @return 发布版本列表
     */
    @Override
    protected List<String> getReleaseVersions(String namespaceId, String baseVersion) {
        return releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getNamespaceId, namespaceId)
                .eq(OReleaseOrderDO::getStatus, ReleaseOrderStatus.IN_PROGRESS)
                .isNotNull(OReleaseOrderDO::getGrayRatio)
                .list()
                .stream()
                .map(OReleaseOrderDO::getReleaseVersion)
                .collect(Collectors.toList());
    }

    /**
     * 获取灰度参数版本列表
     * 查询指定发布版本下状态为INIT的参数版本
     *
     * @param namespaceId     命名空间ID
     * @param releaseVersions 发布版本列表
     * @return 参数版本列表
     */
    @Override
    protected List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> releaseVersions) {
        if (CollectionUtils.isEmpty(releaseVersions)) {
            return Collections.emptyList();
        }
        return parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .in(OParameterVersionDO::getReleaseVersion, releaseVersions)
                .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                .list();
    }

    /**
     * 构建灰度配置结果
     * 将条件映射和发布单信息组装成最终的灰度配置对象
     *
     * @param namespace           命名空间信息
     * @param updateParameters    待更新参数列表
     * @param offlineParameterDOs 待下线参数列表
     * @param conditions          条件列表
     * @return 灰度配置对象
     */
    @Override
    protected GrayConfig buildResult(NamespaceIdNameRecord namespace,
                                     List<Parameter> updateParameters,
                                     List<OParameterVersionDO> offlineParameterDOs,
                                     List<Condition> conditions) {
        List<GrayConfig.ReleaseOrder> orders = buildGrayReleaseOrders(updateParameters, offlineParameterDOs);

        return GrayConfig.builder()
                .schemaVersion("1.0")
                .namespace(namespace.name())
                .strategy(ConfigStrategy.FULL)
                .type(ConfigType.GRAY)
                .conditions(conditions)
                .orders(orders)
                .build();
    }

    /**
     * 构建灰度发布单列表
     * 为每个发布版本构建包含参数和下线参数的发布单对象
     *
     * @return 灰度发布单列表
     */
    private List<GrayConfig.ReleaseOrder> buildGrayReleaseOrders(List<Parameter> updateParameters, List<OParameterVersionDO> offlineParameterDOs) {

        var releaseVersion2UpdateParameters = updateParameters.stream().collect(Collectors.groupingBy(Parameter::getVersion));
        var releaseVersion2OfflineParameters = offlineParameterDOs.stream().collect(Collectors.groupingBy(p -> Long.parseLong(p.getReleaseVersion())));

        Set<Long> releaseVersions = new HashSet<>(releaseVersion2UpdateParameters.keySet());
        releaseVersions.addAll(releaseVersion2OfflineParameters.keySet());

        return releaseVersions.stream()
                .map(version -> buildSingleReleaseOrder(version,
                        releaseVersion2UpdateParameters.get(version),
                        releaseVersion2OfflineParameters.get(version)))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个发布单对象
     *
     * @param version             发布版本
     * @param updateParameter     在线参数版本列表
     * @param offlineParameterDOs 下线参数版本列表
     * @return 发布单对象，如果没有参数则返回null
     */
    private GrayConfig.ReleaseOrder buildSingleReleaseOrder(long version,
                                                            List<Parameter> updateParameter,
                                                            List<OParameterVersionDO> offlineParameterDOs
    ) {
        List<String> offlineParameterKeys = CollectionUtils.isEmpty(offlineParameterDOs) ? null : offlineParameterDOs.stream()
                .map(OParameterVersionDO::getParameterKey)
                .toList();

        return GrayConfig.ReleaseOrder.builder()
                .version(version)
                .parameters(updateParameter)
                .offlineParameters(offlineParameterKeys)
                .build();
    }

    /**
     * 获取版本状态列表
     * 灰度配置需要查询INIT和RELEASED状态的版本
     *
     * @return 版本状态列表
     */
    @Override
    protected List<VersionStatus> getVersionStatuses() {
        return Arrays.asList(VersionStatus.RELEASED, VersionStatus.INIT);
    }
}
