package com.taobao.wireless.orange.web.interceptor;

import com.taobao.wireless.orange.common.constant.enums.NamespaceType;
import com.taobao.wireless.orange.common.thread.OThreadContext;
import com.taobao.wireless.orange.common.thread.OThreadContextHolder;
import com.taobao.wireless.orange.oswitch.manager.namespace.UserNamespaceManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 用户namespace使用记录切面
 * 记录用户访问namespace的频次，用于统计常用namespace
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order(20)
@Slf4j
public class NamespaceUsageAspect {

    @Autowired
    private UserNamespaceManager userNamespaceManager;

    /**
     * namespace相关的Controller方法
     */
    @Pointcut("execution(* com.taobao.wireless.orange.web.controller.NamespaceController.*(..))")
    public void namespaceControllerPointcut() {
    }

    /**
     * parameter相关的Controller方法（因为parameter操作也涉及namespace）
     */
    @Pointcut("execution(* com.taobao.wireless.orange.web.controller.ParameterController.*(..))")
    public void parameterControllerPointcut() {
    }

    /**
     * condition相关的Controller方法（因为condition操作也涉及namespace）
     */
    @Pointcut("execution(* com.taobao.wireless.orange.web.controller.ConditionController.*(..))")
    public void conditionControllerPointcut() {
    }

    /**
     * 记录namespace使用情况
     */
    @AfterReturning("namespaceControllerPointcut() || parameterControllerPointcut() || conditionControllerPointcut()")
    public void recordNamespaceUsage(JoinPoint joinPoint) {
        try {
            // 获取用户信息
            OThreadContext threadContext = OThreadContextHolder.get();
            if (threadContext == null || StringUtils.isBlank(threadContext.getWorkerId())) {
                return;
            }

            String workerId = threadContext.getWorkerId();
            String namespaceId = extractNamespaceId(joinPoint);

            if (StringUtils.isNotBlank(namespaceId)) {
                // 统一记录namespace访问（包括常用和最近访问）
                userNamespaceManager.recordUserNamespaceAccess(NamespaceType.SWITCH, workerId, namespaceId);
            }
        } catch (Exception e) {
            log.warn("Failed to record switch namespace usage", e);
        }
    }

    /**
     * 从方法参数中提取namespaceId
     */
    private String extractNamespaceId(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        Parameter[] parameters = method.getParameters();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();

        // 1. 首先尝试从@PathVariable注解中获取namespaceId
        for (int i = 0; i < parameters.length; i++) {
            for (Annotation annotation : parameterAnnotations[i]) {
                if (annotation instanceof PathVariable) {
                    PathVariable pathVariable = (PathVariable) annotation;
                    String paramName = StringUtils.isNotBlank(pathVariable.value()) ?
                            pathVariable.value() : pathVariable.name();

                    if ("namespaceId".equals(paramName) && args[i] instanceof String) {
                        return (String) args[i];
                    }
                }
            }
        }

        // 2. 尝试从参数名为namespaceId的参数中获取
        for (int i = 0; i < parameters.length; i++) {
            if ("namespaceId".equals(parameters[i].getName()) && args[i] instanceof String) {
                return (String) args[i];
            }
        }

        // 3. 尝试从请求体对象中获取namespaceId字段
        for (Object arg : args) {
            if (arg != null) {
                try {
                    Method getNamespaceIdMethod = arg.getClass().getMethod("getNamespaceId");
                    Object namespaceId = getNamespaceIdMethod.invoke(arg);
                    if (namespaceId instanceof String && StringUtils.isNotBlank((String) namespaceId)) {
                        return (String) namespaceId;
                    }
                } catch (Exception e) {
                    // 忽略反射异常，继续尝试其他方式
                }
            }
        }

        return null;
    }
}
