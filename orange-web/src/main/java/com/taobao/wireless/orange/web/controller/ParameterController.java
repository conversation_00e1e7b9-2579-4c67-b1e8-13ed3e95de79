package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.ParameterService;
import com.taobao.wireless.orange.service.model.ParameterDetailDTO;
import com.taobao.wireless.orange.service.model.ParameterDirectUpdateDTO;
import com.taobao.wireless.orange.service.model.ParameterKeyExistQuery;
import com.taobao.wireless.orange.service.model.ParameterQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "参数管理接口")
@RestController
@RequestMapping("/api/parameters")
public class ParameterController {

    @Autowired
    private ParameterService parameterService;

    @ApiOperation("查询参数列表")
    @GetMapping
    public PaginationResult<ParameterDetailDTO> query(ParameterQueryDTO parameterQueryDTO,
                                                      @RequestParam(defaultValue = "1") Integer page,
                                                      @RequestParam(defaultValue = "10") Integer size) {
        return parameterService.query(parameterQueryDTO, new Pagination(page, size));
    }

    @ApiOperation("更新参数基础信息")
    @PutMapping("/{parameterId}")
    public Result<Void> update(@PathVariable("parameterId") String parameterId,
                               @RequestBody ParameterDirectUpdateDTO parameterUpdate) {
        parameterUpdate.setParameterId(parameterId);
        return parameterService.update(parameterUpdate);
    }

    @ApiOperation("校验参数名是否存在")
    @GetMapping("/check-key-exists")
    public Result<Boolean> checkParameterKeyExists(ParameterKeyExistQuery existQuery) {
        return parameterService.checkParameterKeyExists(existQuery);
    }
}
