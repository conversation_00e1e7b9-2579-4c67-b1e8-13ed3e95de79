package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.thread.OThreadContext;
import com.taobao.wireless.orange.common.thread.OThreadContextHolder;
import com.taobao.wireless.orange.external.switchcenter.SwitchConfig;
import com.taobao.wireless.orange.oswitch.manager.common.PermissionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HomeController {

    @Autowired
    private PermissionManager permissionManager;

    @Value("${orange.console.fe.assert.domain}")
    private String assertDomain;

    @GetMapping("/")
    public String index(Model model) {
        OThreadContext threadContext = OThreadContextHolder.get();
        if (threadContext != null) {
            model.addAttribute("userId", threadContext.getWorkerId());
            model.addAttribute("userName", threadContext.getWorkerName());
            model.addAttribute("isAdmin", permissionManager.isAdmin());
        }

        model.addAttribute("assertDomain", assertDomain);
        model.addAttribute("version", SwitchConfig.feVersion);
        model.addAttribute("announcement", SwitchConfig.announcement);

        return "index";
    }
}
