package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.ReleaseOrderService;
import com.taobao.wireless.orange.service.model.*;
import com.taobao.wireless.tiga.release.console.api.task.model.dto.TaskStageListDTO;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateInstanceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "发布单管理接口")
@RestController
@RequestMapping("/api/release-orders")
public class ReleaseOrderController {

    @Autowired
    private ReleaseOrderService releaseOrderService;

    @ApiOperation("查询发布列表")
    @GetMapping
    public PaginationResult<ReleaseOrderDTO> query(ReleaseOrderQueryDTO query,
                                                   @RequestParam(defaultValue = "1") Integer page,
                                                   @RequestParam(defaultValue = "10") Integer size) {
        return releaseOrderService.query(query, new Pagination(page, size));
    }

    @ApiOperation("查询发布单状态统计")
    @GetMapping("/count-by-status")
    public Result<Map<ReleaseOrderStatus, Long>> countByStatus(@RequestParam("namespaceId") String namespaceId) {
        return releaseOrderService.getCountGroupByStatus(namespaceId);
    }

    @ApiOperation("创建发布单")
    @PostMapping
    public Result<String> create(@RequestBody ReleaseOrderCreateDTO releaseOrder) {
        return releaseOrderService.create(releaseOrder);
    }

    @ApiOperation("申请发布")
    @PutMapping("/{releaseVersion}/apply-release")
    public Result<Void> applyRelease(@PathVariable("releaseVersion") String releaseVersion,
                                     @RequestBody ApplyReleaseDTO applyRelease) {
        return releaseOrderService.applyRelease(releaseVersion, applyRelease);
    }

    @ApiOperation("正式发布")
    @PutMapping("/{releaseVersion}/publish")
    public Result<Void> publish(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.publish(releaseVersion);
    }

    @ApiOperation("取消发布单")
    @PutMapping("/{releaseVersion}/cancel")
    public Result<Void> cancel(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.cancel(releaseVersion);
    }

    @ApiOperation("百分比发布")
    @PutMapping("/{releaseVersion}/ratio-gray")
    public Result<Void> ratioGray(@PathVariable("releaseVersion") String releaseVersion,
                                  @RequestBody RatioGrayDTO ratioGray) {
        return releaseOrderService.ratioGray(releaseVersion, ratioGray);
    }

    @ApiOperation("跳到指定状态")
    @PutMapping("/{releaseVersion}/skip")
    public Result<Void> skip(@PathVariable("releaseVersion") String releaseVersion,
                             @RequestBody SkipCommand skipCommand) {
        return releaseOrderService.skip(releaseVersion, skipCommand);
    }

    @ApiOperation("Tiga小流量灰度")
    @PutMapping("/{releaseVersion}/tiga-gray")
    public Result<Void> tigaGray(@PathVariable("releaseVersion") String releaseVersion,
                                 @RequestBody SmallFlowGrayCommand smallFlowGrayCommand) {
        return releaseOrderService.tigaGray(releaseVersion, smallFlowGrayCommand);
    }

    @ApiOperation("发起验证")
    @PutMapping("/{releaseVersion}/start-verify")
    public Result<Void> startVerify(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.startVerify(releaseVersion);
    }

    @ApiOperation("发布单验证反馈")
    @PutMapping("/{releaseVersion}/verify-reply")
    public Result<Void> verifyReply(@PathVariable("releaseVersion") String releaseVersion,
                                    @RequestBody VerifyReplyDTO verifyReply) {
        return releaseOrderService.verifyReply(releaseVersion, verifyReply);
    }

    @ApiOperation("查询发布版本详情")
    @GetMapping("/{releaseVersion}")
    public Result<ReleaseOrderDetailDTO> getDetail(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.getDetail(releaseVersion);
    }

    @ApiOperation("查询发布变更内容")
    @GetMapping("/{releaseVersion}/changes")
    public Result<ReleaseOrderChangesDTO> getChanges(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.getChanges(releaseVersion);
    }

    @ApiOperation("查询发布单操作记录列表")
    @GetMapping("/{releaseVersion}/operations")
    public Result<List<ReleaseOrderOperationDTO>> getOperations(@PathVariable("releaseVersion") String releaseVersion,
                                                                @RequestParam(required = false) List<OperationType> operationTypes) {
        return releaseOrderService.getOperations(releaseVersion, operationTypes);
    }

    @ApiOperation("查询发布单调试信息")
    @GetMapping("/{releaseVersion}/debug-info")
    public Result<DebugInfoDTO> getDebugInfo(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.getDebugInfo(releaseVersion);
    }

    @ApiOperation("查询发布单关联的 Tiga 任务阶段列表")
    @PutMapping("/{releaseVersion}/tiga-task-stage-list")
    public Result<TaskStageListDTO> getTigaTaskStageList(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.getTigaTaskStageList(releaseVersion);
    }

    @ApiOperation("查询发布单可用灰度模板列表")
    @GetMapping("/{releaseVersion}/gray-templates")
    public Result<List<TemplateInstanceDTO>> getGrayTemplates(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.getGrayTemplates(releaseVersion);
    }

    @ApiOperation("changefree 回调接口")
    @PostMapping("/{releaseVersion}/changefree/callback")
    public Result<Void> changefreeCallback(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.changefreeCallback(releaseVersion);
    }

    @ApiOperation("查询扫码灰度日志")
    @GetMapping("/{releaseVersion}/scan-beta-logs/{type}")
    public Result<List<Map<String, String>>> getScanBetaLogs(@PathVariable("releaseVersion") String releaseVersion,
                                                             @PathVariable("type") ScanBetaLogQuery.ScanBetaLogType type,
                                                             @RequestParam(defaultValue = "1") Integer page,
                                                             @RequestParam(defaultValue = "10") Integer size) {
        ScanBetaLogQuery query = new ScanBetaLogQuery();
        query.setReleaseVersion(releaseVersion);
        query.setType(type);
        query.setPagination(new Pagination(page, size));
        return releaseOrderService.getScanBetaLogs(query);
    }

    @ApiOperation("查询发布单分批发布进展")
    @GetMapping("/{releaseVersion}/ratio-gray-progress")
    public Result<RatioGrayProgressDTO> getRatioGrayProgress(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.getRatioGrayProgress(releaseVersion);
    }
}
