package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.external.idealab.IdealabService;
import com.taobao.wireless.orange.service.model.IdealabPredictCommand;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "AI 相关接口")
@RestController
@RequestMapping("/api/ai")  // 复数形式
@Slf4j
public class AIController {

    @Autowired
    private IdealabService idealabService;

    @ApiOperation("idealab 推理接口")
    @PostMapping("/idealab/predict")
    public Result<String> idealabPredict(@RequestBody IdealabPredictCommand command) {
        String content = idealabService.predict(command.getAppCode(), command.getVariableMap());
        return Result.success(content);
    }
}
