package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.ConditionService;
import com.taobao.wireless.orange.service.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Api(tags = "条件管理接口")
@RestController
@RequestMapping("/api/conditions")
public class ConditionController {

    @Autowired
    private ConditionService conditionService;

    @ApiOperation("查询条件列表")
    @GetMapping
    public PaginationResult<ConditionDetailDTO> query(ConditionQueryDTO conditionQueryDTO,
                                                      @RequestParam(defaultValue = "1") Integer page,
                                                      @RequestParam(defaultValue = "10") Integer size) {
        return conditionService.query(conditionQueryDTO, new Pagination(page, size));
    }

    @ApiOperation("根据条件ID返回条件基础信息MAP")
    @GetMapping("/map")
    public Result<Map<String, ConditionDTO>> getConditionMapByIds(@RequestParam("conditionIds") String conditionIds) {
        return conditionService.getConditionMapByIds(Arrays.stream(conditionIds.split(",")).toList());
    }

    @ApiOperation("获取所有条件")
    @GetMapping("/all")
    public Result<List<ConditionDTO>> getAll(ConditionQueryDTO query) {
        return conditionService.getAll(query);
    }

    @ApiOperation("查询条件线上版本的详情")
    @GetMapping("/{conditionId}")
    public Result<ConditionDetailDTO> getByConditionId(@PathVariable("conditionId") String conditionId) {
        return conditionService.getByConditionId(conditionId);
    }

    @ApiOperation("更新条件基础信息")
    @PutMapping("/{conditionId}")
    public Result<Void> update(@PathVariable("conditionId") String conditionId,
                               @RequestBody ConditionDirectUpdateDTO condition) {
        condition.setConditionId(conditionId);
        return conditionService.update(condition);
    }

    @ApiOperation("查询设备量")
    @PostMapping("/devices/count")
    public Result<Long> countDevices(@RequestBody ExpressionDeviceCountQuery query) {
        return conditionService.countDevices(query);
    }
}
