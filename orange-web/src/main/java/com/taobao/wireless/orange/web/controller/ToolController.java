package com.taobao.wireless.orange.web.controller;

import com.alibaba.fastjson2.JSON;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.model.proto.GrayConfigProto;
import com.taobao.wireless.orange.common.model.proto.IndexProto;
import com.taobao.wireless.orange.common.model.proto.ReleaseConfigProto;
import com.taobao.wireless.orange.external.oss.OssService;
import com.taobao.wireless.orange.oswitch.dal.dao.OResourceDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OResourceDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Api(tags = "工具接口")
@RestController
@RequestMapping("/api/tools")  // 复数形式
@Slf4j
public class ToolController {

    @Autowired
    private OResourceDAO resourceDAO;

    @Autowired
    private OssService ossService;

    @FunctionalInterface
    private interface ProtoParser {
        MessageOrBuilder parse(byte[] bytes) throws Exception;
    }

    private final static Map<ResourceType, ProtoParser> PROTO_MAP = new HashMap<>() {
        {
            put(ResourceType.FULL_INDEX, IndexProto::parseFrom);
            put(ResourceType.INCREMENTAL_INDEX, IndexProto::parseFrom);

            put(ResourceType.FULL_RELEASE_CONFIG, ReleaseConfigProto::parseFrom);
            put(ResourceType.INCREMENTAL_RELEASE_CONFIG, ReleaseConfigProto::parseFrom);

            put(ResourceType.FULL_GRAY_CONFIG, GrayConfigProto::parseFrom);
            put(ResourceType.BETA_CONFIG, GrayConfigProto::parseFrom);
        }
    };

    @ApiOperation("查看配置文件内容")
    @GetMapping("/resources")
    public Result<Object> getResourceContent(@RequestParam("resourceId") String resourceId) throws Exception {
        OResourceDO resource = resourceDAO.lambdaQuery()
                .eq(OResourceDO::getResourceId, resourceId)
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RESOURCE_NOT_EXIST));

        byte[] bytes = ossService.readData(resourceId);

        MessageOrBuilder proto = PROTO_MAP.get(resource.getType()).parse(bytes);
        if (proto == null) {
            return Result.success(null);
        }

        String jsonStr = JsonFormat.printer()
                .includingDefaultValueFields()
                .print(proto);

        return Result.success(JSON.parseObject(jsonStr));
    }
}
