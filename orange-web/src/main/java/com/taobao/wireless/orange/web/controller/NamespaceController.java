package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.NamespaceService;
import com.taobao.wireless.orange.service.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 命名空间管理控制器
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Api(tags = "命名空间管理接口")
@RestController
@RequestMapping("/api/namespaces")
public class NamespaceController {
    /**
     * 命名空间服务接口
     */
    @Autowired
    private NamespaceService namespaceService;

    /**
     * 获取所有命名空间列表
     *
     * @return Result对象，包含命名空间列表数据
     */
    @GetMapping
    public PaginationResult<NamespaceDTO> query(NamespaceQueryDTO namespaceQueryDTO,
                                                @RequestParam(defaultValue = "1") Integer page,
                                                @RequestParam(defaultValue = "10") Integer size) {
        return namespaceService.query(namespaceQueryDTO, new Pagination(page, size));
    }

    /**
     * 创建新的命名空间
     *
     * @param namespace 需要创建的命名空间对象
     * @return Result对象，包含新创建的命名空间ID
     */
    @PostMapping
    public Result<String> create(@RequestBody NamespaceCreateDTO namespace) {
        return namespaceService.create(namespace);
    }

    /**
     * 根据ID获取指定命名空间
     *
     * @param namespaceId 命名空间ID
     * @return Result对象，包含查询到的命名空间信息
     */
    @GetMapping("/{namespaceId}")
    public Result<NamespaceDTO> getByNamespaceId(@PathVariable("namespaceId") String namespaceId) {
        return namespaceService.getByNamespaceId(namespaceId);
    }

    /**
     * 根据名称获取指定命名空间
     *
     * @param namespaceName
     * @return
     */
    @GetMapping("/name/{namespaceName}")
    public Result<NamespaceDTO> getByNamespaceName(@PathVariable("namespaceName") String namespaceName) {
        return namespaceService.getByNamespaceName(namespaceName);
    }

    /**
     * 更新指定ID的命名空间信息
     *
     * @param namespaceId      待更新的命名空间ID
     * @param updatedNamespace 更新后的命名空间对象
     */
    @PutMapping("/{namespaceId}")
    public Result<Void> update(@PathVariable("namespaceId") String namespaceId, @RequestBody NamespaceUpdateDTO updatedNamespace) {
        updatedNamespace.setNamespaceId(namespaceId);
        return namespaceService.update(updatedNamespace);
    }

    /**
     * 删除指定ID的命名空间
     *
     * @param namespaceId 命名空间ID
     */
    @DeleteMapping("/{namespaceId}")
    public Result<Void> delete(@PathVariable("namespaceId") String namespaceId) {
        return namespaceService.delete(namespaceId);
    }

    /**
     * 获取指定命名空间的历史版本
     *
     * @param namespaceId 命名空间ID
     * @param page        页码
     * @param size        每页大小
     * @return Result对象，包含历史版本列表数据
     */
    @GetMapping("/{namespaceId}/histories")
    public PaginationResult<NamespaceVersionDTO> histories(@PathVariable("namespaceId") String namespaceId,
                                                           @RequestParam(defaultValue = "1") Integer page,
                                                           @RequestParam(defaultValue = "10") Integer size) {
        return namespaceService.histories(namespaceId, new Pagination(page, size));
    }

    /**
     * 获取当前用户常用的namespace列表
     *
     * @param limit 返回数量限制，默认10，最大50
     * @return Result对象，包含常用namespace列表
     */
    @ApiOperation("获取当前用户常用的namespace列表")
    @GetMapping("/frequent")
    public Result<List<NamespaceDTO>> getFrequentNamespaces(
            @ApiParam(value = "返回数量限制，默认5，最大50", example = "5")
            @RequestParam(defaultValue = "5") Integer limit) {
        return namespaceService.getFrequentNamespaces(limit);
    }

    /**
     * 获取当前用户最近访问的namespace列表
     *
     * @param limit 返回数量限制，默认10，最大50
     * @return Result对象，包含最近访问namespace列表
     */
    @ApiOperation("获取当前用户最近访问的namespace列表")
    @GetMapping("/recent")
    public Result<List<NamespaceDTO>> getRecentNamespaces(
            @ApiParam(value = "返回数量限制，默认5，最大50", example = "5")
            @RequestParam(defaultValue = "5") Integer limit) {
        return namespaceService.getRecentNamespaces(limit);
    }
}
