package com.taobao.wireless.orange.service.mapper;

import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.common.constant.OperatorConstants;
import com.taobao.wireless.orange.oswitch.manager.namespace.mapper.ExpressionMapper;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionExpressionBO;
import com.taobao.wireless.tiga.release.expression.LogicExpression;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ExpressionMapper 单元测试
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
public class ExpressionMapperTest extends BaseTest {

    @Autowired
    private ExpressionMapper expressionMapper;

    private ConditionExpressionBO testConditionExpressionBO;

    @Before
    public void setUp() {
        // 准备测试数据 - ConditionExpressionBO
        testConditionExpressionBO = new ConditionExpressionBO();
        testConditionExpressionBO.setOperator(OperatorConstants.AND);

        ConditionExpressionBO child1 = new ConditionExpressionBO();
        child1.setKey("m_brand");
        child1.setOperator("==");
        child1.setValue("Huawei");

        ConditionExpressionBO child2 = new ConditionExpressionBO();
        child2.setKey("app_ver");
        child2.setOperator(">=");
        child2.setValue("1.0.0");

        testConditionExpressionBO.setChildren(Arrays.asList(child1, child2));
    }

    @Test
    public void testToLogicExpression() {
        // 执行转换
        LogicExpression result = expressionMapper.toLogicExpression(testConditionExpressionBO);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getType()).isEqualTo(LogicExpression.ExpressionType.LOGICAL);
    }

    @Test
    public void testToLogicExpressionWithNull() {
        // 测试null输入
        LogicExpression result = expressionMapper.toLogicExpression(null);
        assertThat(result).isNull();
    }

    @Test
    public void testToLogicExpressionList() {
        // 准备测试数据
        List<ConditionExpressionBO> boList = Arrays.asList(testConditionExpressionBO);

        // 执行转换
        List<LogicExpression> result = expressionMapper.toLogicExpressionList(boList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.getFirst().getType()).isEqualTo(LogicExpression.ExpressionType.LOGICAL);
    }

    @Test
    public void testToLogicExpressionListWithNull() {
        // 测试null输入
        List<LogicExpression> result = expressionMapper.toLogicExpressionList(null);
        assertThat(result).isNull();
    }

    @Test
    public void testSimpleEqualityExpression() {
        // 准备测试数据 - 简单的等式表达式
        ConditionExpressionBO equalityBO = new ConditionExpressionBO();
        equalityBO.setKey("m_brand");
        equalityBO.setOperator("==");
        equalityBO.setValue("Samsung");

        // 执行转换
        LogicExpression result = expressionMapper.toLogicExpression(equalityBO);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getType()).isEqualTo(LogicExpression.ExpressionType.EQUALITY);
    }

    @Test
    public void testSimpleComparisonExpression() {
        // 准备测试数据 - 简单的比较表达式
        ConditionExpressionBO comparisonBO = new ConditionExpressionBO();
        comparisonBO.setKey("app_ver");
        comparisonBO.setOperator(">");
        comparisonBO.setValue("2.0.0");

        // 执行转换
        LogicExpression result = expressionMapper.toLogicExpression(comparisonBO);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getType()).isEqualTo(LogicExpression.ExpressionType.COMPARISON);
    }

    @Test
    public void testComplexLogicalExpression() {
        // 准备复杂的逻辑表达式 - 嵌套的AND/OR结构
        ConditionExpressionBO brandChild = new ConditionExpressionBO();
        brandChild.setKey("m_brand");
        brandChild.setOperator("==");
        brandChild.setValue("Huawei");

        ConditionExpressionBO versionChild = new ConditionExpressionBO();
        versionChild.setKey("app_ver");
        versionChild.setOperator(">=");
        versionChild.setValue("1.0.0");

        ConditionExpressionBO andNode = new ConditionExpressionBO();
        andNode.setOperator(OperatorConstants.AND);
        andNode.setChildren(Arrays.asList(brandChild, versionChild));

        ConditionExpressionBO osChild = new ConditionExpressionBO();
        osChild.setKey("os_ver");
        osChild.setOperator("==");
        osChild.setValue("Android");

        ConditionExpressionBO orNode = new ConditionExpressionBO();
        orNode.setOperator(OperatorConstants.OR);
        orNode.setChildren(Arrays.asList(andNode, osChild));

        // 执行转换
        LogicExpression result = expressionMapper.toLogicExpression(orNode);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getType()).isEqualTo(LogicExpression.ExpressionType.LOGICAL);
    }
}
