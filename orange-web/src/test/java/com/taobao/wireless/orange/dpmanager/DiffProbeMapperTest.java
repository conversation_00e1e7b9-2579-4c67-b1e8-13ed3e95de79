package com.taobao.wireless.orange.dpmanager;

import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.publish.probe.model.AserverProbe;
import com.taobao.wireless.orange.service.mapper.DiffProbeMapper;
import com.taobao.wireless.orange.text.manager.model.DiffProbe;
import com.taobao.wireless.orange.text.manager.model.DiffProbeFile;
import com.taobao.wireless.orange.text.manager.model.ProbeMetaItem;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;


/**
 * DiffProbeMapper 单元测试
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
public class DiffProbeMapperTest extends BaseTest {

    @Autowired
    private DiffProbeMapper diffProbeMapper;

    @Test
    public void testConvert_Success() {
        // 准备测试数据
        DiffProbeFile diffProbeFile = createTestDiffProbeFile();

        // 执行转换
        List<AserverProbe> result = diffProbeMapper.convert(diffProbeFile);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);

        AserverProbe aserverProbe = result.get(0);
        assertThat(aserverProbe.getAppkey()).isEqualTo("testAppKey");
        assertThat(aserverProbe.getIndexType()).isEqualTo("dp");
        assertThat(aserverProbe.getHost()).isEqualTo("test.cdn.com");
        assertThat(aserverProbe.getProtocol()).isEqualTo("https");
        assertThat(aserverProbe.getProbeVersion()).isEqualTo("20250801120000");

        // 验证版本列表
        assertThat(aserverProbe.getVersions()).isNotNull();
        assertThat(aserverProbe.getVersions()).hasSize(2);

        AserverProbe.Version version1 = aserverProbe.getVersions().get(0);
        assertThat(version1.getBaseVersion()).isEqualTo("baseVersion1");
        assertThat(version1.getResourceId()).isEqualTo("resourceId1");
        assertThat(version1.getMd5()).isEqualTo("md5Hash1");

        AserverProbe.Version version2 = aserverProbe.getVersions().get(1);
        assertThat(version2.getBaseVersion()).isEqualTo("baseVersion2");
        assertThat(version2.getResourceId()).isEqualTo("resourceId2");
        assertThat(version2.getMd5()).isEqualTo("md5Hash2");
    }

    @Test
    public void testConvert_NullInput() {
        List<AserverProbe> result = diffProbeMapper.convert(null);
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }

    @Test
    public void testConvert_EmptyList() {
        DiffProbeFile diffProbeFile = new DiffProbeFile();
        diffProbeFile.setList(Arrays.asList());

        List<AserverProbe> result = diffProbeMapper.convert(diffProbeFile);
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }

    @Test
    public void testConvert_WithDiffProbeCdn() {
        // 测试 DiffProbe 有自己的 CDN 的情况
        DiffProbeFile diffProbeFile = createTestDiffProbeFile();
        diffProbeFile.getList().get(0).setCdn("probe.specific.cdn.com");

        List<AserverProbe> result = diffProbeMapper.convert(diffProbeFile);

        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getHost()).isEqualTo("probe.specific.cdn.com");
    }

    private DiffProbeFile createTestDiffProbeFile() {
        DiffProbeFile diffProbeFile = new DiffProbeFile();
        diffProbeFile.setCdn("test.cdn.com");
        diffProbeFile.setProtocol("https");
        diffProbeFile.setVersion("20250801");

        DiffProbe diffProbe = new DiffProbe();
        diffProbe.setAppKey("testAppKey");
        diffProbe.setProbeVersion("20250801120000");
        diffProbe.setCdn("test.cdn.com");

        ProbeMetaItem meta1 = new ProbeMetaItem();
        meta1.setBaseVersion("baseVersion1");
        meta1.setResourceId("resourceId1");
        meta1.setMd5("md5Hash1");

        ProbeMetaItem meta2 = new ProbeMetaItem();
        meta2.setBaseVersion("baseVersion2");
        meta2.setResourceId("resourceId2");
        meta2.setMd5("md5Hash2");

        diffProbe.setMetaList(Arrays.asList(meta1, meta2));
        diffProbeFile.setList(Arrays.asList(diffProbe));

        return diffProbeFile;
    }
}
