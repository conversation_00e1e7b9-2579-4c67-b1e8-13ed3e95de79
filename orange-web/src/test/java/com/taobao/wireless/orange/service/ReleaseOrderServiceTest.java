package com.taobao.wireless.orange.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.oswitch.dal.dao.*;
import com.taobao.wireless.orange.oswitch.dal.entity.*;
import com.taobao.wireless.orange.oswitch.manager.namespace.ParameterManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterBO;
import com.taobao.wireless.orange.oswitch.manager.release.ReleaseOrderManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.ReleaseOrderChangeBO;
import com.taobao.wireless.orange.oswitch.manager.release.model.SkipCommandBO;
import com.taobao.wireless.orange.service.model.*;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateInstanceDTO;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static org.assertj.core.api.Assertions.assertThat;

public class ReleaseOrderServiceTest extends BaseIntegrationTest {
    @Autowired
    private ReleaseOrderService releaseOrderService;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;
    @Autowired
    private ReleaseOrderManager releaseOrderManager;
    @Autowired
    private NamespaceService namespaceService;
    @Autowired
    private OProbeDAO probeDAO;

    private String testNamespaceId;
    @Autowired
    private ParameterManager parameterManager;

    @Before
    public void setUp() {
        NamespaceCreateDTO namespaceCreateDTO = new NamespaceCreateDTO();
        testNamespaceId = prepareNamespace(namespaceService, namespaceCreateDTO).getData();
    }

    @Test
    public void getCountGroupByStatus() {
        var result = releaseOrderService.getCountGroupByStatus(testNamespaceId);
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEmpty();
    }

    @Test
    public void query() {
        ReleaseOrderQueryDTO releaseOrderQueryDTO = new ReleaseOrderQueryDTO();
        releaseOrderQueryDTO.setNamespaceId(testNamespaceId);

        Pagination pagination = Pagination.builder()
                .pageNum(1)
                .pageSize(10)
                .build();
        PaginationResult<ReleaseOrderDTO> result = releaseOrderService.query(releaseOrderQueryDTO, pagination);
        assertThat(result.isSuccess()).isTrue();
    }

    @Test
    // 创建新的参数&条件
    public void create() {
        // 跨步骤共享数据
        AtomicReference<String> releaseVersionRef = new AtomicReference<>();
        AtomicReference<OConditionDO> conditionRef = new AtomicReference<>();
        AtomicReference<OParameterDO> parameterRef = new AtomicReference<>();
        AtomicReference<String> releaseVersion2Ref = new AtomicReference<>();
        AtomicReference<OConditionDO> condition2Ref = new AtomicReference<>();
        AtomicReference<OParameterDO> parameter2Ref = new AtomicReference<>();
        AtomicReference<String> releaseVersion3Ref = new AtomicReference<>();

        // 步骤化验证业务流程
        verifyWorkflow("创建新的参数&条件-完整流程",
                // 步骤1：创建首个发布单（新增参数与条件）并校验
                () -> {
                    ReleaseOrderCreateDTO releaseOrder = new ReleaseOrderCreateDTO();
                    releaseOrder.setNamespaceId(testNamespaceId);
                    releaseOrder.setBizType(ReleaseOrderBizType.NAMESPACE);
                    releaseOrder.setReleaseType(ReleaseType.IMPACT_RELEASE);
                    releaseOrder.setDescription("创建新的参数&条件");

                    List<ParameterChangeDTO> parameterChanges = new ArrayList<>();
                    ParameterChangeDTO parameterChange = new ParameterChangeDTO();
                    parameterChange.setParameterKey("test-param-1");
                    parameterChange.setValueType(ParameterValueType.STRING);
                    parameterChange.setConditionNamesOrder(List.of("测试条件"));
                    parameterChange.setChangeType(ChangeType.CREATE);

                    List<ParameterConditionChangeDTO> parameterConditionChanges = new ArrayList<>();
                    ParameterConditionChangeDTO parameterConditionChange1 = new ParameterConditionChangeDTO();
                    parameterConditionChange1.setConditionName("测试条件");
                    parameterConditionChange1.setValue("测试条件-value");
                    parameterConditionChange1.setChangeType(ChangeType.CREATE);
                    parameterConditionChanges.add(parameterConditionChange1);

                    ParameterConditionChangeDTO parameterConditionChange2 = new ParameterConditionChangeDTO();
                    parameterConditionChange2.setConditionId("*");
                    parameterConditionChange2.setValue("默认值-value");
                    parameterConditionChange2.setChangeType(ChangeType.CREATE);
                    parameterConditionChanges.add(parameterConditionChange2);

                    parameterChange.setParameterConditionChanges(parameterConditionChanges);
                    parameterChanges.add(parameterChange);

                    List<ConditionChangeDTO> conditionChanges = new ArrayList<>();
                    ConditionChangeDTO conditionChange = new ConditionChangeDTO();
                    conditionChange.setName("测试条件");
                    conditionChange.setChangeType(ChangeType.CREATE);
                    conditionChange.setExpression(new ConditionExpressionDTO());
                    conditionChanges.add(conditionChange);

                    releaseOrder.setParameterChanges(parameterChanges);
                    releaseOrder.setConditionChanges(conditionChanges);
                    var result = releaseOrderService.create(releaseOrder);
                    assertThat(result.isSuccess()).isTrue();
                    releaseVersionRef.set(result.getData());

                    // 校验 condition
                    var condition = conditionDAO.lambdaQuery()
                            .eq(OConditionDO::getNamespaceId, testNamespaceId)
                            .eq(OConditionDO::getName, "测试条件")
                            .one();
                    assertThat(condition).isNotNull();
                    assertThat(condition.getStatus()).isEqualTo(ConditionStatus.INIT);
                    conditionRef.set(condition);

                    // 校验 conditionVersion
                    var conditionVersion = conditionVersionDAO.lambdaQuery()
                            .eq(OConditionVersionDO::getConditionId, condition.getConditionId())
                            .eq(OConditionVersionDO::getReleaseVersion, releaseVersionRef.get())
                            .one();
                    assertThat(conditionVersion).isNotNull();
                    assertThat(conditionVersion.getPreviousReleaseVersion()).isNull();
                    assertThat(conditionVersion.getChangeType()).isEqualTo(ChangeType.CREATE);

                    // 校验 parameter
                    var parameter = parameterDAO.lambdaQuery()
                            .eq(OParameterDO::getNamespaceId, testNamespaceId)
                            .eq(OParameterDO::getParameterKey, "test-param-1")
                            .one();
                    assertThat(parameter).isNotNull();
                    parameterRef.set(parameter);

                    // 校验 parameterVersion
                    var parameterVersion = parameterVersionDAO.lambdaQuery()
                            .eq(OParameterVersionDO::getParameterId, parameter.getParameterId())
                            .eq(OParameterVersionDO::getReleaseVersion, releaseVersionRef.get())
                            .one();
                    assertThat(parameterVersion).isNotNull();
                    assertThat(parameterVersion.getConditionsOrder()).isEqualTo(condition.getConditionId());
                    assertThat(parameterVersion.getPreviousReleaseVersion()).isNull();
                    assertThat(parameterVersion.getChangeType()).isEqualTo(ChangeType.CREATE);

                    // 校验 parameterConditionVersion
                    var parameterConditionVersions = parameterConditionVersionDAO.lambdaQuery()
                            .eq(OParameterConditionVersionDO::getParameterId, parameter.getParameterId())
                            .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersionRef.get())
                            .list();
                    assertThat(parameterConditionVersions.size()).isEqualTo(2);
                    assertThat(parameterConditionVersions.stream().anyMatch(i ->
                            i.getConditionId().equals(condition.getConditionId()) && i.getValue().equals("测试条件-value"))).isTrue();
                    assertThat(parameterConditionVersions.stream().anyMatch(i ->
                            i.getConditionId().equals(DEFAULT_CONDITION_ID) && i.getValue().equals("默认值-value"))).isTrue();
                    parameterConditionVersions.forEach(i -> {
                        assertThat(i.getPreviousReleaseVersion()).isNull();
                        assertThat(i.getChangeType()).isEqualTo(ChangeType.CREATE);
                    });

                    // 校验 releaseOrder
                    var releaseOrderDO = releaseOrderDAO.lambdaQuery()
                            .eq(OReleaseOrderDO::getReleaseVersion, releaseVersionRef.get())
                            .one();
                    assertThat(releaseOrderDO).isNotNull();
                    assertThat(releaseOrderDO.getStatus()).isEqualTo(ReleaseOrderStatus.IN_PROGRESS);

                    // 校验 namespaceVersionDO
                    var namespaceVersionDO = namespaceVersionDAO.lambdaQuery()
                            .eq(ONamespaceVersionDO::getNamespaceId, testNamespaceId)
                            .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersionRef.get())
                            .one();
                    assertThat(namespaceVersionDO).isNotNull();
                    assertThat(namespaceVersionDO.getChangeType()).isEqualTo(NamespaceVersionChangeType.NEW_IMPACT_RELEASE);
                },
                // 步骤2：并行修改相同参数应失败
                () -> {
                    ReleaseOrderCreateDTO releaseOrder2 = new ReleaseOrderCreateDTO();
                    releaseOrder2.setNamespaceId(testNamespaceId);
                    releaseOrder2.setBizType(ReleaseOrderBizType.NAMESPACE);
                    releaseOrder2.setReleaseType(ReleaseType.IMPACT_RELEASE);
                    releaseOrder2.setDescription("修改参数的条件顺序同时新增一个条件值并修改一个已有的条件值");

                    ParameterChangeDTO parameterChange2 = new ParameterChangeDTO();
                    parameterChange2.setPreviousReleaseVersion(releaseVersionRef.get());
                    parameterChange2.setParameterId(parameterRef.get().getParameterId());
                    parameterChange2.setChangeType(ChangeType.UPDATE);
                    parameterChange2.setConditionNamesOrder(List.of("测试条件", "测试条件2"));
                    releaseOrder2.setParameterChanges(List.of(parameterChange2));

                    var result2 = releaseOrderService.create(releaseOrder2);
                    assertThat(result2.isSuccess()).isFalse();
                    assertThat(result2.getCode()).isEqualTo(ExceptionEnum.PARAMETER_IS_PUBLISHING.getCode());

                    // 把 releaseOrder2 暂存到 ThreadLocal 通过外层变量即可在下一步重建
                },
                // 步骤3：发布首个版本后再次创建变更，应成功
                () -> {
                    // 构造 releaseOrder2 完整变更
                    ReleaseOrderCreateDTO releaseOrder2 = new ReleaseOrderCreateDTO();
                    releaseOrder2.setNamespaceId(testNamespaceId);
                    releaseOrder2.setBizType(ReleaseOrderBizType.NAMESPACE);
                    releaseOrder2.setReleaseType(ReleaseType.IMPACT_RELEASE);
                    releaseOrder2.setDescription("修改参数的条件顺序同时新增一个条件值并修改一个已有的条件值");

                    ParameterChangeDTO parameterChange2 = new ParameterChangeDTO();
                    parameterChange2.setPreviousReleaseVersion(releaseVersionRef.get());
                    parameterChange2.setParameterId(parameterRef.get().getParameterId());
                    parameterChange2.setChangeType(ChangeType.UPDATE);
                    parameterChange2.setConditionNamesOrder(List.of("测试条件", "测试条件2"));

                    ParameterConditionChangeDTO parameterConditionChange3 = new ParameterConditionChangeDTO();
                    parameterConditionChange3.setConditionName("测试条件2");
                    parameterConditionChange3.setValue("测试条件2-value");
                    parameterConditionChange3.setChangeType(ChangeType.CREATE);
                    ParameterConditionChangeDTO parameterConditionChange4 = new ParameterConditionChangeDTO();
                    parameterConditionChange4.setConditionId(conditionRef.get().getConditionId());
                    parameterConditionChange4.setValue("测试条件-value-2");
                    parameterConditionChange4.setChangeType(ChangeType.UPDATE);
                    parameterChange2.setParameterConditionChanges(Arrays.asList(parameterConditionChange3, parameterConditionChange4));

                    releaseOrder2.setParameterChanges(List.of(parameterChange2));

                    ConditionChangeDTO conditionChange2 = new ConditionChangeDTO();
                    conditionChange2.setName("测试条件2");
                    conditionChange2.setChangeType(ChangeType.CREATE);
                    conditionChange2.setExpression(new ConditionExpressionDTO());
                    releaseOrder2.setConditionChanges(List.of(conditionChange2));

                    // 发布首个版本
                    SkipCommandBO skipCommandBO = new SkipCommandBO();
                    skipCommandBO.setSkipType(SkipType.ALL);
                    skipCommandBO.setReason("跳过");
                    releaseOrderManager.skip(releaseVersionRef.get(), skipCommandBO);
                    releaseOrderManager.publish(releaseVersionRef.get());

                    var result2 = releaseOrderService.create(releaseOrder2);
                    assertThat(result2.isSuccess()).isTrue();
                    releaseVersion2Ref.set(result2.getData());
                },
                // 步骤4：校验第二个发布单的数据
                () -> {
                    var condition2 = conditionDAO.lambdaQuery()
                            .eq(OConditionDO::getNamespaceId, testNamespaceId)
                            .eq(OConditionDO::getName, "测试条件2")
                            .one();
                    assertThat(condition2).isNotNull();
                    assertThat(condition2.getStatus()).isEqualTo(ConditionStatus.INIT);
                    condition2Ref.set(condition2);

                    var conditionVersion2 = conditionVersionDAO.lambdaQuery()
                            .eq(OConditionVersionDO::getConditionId, condition2.getConditionId())
                            .eq(OConditionVersionDO::getReleaseVersion, releaseVersion2Ref.get())
                            .one();
                    assertThat(conditionVersion2).isNotNull();
                    assertThat(conditionVersion2.getPreviousReleaseVersion()).isNull();
                    assertThat(conditionVersion2.getChangeType()).isEqualTo(ChangeType.CREATE);

                    var parameter2 = parameterDAO.lambdaQuery()
                            .eq(OParameterDO::getNamespaceId, testNamespaceId)
                            .eq(OParameterDO::getParameterKey, "test-param-1")
                            .one();
                    assertThat(parameter2).isNotNull();
                    assertThat(parameter2.getParameterId()).isEqualTo(parameterRef.get().getParameterId());
                    parameter2Ref.set(parameter2);

                    var parameterVersion2 = parameterVersionDAO.lambdaQuery()
                            .eq(OParameterVersionDO::getParameterId, parameter2.getParameterId())
                            .eq(OParameterVersionDO::getReleaseVersion, releaseVersion2Ref.get())
                            .one();
                    assertThat(parameterVersion2).isNotNull();
                    assertThat(parameterVersion2.getConditionsOrder()).isEqualTo(conditionRef.get().getConditionId() + "," + condition2.getConditionId());
                    assertThat(parameterVersion2.getPreviousReleaseVersion()).isEqualTo(releaseVersionRef.get());

                    var parameterConditionVersions2 = parameterConditionVersionDAO.lambdaQuery()
                            .eq(OParameterConditionVersionDO::getParameterId, parameter2.getParameterId())
                            .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion2Ref.get())
                            .list();
                    assertThat(parameterConditionVersions2.size()).isEqualTo(2);
                    assertThat(parameterConditionVersions2.stream().anyMatch(i -> i.getConditionId().equals(conditionRef.get().getConditionId()) && i.getValue().equals("测试条件-value-2"))).isTrue();
                    assertThat(parameterConditionVersions2.stream().anyMatch(i -> i.getConditionId().equals(condition2.getConditionId()) && i.getValue().equals("测试条件2-value"))).isTrue();

                    var releaseOrderDO2 = releaseOrderDAO.lambdaQuery()
                            .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion2Ref.get())
                            .one();
                    assertThat(releaseOrderDO2).isNotNull();
                    assertThat(releaseOrderDO2.getStatus()).isEqualTo(ReleaseOrderStatus.IN_PROGRESS);

                    var namespaceVersion2 = namespaceVersionDAO.lambdaQuery()
                            .eq(ONamespaceVersionDO::getNamespaceId, testNamespaceId)
                            .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion2Ref.get())
                            .one();
                    assertThat(namespaceVersion2).isNotNull();
                    assertThat(namespaceVersion2.getChangeType()).isEqualTo(NamespaceVersionChangeType.NEW_IMPACT_RELEASE);
                },
                // 步骤5：创建删除参数的第三个发布单
                () -> {
                    releaseOrderManager.cancel(releaseVersion2Ref.get());

                    ReleaseOrderCreateDTO releaseOrder3 = new ReleaseOrderCreateDTO();
                    releaseOrder3.setNamespaceId(testNamespaceId);
                    releaseOrder3.setBizType(ReleaseOrderBizType.NAMESPACE);
                    releaseOrder3.setReleaseType(ReleaseType.IMPACT_RELEASE);
                    releaseOrder3.setDescription("同时新增&删除&修改已有的参数");
                    ParameterChangeDTO parameterChange3 = new ParameterChangeDTO();
                    parameterChange3.setPreviousReleaseVersion(releaseVersionRef.get());
                    parameterChange3.setParameterId(parameterRef.get().getParameterId());
                    parameterChange3.setChangeType(ChangeType.DELETE);
                    releaseOrder3.setParameterChanges(List.of(parameterChange3));

                    var result3 = releaseOrderService.create(releaseOrder3);
                    assertThat(result3.isSuccess()).isTrue();
                    releaseVersion3Ref.set(result3.getData());
                },
                // 步骤6：校验删除发布单的数据与影响
                () -> {
                    var parameter3 = parameterDAO.lambdaQuery()
                            .eq(OParameterDO::getNamespaceId, testNamespaceId)
                            .eq(OParameterDO::getParameterKey, "test-param-1")
                            .one();
                    assertThat(parameter3).isNotNull();
                    assertThat(parameter3.getStatus()).isEqualTo(ParameterStatus.ONLINE);

                    var parameterVersion3 = parameterVersionDAO.lambdaQuery()
                            .eq(OParameterVersionDO::getParameterId, parameter3.getParameterId())
                            .eq(OParameterVersionDO::getReleaseVersion, releaseVersion3Ref.get())
                            .one();
                    assertThat(parameterVersion3).isNotNull();
                    assertThat(parameterVersion3.getPreviousReleaseVersion()).isEqualTo(releaseVersionRef.get());
                    assertThat(parameterVersion3.getChangeType()).isEqualTo(ChangeType.DELETE);

                    var parameterConditionVersions3 = parameterConditionVersionDAO.lambdaQuery()
                            .eq(OParameterConditionVersionDO::getParameterId, parameter3.getParameterId())
                            .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion3Ref.get())
                            .list();
                    assertThat(parameterConditionVersions3.size()).isEqualTo(2);

                    parameterConditionVersions3.stream()
                            .filter(i -> i.getConditionId().equals(DEFAULT_CONDITION_ID))
                            .findAny()
                            .ifPresent(i -> {
                                assertThat(i.getChangeType()).isEqualTo(ChangeType.DELETE);
                                assertThat(i.getValue()).isEqualTo("");
                            });

                    parameterConditionVersions3.stream()
                            .filter(i -> i.getConditionId().equals(conditionRef.get().getConditionId()))
                            .findAny()
                            .ifPresent(i -> {
                                assertThat(i.getChangeType()).isEqualTo(ChangeType.DELETE);
                                assertThat(i.getValue()).isEqualTo("");
                            });

                    ReleaseOrderChangeBO changes = releaseOrderManager.getChanges(releaseVersion3Ref.get());
                    assertThat(changes.getParameterChanges().size()).isEqualTo(1);
                    assertThat(changes.getParameterChanges().getFirst().getChangeType()).isEqualTo(ChangeType.DELETE);

                    ParameterBO parameterBO = new ParameterBO();
                    parameterBO.setNamespaceId(testNamespaceId);
                    Page<ParameterBO> queryResult = parameterManager.query(parameterBO, new Pagination(1, 10));
                    assertThat(queryResult.getTotal()).isEqualTo(1);
                }
        );
    }

    @Test
    public void createNoImpactOrder() {
        String input = """
                {
                    "description": "1",
                    "conditionChanges": [
                        {
                            "name": "ruanying_cond10",
                            "expression": {
                                "operator": "AND",
                                "children": [
                                    {
                                        "key": "app_ver",
                                        "operator": "==",
                                        "value": "1.2.3",
                                        "id": "criteria-1751263585027"
                                    }
                                ]
                            },
                            "changeType": "CREATE",
                            "color": "red"
                        }
                    ],
                    "releaseType": "NO_IMPACT_RELEASE",
                    "bizType": "NAMESPACE"
                }
                """;
        ReleaseOrderCreateDTO releaseOrder = JSON.parseObject(input, ReleaseOrderCreateDTO.class);
        releaseOrder.setNamespaceId(testNamespaceId);
        releaseOrder.setBizId(testNamespaceId);
        var result = releaseOrderService.create(releaseOrder);
        assertThat(result.isSuccess()).isTrue();
        String releaseVersion = result.getData();

        // 校验 namespaceVersion
        var namespaceVersion = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, releaseOrder.getNamespaceId())
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .one();
        assertThat(namespaceVersion).isNull();

        SkipCommandBO skipCommandBO = new SkipCommandBO();
        skipCommandBO.setSkipType(SkipType.ALL);
        skipCommandBO.setReason("跳过");
        releaseOrderManager.skip(releaseVersion, skipCommandBO);
        var result2 = releaseOrderService.publish(releaseVersion);
        assertThat(result2.isSuccess()).isTrue();

        // 校验 namespaceVersion
        var namespaceVersion2 = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, releaseOrder.getNamespaceId())
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .one();
        assertThat(namespaceVersion2).isNotNull();
        assertThat(namespaceVersion2.getChangeType()).isEqualTo(NamespaceVersionChangeType.FINISH_NO_IMPACT_RELEASE);
        // 第一次发布，namespaceVersion 和 namespaceChangeVersion 应该相同
        assertThat(Long.parseLong(namespaceVersion2.getNamespaceVersion())).isEqualTo(Long.parseLong(namespaceVersion2.getNamespaceChangeVersion()));

        releaseOrder.getConditionChanges().getFirst().setName("ruanying_cond1");
        result = releaseOrderService.create(releaseOrder);
        releaseOrderManager.skip(result.getData(), skipCommandBO);
        releaseOrderService.publish(result.getData());
        namespaceVersion2 = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, releaseOrder.getNamespaceId())
                .eq(ONamespaceVersionDO::getReleaseVersion, result.getData())
                .one();
        assertThat(Long.parseLong(namespaceVersion2.getNamespaceVersion())).isGreaterThan(Long.parseLong(namespaceVersion2.getNamespaceChangeVersion()));
    }

    @Test
    public void cancel() {
    }

    @Test
    public void ratioGray() {
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 空白参数验证
     * 场景：传入空白发布版本，应该抛出参数验证异常
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithBlankReleaseVersion_ShouldThrowException() {
        // 测试null参数
        Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(null);
        assertThat(result.isSuccess()).as("操作应该失败").isFalse();

        // 测试空字符串参数
        result = releaseOrderService.getRatioGrayProgress("");
        assertThat(result.isSuccess()).as("操作应该失败").isFalse();

        // 测试空白字符串参数
        result = releaseOrderService.getRatioGrayProgress("   ");
        assertThat(result.isSuccess()).as("操作应该失败").isFalse();
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 仅有发布单无操作记录
     * 场景：发布单存在但没有灰度和发布操作记录，应该返回空的进展信息
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithNoOperations_ShouldReturnEmptyProgress() {
        // 准备测试数据
        String testReleaseVersion = "test-release-no-ops-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-无操作记录",
                // 步骤1：创建发布单但不创建操作记录
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：查询并断言
                () -> {
                    Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("数据不应为null").isNotNull();

                    RatioGrayProgressDTO progressDTO = result.getData();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("灰度进展节点应该为null").isNull();
                    assertThat(progressDTO.getReleaseProgressNode()).as("发布进展节点应该为null").isNull();
                }
        );
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 仅有灰度操作
     * 场景：发布单有灰度操作但没有正式发布操作，应该返回灰度进展信息
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithOnlyGrayOperations_ShouldReturnGrayProgress() {
        // 准备测试数据
        String testReleaseVersion = "test-release-gray-only-" + System.currentTimeMillis();
        String testIndexVersion1 = "index-v1-" + System.currentTimeMillis();
        String testIndexVersion2 = "index-v2-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-仅灰度操作",
                // 步骤1：创建发布单
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：创建灰度操作记录和对应的命名空间版本
                () -> {
                    OReleaseOrderOperationDO grayOperation1 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation1.setParams("{\"grayRatio\": 1000}"); // 1%
                    releaseOrderOperationDAO.save(grayOperation1);

                    OReleaseOrderOperationDO grayOperation2 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation2.setParams("{\"grayRatio\": 5000}"); // 5%
                    releaseOrderOperationDAO.save(grayOperation2);

                    // 创建对应的命名空间版本，确保数量一致
                    ONamespaceVersionDO grayNsVersion1 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion1);
                    namespaceVersionDAO.save(grayNsVersion1);

                    ONamespaceVersionDO grayNsVersion2 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion2);
                    namespaceVersionDAO.save(grayNsVersion2);

                    // 创建对应的探针数据
                    OProbeDO probe1 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion1, "task-001");
                    OProbeDO probe2 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion2, "task-002");
                    probeDAO.save(probe1);
                    probeDAO.save(probe2);
                },
                // 步骤3：查询并断言
                () -> {
                    Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("数据不应为null").isNotNull();

                    RatioGrayProgressDTO progressDTO = result.getData();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("灰度进展节点不应为null").isNotNull();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("应该有2个灰度进展节点").hasSize(2);
                    assertThat(progressDTO.getReleaseProgressNode()).as("发布进展节点应该为null").isNull();

                    // 验证灰度节点信息
                    List<RatioGrayProgressDTO.RatioGrayProgressNode> grayNodes = progressDTO.getRatioGrayProgressNodes();
                    assertThat(grayNodes.get(0).getGrayRatio()).as("第一个灰度比例应该为1000").isEqualTo(1000);
                    assertThat(grayNodes.get(1).getGrayRatio()).as("第二个灰度比例应该为5000").isEqualTo(5000);
                    assertThat(grayNodes.get(0).getStartTime()).as("第一个灰度开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(1).getStartTime()).as("第二个灰度开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(0).getScheduleTime()).as("第一个灰度调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(1).getScheduleTime()).as("第二个灰度调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(0).getAgatewareTaskId()).as("第一个灰度任务ID不应为空").isEqualTo("task-001");
                    assertThat(grayNodes.get(1).getAgatewareTaskId()).as("第二个灰度任务ID不应为空").isEqualTo("task-002");
                }
        );
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 仅有正式发布操作
     * 场景：发布单有正式发布操作但没有灰度操作，应该返回发布进展信息
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithOnlyReleaseOperation_ShouldReturnReleaseProgress() {
        // 准备测试数据
        String testReleaseVersion = "test-release-only-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-仅正式发布操作",
                // 步骤1：创建发布单
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：创建正式发布操作记录
                () -> {
                    OReleaseOrderOperationDO releaseOperation = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RELEASE, OperationStatus.SUCCESS);
                    releaseOrderOperationDAO.save(releaseOperation);

                    ONamespaceVersionDO releaseNsVersion = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.FINISH_IMPACT_RELEASE, "index-release-v-" + System.currentTimeMillis());
                    namespaceVersionDAO.save(releaseNsVersion);
                },
                // 步骤3：查询并断言
                () -> {
                    Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("数据不应为null").isNotNull();

                    RatioGrayProgressDTO progressDTO = result.getData();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("灰度进展节点应该为null").isNull();
                    assertThat(progressDTO.getReleaseProgressNode()).as("发布进展节点不应为null").isNotNull();

                    // 验证发布节点信息
                    RatioGrayProgressDTO.ReleaseProgressNode releaseNode = progressDTO.getReleaseProgressNode();
                    assertThat(releaseNode.getStartTime()).as("开始时间不应为空").isNotNull();
                    assertThat(releaseNode.getScheduleTime()).as("调度时间应该为null（无探针数据）").isNull();
                    assertThat(releaseNode.getAgatewareTaskId()).as("任务ID应该为null（无探针数据）").isNull();
                    assertThat(releaseNode.getAgatewareTaskInfo()).as("任务状态应该为null（无探针数据）").isNull();
                }
        );
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 完整场景
     * 场景：发布单有灰度操作、正式发布操作、命名空间版本和探针数据
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithCompleteData_ShouldReturnFullProgress() {
        // 准备测试数据
        String testReleaseVersion = "test-release-complete-" + System.currentTimeMillis();
        String testIndexVersion1 = "index-v1-" + System.currentTimeMillis();
        String testIndexVersion2 = "index-v2-" + System.currentTimeMillis();
        String testIndexVersion3 = "index-v3-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-完整场景",
                // 步骤1：创建发布单
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：创建灰度和正式发布操作记录
                () -> {
                    OReleaseOrderOperationDO grayOperation1 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation1.setParams("{\"grayRatio\": 1000}"); // 1%
                    releaseOrderOperationDAO.save(grayOperation1);

                    OReleaseOrderOperationDO grayOperation2 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation2.setParams("{\"grayRatio\": 5000}"); // 5%
                    releaseOrderOperationDAO.save(grayOperation2);

                    OReleaseOrderOperationDO releaseOperation = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RELEASE, OperationStatus.SUCCESS);
                    releaseOrderOperationDAO.save(releaseOperation);
                },
                // 步骤3：创建命名空间版本记录
                () -> {
                    // 创建灰度命名空间版本
                    ONamespaceVersionDO grayNsVersion1 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion1);
                    namespaceVersionDAO.save(grayNsVersion1);

                    ONamespaceVersionDO grayNsVersion2 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion2);
                    namespaceVersionDAO.save(grayNsVersion2);

                    // 创建正式发布命名空间版本
                    ONamespaceVersionDO releaseNsVersion = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.FINISH_IMPACT_RELEASE, testIndexVersion3);
                    namespaceVersionDAO.save(releaseNsVersion);
                },
                // 步骤4：创建探针数据
                () -> {
                    OProbeDO probe1 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion1, "12345");
                    OProbeDO probe2 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion2, "12346");
                    OProbeDO probe3 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion3, "12347");
                    probeDAO.save(probe1);
                    probeDAO.save(probe2);
                    probeDAO.save(probe3);
                },
                // 步骤5：查询并断言
                () -> {
                    Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("数据不应为null").isNotNull();

                    RatioGrayProgressDTO progressDTO = result.getData();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("灰度进展节点不应为null").isNotNull();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("应该有2个灰度进展节点").hasSize(2);
                    assertThat(progressDTO.getReleaseProgressNode()).as("发布进展节点不应为null").isNotNull();

                    // 验证灰度节点信息
                    List<RatioGrayProgressDTO.RatioGrayProgressNode> grayNodes = progressDTO.getRatioGrayProgressNodes();
                    assertThat(grayNodes.get(0).getGrayRatio()).as("第一个灰度比例应该为1000").isEqualTo(1000);
                    assertThat(grayNodes.get(1).getGrayRatio()).as("第二个灰度比例应该为5000").isEqualTo(5000);
                    assertThat(grayNodes.get(0).getStartTime()).as("第一个灰度开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(1).getStartTime()).as("第二个灰度开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(0).getScheduleTime()).as("第一个灰度调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(1).getScheduleTime()).as("第二个灰度调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(0).getAgatewareTaskId()).as("第一个灰度任务ID不应为空").isEqualTo("12345");
                    assertThat(grayNodes.get(1).getAgatewareTaskId()).as("第二个灰度任务ID不应为空").isEqualTo("12346");

                    // 验证发布节点信息
                    RatioGrayProgressDTO.ReleaseProgressNode releaseNode = progressDTO.getReleaseProgressNode();
                    assertThat(releaseNode.getStartTime()).as("发布开始时间不应为空").isNotNull();
                    assertThat(releaseNode.getScheduleTime()).as("发布调度时间不应为空").isNotNull();
                    assertThat(releaseNode.getAgatewareTaskId()).as("发布任务ID不应为空").isEqualTo("12347");
                }
        );
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 灰度参数解析异常
     * 场景：灰度操作记录的参数格式错误，应该正常处理并返回null灰度比例
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithInvalidGrayParams_ShouldHandleGracefully() {
        // 准备测试数据
        String testReleaseVersion = "test-release-invalid-params-" + System.currentTimeMillis();
        String testIndexVersion1 = "index-v1-" + System.currentTimeMillis();
        String testIndexVersion2 = "index-v2-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-无效灰度参数",
                // 步骤1：创建发布单
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：创建包含无效参数的灰度操作记录和对应的命名空间版本
                () -> {
                    OReleaseOrderOperationDO grayOperation1 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation1.setParams("{\"invalidField\": 1000}"); // 缺少grayRatio字段
                    releaseOrderOperationDAO.save(grayOperation1);

                    OReleaseOrderOperationDO grayOperation2 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation2.setParams(null); // null参数
                    releaseOrderOperationDAO.save(grayOperation2);

                    // 创建对应的命名空间版本，确保数量一致
                    ONamespaceVersionDO grayNsVersion1 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion1);
                    namespaceVersionDAO.save(grayNsVersion1);

                    ONamespaceVersionDO grayNsVersion2 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion2);
                    namespaceVersionDAO.save(grayNsVersion2);

                    // 创建对应的探针数据
                    OProbeDO probe1 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion1, "task-invalid-001");
                    OProbeDO probe2 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion2, "task-invalid-002");
                    probeDAO.save(probe1);
                    probeDAO.save(probe2);
                },
                // 步骤3：查询并断言
                () -> {
                    Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("数据不应为null").isNotNull();

                    RatioGrayProgressDTO progressDTO = result.getData();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("灰度进展节点不应为null").isNotNull();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("应该有2个灰度进展节点").hasSize(2);

                    // 验证所有灰度比例都应该为null（由于参数解析失败）
                    List<RatioGrayProgressDTO.RatioGrayProgressNode> grayNodes = progressDTO.getRatioGrayProgressNodes();
                    assertThat(grayNodes.get(0).getGrayRatio()).as("第一个灰度比例应该为null（缺少字段）").isNull();
                    assertThat(grayNodes.get(1).getGrayRatio()).as("第二个灰度比例应该为null（null参数）").isNull();

                    // 验证其他字段正常
                    assertThat(grayNodes.get(0).getStartTime()).as("第一个灰度开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(1).getStartTime()).as("第二个灰度开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(0).getScheduleTime()).as("第一个灰度调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(1).getScheduleTime()).as("第二个灰度调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(0).getAgatewareTaskId()).as("第一个灰度任务ID不应为空").isEqualTo("task-invalid-001");
                    assertThat(grayNodes.get(1).getAgatewareTaskId()).as("第二个灰度任务ID不应为空").isEqualTo("task-invalid-002");
                }
        );
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 多个灰度操作场景
     * 场景：发布单有多个灰度操作记录，灰度操作和命名空间版本数量一致
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithMultipleGrayOperations_ShouldReturnConsistentData() {
        // 准备测试数据
        String testReleaseVersion = "test-release-multiple-gray-" + System.currentTimeMillis();
        String testIndexVersion1 = "index-v1-" + System.currentTimeMillis();
        String testIndexVersion2 = "index-v2-" + System.currentTimeMillis();
        String testIndexVersion3 = "index-v3-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-多个灰度操作",
                // 步骤1：创建发布单
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：创建3个灰度操作记录和对应的3个命名空间版本
                () -> {
                    OReleaseOrderOperationDO grayOperation1 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation1.setParams("{\"grayRatio\": 1000}");
                    releaseOrderOperationDAO.save(grayOperation1);

                    OReleaseOrderOperationDO grayOperation2 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation2.setParams("{\"grayRatio\": 3000}");
                    releaseOrderOperationDAO.save(grayOperation2);

                    OReleaseOrderOperationDO grayOperation3 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation3.setParams("{\"grayRatio\": 5000}");
                    releaseOrderOperationDAO.save(grayOperation3);

                    // 创建对应的3个命名空间版本，确保数量一致
                    ONamespaceVersionDO grayNsVersion1 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion1);
                    namespaceVersionDAO.save(grayNsVersion1);

                    ONamespaceVersionDO grayNsVersion2 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion2);
                    namespaceVersionDAO.save(grayNsVersion2);

                    ONamespaceVersionDO grayNsVersion3 = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion3);
                    namespaceVersionDAO.save(grayNsVersion3);

                    // 创建对应的探针数据
                    OProbeDO probe1 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion1, "task-multi-001");
                    OProbeDO probe2 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion2, "task-multi-002");
                    OProbeDO probe3 = createTestProbe(DEFAULT_APP_KEY, testIndexVersion3, "task-multi-003");
                    probeDAO.save(probe1);
                    probeDAO.save(probe2);
                    probeDAO.save(probe3);
                },
                // 步骤3：查询并断言
                () -> {
                    Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("数据不应为null").isNotNull();

                    RatioGrayProgressDTO progressDTO = result.getData();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("灰度进展节点不应为null").isNotNull();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("应该有3个灰度进展节点").hasSize(3);

                    // 验证节点信息
                    List<RatioGrayProgressDTO.RatioGrayProgressNode> grayNodes = progressDTO.getRatioGrayProgressNodes();
                    assertThat(grayNodes.get(0).getGrayRatio()).as("第一个灰度比例应该为1000").isEqualTo(1000);
                    assertThat(grayNodes.get(1).getGrayRatio()).as("第二个灰度比例应该为3000").isEqualTo(3000);
                    assertThat(grayNodes.get(2).getGrayRatio()).as("第三个灰度比例应该为5000").isEqualTo(5000);

                    // 所有节点都应该有完整信息（有对应的命名空间版本和探针数据）
                    assertThat(grayNodes.get(0).getStartTime()).as("第一个节点开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(0).getScheduleTime()).as("第一个节点调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(0).getAgatewareTaskId()).as("第一个节点任务ID不应为空").isEqualTo("task-multi-001");

                    assertThat(grayNodes.get(1).getStartTime()).as("第二个节点开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(1).getScheduleTime()).as("第二个节点调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(1).getAgatewareTaskId()).as("第二个节点任务ID不应为空").isEqualTo("task-multi-002");

                    assertThat(grayNodes.get(2).getStartTime()).as("第三个节点开始时间不应为空").isNotNull();
                    assertThat(grayNodes.get(2).getScheduleTime()).as("第三个节点调度时间不应为空").isNotNull();
                    assertThat(grayNodes.get(2).getAgatewareTaskId()).as("第三个节点任务ID不应为空").isEqualTo("task-multi-003");
                }
        );
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 验证字段映射
     * 场景：验证从BO到DTO的字段映射是否正确
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_FieldMapping_ShouldMapAllFieldsCorrectly() {
        // 准备测试数据
        String testReleaseVersion = "test-release-field-mapping-" + System.currentTimeMillis();
        String testGrayIndexVersion = "index-gray-v-" + System.currentTimeMillis();
        String testReleaseIndexVersion = "index-release-v-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-字段映射验证",
                // 步骤1：创建完整的测试数据
                () -> {
                    // 创建发布单
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);

                    // 创建灰度操作记录
                    OReleaseOrderOperationDO grayOperation = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation.setParams("{\"grayRatio\": 2500}"); // 25%
                    releaseOrderOperationDAO.save(grayOperation);

                    // 创建正式发布操作记录
                    OReleaseOrderOperationDO releaseOperation = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RELEASE, OperationStatus.SUCCESS);
                    releaseOrderOperationDAO.save(releaseOperation);

                    // 创建对应的命名空间版本，使用不同的indexVersion
                    ONamespaceVersionDO grayNsVersion = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testGrayIndexVersion);
                    namespaceVersionDAO.save(grayNsVersion);

                    ONamespaceVersionDO releaseNsVersion = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.FINISH_IMPACT_RELEASE, testReleaseIndexVersion);
                    namespaceVersionDAO.save(releaseNsVersion);

                    // 创建对应的探针数据
                    OProbeDO grayProbe = createTestProbe(DEFAULT_APP_KEY, testGrayIndexVersion, "task-gray-mapping");
                    OProbeDO releaseProbe = createTestProbe(DEFAULT_APP_KEY, testReleaseIndexVersion, "task-release-mapping");
                    probeDAO.save(grayProbe);
                    probeDAO.save(releaseProbe);
                },
                // 步骤2：查询并验证所有字段映射
                () -> {
                    Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("数据不应为null").isNotNull();

                    RatioGrayProgressDTO progressDTO = result.getData();

                    // 验证灰度进展节点字段映射
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("灰度进展节点不应为null").isNotNull();
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("应该有1个灰度进展节点").hasSize(1);

                    RatioGrayProgressDTO.RatioGrayProgressNode grayNode = progressDTO.getRatioGrayProgressNodes().get(0);
                    assertThat(grayNode.getGrayRatio()).as("灰度比例字段映射正确").isEqualTo(2500);
                    assertThat(grayNode.getStartTime()).as("灰度开始时间字段映射正确").isNotNull();
                    assertThat(grayNode.getScheduleTime()).as("灰度调度时间字段映射正确").isNotNull();
                    assertThat(grayNode.getAgatewareTaskId()).as("灰度任务ID字段映射正确").isEqualTo("task-gray-mapping");
                    assertThat(grayNode.getAgatewareTaskInfo()).as("灰度任务状态字段映射正确（无外部调用时为null）").isNull();

                    // 验证发布进展节点字段映射
                    assertThat(progressDTO.getReleaseProgressNode()).as("发布进展节点不应为null").isNotNull();
                    RatioGrayProgressDTO.ReleaseProgressNode releaseNode = progressDTO.getReleaseProgressNode();
                    assertThat(releaseNode.getStartTime()).as("发布开始时间字段映射正确").isNotNull();
                    assertThat(releaseNode.getScheduleTime()).as("发布调度时间字段映射正确").isNotNull();
                    assertThat(releaseNode.getAgatewareTaskId()).as("发布任务ID字段映射正确").isEqualTo("task-release-mapping");
                    assertThat(releaseNode.getAgatewareTaskInfo()).as("发布任务状态字段映射正确（无外部调用时为null）").isNull();
                }
        );
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 空列表兼容性
     * 场景：验证当灰度进展节点为空时，字段被正确设置为null（兼容历史行为）
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithEmptyGrayNodes_ShouldSetNullForCompatibility() {
        // 准备测试数据
        String testReleaseVersion = "test-release-empty-gray-" + System.currentTimeMillis();
        String testReleaseIndexVersion = "index-release-v-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-空灰度节点兼容性",
                // 步骤1：创建发布单和正式发布操作（无灰度操作）
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);

                    OReleaseOrderOperationDO releaseOperation = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RELEASE, OperationStatus.SUCCESS);
                    releaseOrderOperationDAO.save(releaseOperation);

                    ONamespaceVersionDO releaseNsVersion = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.FINISH_IMPACT_RELEASE, testReleaseIndexVersion);
                    namespaceVersionDAO.save(releaseNsVersion);
                },
                // 步骤2：查询并验证兼容性行为
                () -> {
                    Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("数据不应为null").isNotNull();

                    RatioGrayProgressDTO progressDTO = result.getData();
                    // 验证兼容性：空列表应该被设置为null而不是空列表
                    assertThat(progressDTO.getRatioGrayProgressNodes()).as("空的灰度进展节点应该为null（兼容历史行为）").isNull();
                    assertThat(progressDTO.getReleaseProgressNode()).as("发布进展节点不应为null").isNotNull();
                }
        );
    }

    /**
     * 测试用例：测试获取分批发布进展功能 - 数据不一致异常
     * 场景：灰度操作列表和命名空间版本列表长度不一致时，应该抛出异常或返回错误结果
     */
    @Test
    @Transactional
    public void testGetRatioGrayProgress_WithMismatchedDataLength_ShouldThrowException() {
        // 准备测试数据
        String testReleaseVersion = "test-release-mismatched-length-" + System.currentTimeMillis();
        String testIndexVersion1 = "index-v1-" + System.currentTimeMillis();

        verifyWorkflow("获取分批发布进展-数据长度不一致异常",
                // 步骤1：创建发布单
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：创建3个灰度操作记录但只有1个命名空间版本（故意制造不一致）
                () -> {
                    // 创建3个灰度操作记录
                    OReleaseOrderOperationDO grayOperation1 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation1.setParams("{\"grayRatio\": 1000}");
                    releaseOrderOperationDAO.save(grayOperation1);

                    OReleaseOrderOperationDO grayOperation2 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation2.setParams("{\"grayRatio\": 3000}");
                    releaseOrderOperationDAO.save(grayOperation2);

                    OReleaseOrderOperationDO grayOperation3 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.SUCCESS);
                    grayOperation3.setParams("{\"grayRatio\": 5000}");
                    releaseOrderOperationDAO.save(grayOperation3);

                    // 只创建1个命名空间版本（故意制造不一致）
                    ONamespaceVersionDO grayNsVersion = createTestNamespaceVersion(testReleaseVersion, testNamespaceId, NamespaceVersionChangeType.RATIO_GRAY, testIndexVersion1);
                    namespaceVersionDAO.save(grayNsVersion);

                    // 创建对应的探针数据
                    OProbeDO probe = createTestProbe(DEFAULT_APP_KEY, testIndexVersion1, "task-mismatch-001");
                    probeDAO.save(probe);
                },
                // 步骤3：查询并验证异常处理
                () -> {
                    try {
                        Result<RatioGrayProgressDTO> result = releaseOrderService.getRatioGrayProgress(testReleaseVersion);

                        // 如果方法返回Result而不是抛出异常，验证返回的是失败结果
                        if (result != null) {
                            assertThat(result.isSuccess()).as("操作应该失败，因为数据不一致").isFalse();
                            assertThat(result.getCode()).as("错误信息应该包含数据不一致的描述").isEqualTo(ExceptionEnum.RELEASE_ORDER_GRAY_AND_NAMESPACE_VERSION_NOT_MATCH.getCode());
                        } else {
                            // 如果返回null，也是可以接受的错误处理方式
                            assertThat(true).as("返回null表示处理失败，符合预期").isTrue();
                        }
                    } catch (Exception e) {
                        // 如果抛出异常，验证异常信息
                        assertThat(e.getMessage()).as("异常信息应该包含数据不一致的描述")
                                .containsAnyOf("数据不一致", "长度不匹配", "灰度操作", "命名空间版本", "不一致", "数量不匹配");
                    }
                }
        );
    }

    @Test
    public void verifyReply() {
    }

    @Test
    public void getDetail() {
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 正常场景
     * 场景：存在发布单和操作记录，应该成功返回操作列表
     */
    @Test
    @Transactional
    public void testGetOperations_WithExistingOperations_ShouldReturnOperationsList() {
        // 准备测试数据
        String testReleaseVersion = "test-release-" + System.currentTimeMillis();

        verifyWorkflow("获取操作记录-正常场景",
                // 步骤1：创建发布单
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：创建两条操作记录
                () -> {
                    OReleaseOrderOperationDO operation1 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.SKIP, OperationStatus.SUCCESS);
                    OReleaseOrderOperationDO operation2 = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.VERIFY_REPLY, OperationStatus.INIT);
                    releaseOrderOperationDAO.save(operation1);
                    releaseOrderOperationDAO.save(operation2);
                },
                // 步骤3：查询并断言
                () -> {
                    Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(testReleaseVersion, null);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("操作列表不应为空").isNotNull();
                    assertThat(result.getData()).as("应该返回2个操作记录").hasSize(2);

                    List<ReleaseOrderOperationDTO> operations = result.getData();
                    assertThat(operations.get(0).getId()).as("第一个操作的ID应该大于第二个").isGreaterThan(operations.get(1).getId());

                    ReleaseOrderOperationDTO firstOperation = operations.get(0);
                    assertThat(firstOperation.getType()).as("操作类型应该匹配").isEqualTo(OperationType.VERIFY_REPLY);
                    assertThat(firstOperation.getStatus()).as("操作状态应该匹配").isEqualTo(OperationStatus.INIT);
                }
        );
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 空结果场景
     * 场景：发布单存在但没有操作记录，应该返回空列表
     */
    @Test
    @Transactional
    public void testGetOperations_WithNoOperations_ShouldReturnEmptyList() {
        // 准备测试数据
        String testReleaseVersion = "test-release-empty-" + System.currentTimeMillis();
        String testNamespaceId = "test-namespace-id";

        // 创建测试发布单但不创建操作记录
        OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
        releaseOrderDAO.save(releaseOrder);

        // 执行测试
        Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(testReleaseVersion, null);

        // 验证结果
        assertThat(result.isSuccess()).as("操作应该成功").isTrue();
        assertThat(result.getData()).as("操作列表不应为null").isNotNull();
        assertThat(result.getData()).as("操作列表应该为空").isEmpty();
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 不存在的发布版本
     * 场景：查询不存在的发布版本，应该返回空列表
     */
    @Test
    @Transactional
    public void testGetOperations_WithNonExistentReleaseVersion_ShouldReturnEmptyList() {
        // 使用不存在的发布版本
        String nonExistentReleaseVersion = "non-existent-release-" + System.currentTimeMillis();

        // 执行测试
        Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(nonExistentReleaseVersion, null);

        // 验证结果
        assertThat(result.isSuccess()).as("操作应该成功").isTrue();
        assertThat(result.getData()).as("操作列表不应为null").isNotNull();
        assertThat(result.getData()).as("操作列表应该为空").isEmpty();
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 多种操作类型和状态
     * 场景：发布单有多种类型和状态的操作记录，应该正确返回所有记录
     */
    @Test
    @Transactional
    public void testGetOperations_WithMultipleOperationTypesAndStatuses_ShouldReturnAllOperations() {
        // 准备测试数据
        String testReleaseVersion = "test-release-multi-" + System.currentTimeMillis();
        String testNamespaceId = "test-namespace-id";

        verifyWorkflow("获取操作记录-多类型和状态",
                // 步骤1：创建发布单
                () -> {
                    OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
                    releaseOrderDAO.save(releaseOrder);
                },
                // 步骤2：创建多种类型和状态的操作记录
                () -> {
                    OReleaseOrderOperationDO createOp = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.SKIP, OperationStatus.SUCCESS);
                    OReleaseOrderOperationDO verifyOp = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.VERIFY_REPLY, OperationStatus.SUCCESS);
                    OReleaseOrderOperationDO releaseOp = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RELEASE, OperationStatus.INIT);
                    OReleaseOrderOperationDO failedOp = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.RATIO_GRAY, OperationStatus.FAILED);

                    releaseOrderOperationDAO.save(createOp);
                    releaseOrderOperationDAO.save(verifyOp);
                    releaseOrderOperationDAO.save(releaseOp);
                    releaseOrderOperationDAO.save(failedOp);
                },
                // 步骤3：查询并断言
                () -> {
                    Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(testReleaseVersion, null);
                    assertThat(result.isSuccess()).as("操作应该成功").isTrue();
                    assertThat(result.getData()).as("操作列表不应为空").isNotNull();
                    assertThat(result.getData()).as("应该返回4个操作记录").hasSize(4);

                    List<ReleaseOrderOperationDTO> operations = result.getData();
                    boolean hasCreate = operations.stream().anyMatch(op -> op.getType() == OperationType.SKIP);
                    boolean hasVerify = operations.stream().anyMatch(op -> op.getType() == OperationType.VERIFY_REPLY);
                    boolean hasRelease = operations.stream().anyMatch(op -> op.getType() == OperationType.RELEASE);
                    boolean hasRatioGray = operations.stream().anyMatch(op -> op.getType() == OperationType.RATIO_GRAY);

                    assertThat(hasCreate).as("应该包含CREATE操作").isTrue();
                    assertThat(hasVerify).as("应该包含VERIFY操作").isTrue();
                    assertThat(hasRelease).as("应该包含RELEASE操作").isTrue();
                    assertThat(hasRatioGray).as("应该包含RATIO_GRAY操作").isTrue();

                    boolean hasSuccess = operations.stream().anyMatch(op -> op.getStatus() == OperationStatus.SUCCESS);
                    boolean hasInit = operations.stream().anyMatch(op -> op.getStatus() == OperationStatus.INIT);
                    boolean hasFailed = operations.stream().anyMatch(op -> op.getStatus() == OperationStatus.FAILED);

                    assertThat(hasSuccess).as("应该包含SUCCESS状态").isTrue();
                    assertThat(hasInit).as("应该包含INIT状态").isTrue();
                    assertThat(hasFailed).as("应该包含FAILED状态").isTrue();
                }
        );
    }

    /**
     * 测试用例：测试获取发布单操作记录功能 - 验证字段映射
     * 场景：验证DTO字段是否正确映射
     */
    @Test
    @Transactional
    public void testGetOperations_FieldMapping_ShouldMapAllFieldsCorrectly() {
        // 准备测试数据
        String testReleaseVersion = "test-release-mapping-" + System.currentTimeMillis();
        String testNamespaceId = "test-namespace-id";
        String testParams = "{\"key\": \"value\"}";
        String testResult = "{\"status\": \"success\"}";

        // 创建测试发布单
        OReleaseOrderDO releaseOrder = createTestReleaseOrder(testReleaseVersion, testNamespaceId);
        releaseOrderDAO.save(releaseOrder);

        // 创建包含所有字段的操作记录
        OReleaseOrderOperationDO operation = createTestOperation(testReleaseVersion, testNamespaceId, OperationType.SKIP, OperationStatus.SUCCESS);
        operation.setParams(testParams);
        operation.setResult(testResult);
        releaseOrderOperationDAO.save(operation);

        // 执行测试
        Result<List<ReleaseOrderOperationDTO>> result = releaseOrderService.getOperations(testReleaseVersion, null);

        // 验证结果
        assertThat(result.isSuccess()).as("操作应该成功").isTrue();
        assertThat(result.getData()).as("应该返回1个操作记录").hasSize(1);

        ReleaseOrderOperationDTO operationDTO = result.getData().getFirst();

        // 验证所有字段映射
        assertThat(operationDTO.getId()).as("ID不应为空").isNotNull();
        assertThat(operationDTO.getType()).as("操作类型应该匹配").isEqualTo(OperationType.SKIP);
        assertThat(operationDTO.getStatus()).as("操作状态应该匹配").isEqualTo(OperationStatus.SUCCESS);
        assertThat(operationDTO.getParams()).as("参数应该匹配").isEqualTo(testParams);
        assertThat(operationDTO.getResult()).as("结果应该匹配").isEqualTo(testResult);
        assertThat(operationDTO.getGmtCreate()).as("创建时间不应为空").isNotNull();
        assertThat(operationDTO.getGmtModified()).as("修改时间不应为空").isNotNull();
    }

    // 辅助方法：创建测试发布单
    private OReleaseOrderDO createTestReleaseOrder(String releaseVersion, String namespaceId) {
        OReleaseOrderDO releaseOrder = new OReleaseOrderDO();
        releaseOrder.setReleaseVersion(releaseVersion);
        releaseOrder.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrder.setBizId(namespaceId);
        releaseOrder.setAppKey(DEFAULT_APP_KEY);
        releaseOrder.setNamespaceId(namespaceId);
        releaseOrder.setReleaseType(ReleaseType.IMPACT_RELEASE);
        releaseOrder.setStatus(ReleaseOrderStatus.IN_PROGRESS);
        releaseOrder.setDescription("Test release order");
        return releaseOrder;
    }

    // 辅助方法：创建测试操作记录
    private OReleaseOrderOperationDO createTestOperation(String releaseVersion, String namespaceId,
                                                         OperationType type, OperationStatus status) {
        OReleaseOrderOperationDO operation = new OReleaseOrderOperationDO();
        operation.setReleaseVersion(releaseVersion);
        operation.setAppKey(DEFAULT_APP_KEY);
        operation.setNamespaceId(namespaceId);
        operation.setType(type);
        operation.setStatus(status);
        operation.setGmtCreate(new Date());
        operation.setGmtModified(new Date());
        return operation;
    }

    // 辅助方法：创建测试命名空间版本记录
    private ONamespaceVersionDO createTestNamespaceVersion(String releaseVersion, String namespaceId,
                                                           NamespaceVersionChangeType changeType, String indexVersion) {
        ONamespaceVersionDO namespaceVersion = new ONamespaceVersionDO();
        namespaceVersion.setReleaseVersion(releaseVersion);
        namespaceVersion.setAppKey(DEFAULT_APP_KEY);
        namespaceVersion.setNamespaceId(namespaceId);
        namespaceVersion.setChangeType(changeType);
        namespaceVersion.setIndexVersion(indexVersion);
        namespaceVersion.setNamespaceVersion("ns-v-" + System.currentTimeMillis());
        namespaceVersion.setNamespaceChangeVersion("ns-cv-" + System.currentTimeMillis());
        namespaceVersion.setIsAvailable(Available.y);
        namespaceVersion.setGmtCreate(new Date());
        namespaceVersion.setGmtModified(new Date());
        return namespaceVersion;
    }

    // 辅助方法：创建测试探针记录
    private OProbeDO createTestProbe(String appKey, String indexVersion, String agatewareTaskId) {
        OProbeDO probe = new OProbeDO();
        probe.setAppKey(appKey);
        probe.setIndexVersion(indexVersion);
        probe.setAgatewareTaskId(agatewareTaskId);
        probe.setContent("test probe content");
        probe.setIsAvailable(Available.y);
        probe.setGmtCreate(new Date());
        probe.setGmtModified(new Date());
        return probe;
    }

    @Test
    public void testGetTigaTaskStageList() {
        var result = releaseOrderService.getTigaTaskStageList("not_exist");
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNull();
    }

    @Test
    public void testGetGrayTemplates() {
        Result<List<TemplateInstanceDTO>> result = releaseOrderService.getGrayTemplates("not_exist");
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().isEmpty()).isTrue();

        String testReleaseVersion = "test-release-" + System.currentTimeMillis();
        releaseOrderDAO.save(createTestReleaseOrder(testReleaseVersion, testNamespaceId));
        result = releaseOrderService.getGrayTemplates(testReleaseVersion);
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().isEmpty()).isFalse();
    }
}