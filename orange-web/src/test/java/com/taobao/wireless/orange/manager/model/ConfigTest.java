package com.taobao.wireless.orange.manager.model;

import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.model.proto.GrayConfigProto;
import com.taobao.wireless.orange.common.model.proto.ReleaseConfigProto;
import com.taobao.wireless.orange.external.oss.OssService;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.assertj.core.api.Assertions.assertThat;

public class ConfigTest extends BaseIntegrationTest {

    @Autowired
    private OssService ossService;

    @Before
    public void setUp() {
    }

    @Test
    public void testSerializeAndDeserializeReleaseConfig() {
        String resourceId = "04b81b3e71694b6c87aaeefcf72f3fff";
        byte[] bytes = ossService.readData(resourceId);
        try {
            MessageOrBuilder proto = ReleaseConfigProto.parseFrom(bytes);
            String jsonStr = JsonFormat.printer()
                    .includingDefaultValueFields()
                    .print(proto);
            assertThat(jsonStr).isEqualTo("""
                            {
                              "schemaVersion": "1.0",
                              "namespace": "orange_test",
                              "strategy": "FULL",
                              "type": "RELEASE",
                              "offlineParameters": [],
                              "conditions": [{
                                "id": "cd422ca8572d45b19f8d72423c796fc2",
                                "expression": {
                                  "operator": "AND",
                                  "children": [{
                                    "operator": "\\u003e\\u003d",
                                    "children": [],
                                    "key": "app_ver",
                                    "value": "1.2.3"
                                  }],
                                  "key": "",
                                  "value": ""
                                }
                              }],
                              "parameters": [{
                                "key": "ruanying_boolean",
                                "version": "2025072822480271832",
                                "valueType": "BOOLEAN",
                                "conditionalValues": [{
                                  "conditionId": "cd422ca8572d45b19f8d72423c796fc2",
                                  "boolValue": true
                                }],
                                "defaultBoolValue": false
                              }]
                            }""");
        } catch (Exception e) {
            assertThat(e).isNull();
        }
    }

    @Test
    public void testSerializeAndDeserializeGrayConfig() {
        String resourceId = "958098a46c314bd68e220da9d095f00d";
        byte[] bytes = ossService.readData(resourceId);
        try {
            MessageOrBuilder proto = GrayConfigProto.parseFrom(bytes);
            String jsonStr = JsonFormat.printer()
                    .includingDefaultValueFields()
                    .print(proto);
            assertThat(jsonStr).isEqualTo("""
                    {
                      "schemaVersion": "1.0",
                      "namespace": "orange_test",
                      "strategy": "FULL",
                      "type": "GRAY",
                      "orders": [{
                        "version": "2025072822480271832",
                        "offlineParameters": [],
                        "parameters": [{
                          "key": "ruanying_boolean",
                          "version": "2025072822480271832",
                          "valueType": "BOOLEAN",
                          "conditionalValues": [{
                            "conditionId": "cd422ca8572d45b19f8d72423c796fc2",
                            "boolValue": true
                          }],
                          "defaultBoolValue": false
                        }]
                      }],
                      "conditions": [{
                        "id": "cd422ca8572d45b19f8d72423c796fc2",
                        "expression": {
                          "operator": "AND",
                          "children": [{
                            "operator": "\\u003e\\u003d",
                            "children": [],
                            "key": "app_ver",
                            "value": "1.2.3"
                          }],
                          "key": "",
                          "value": ""
                        }
                      }]
                    }""");
        } catch (Exception e) {
            assertThat(e).isNull();
        }
    }
}
