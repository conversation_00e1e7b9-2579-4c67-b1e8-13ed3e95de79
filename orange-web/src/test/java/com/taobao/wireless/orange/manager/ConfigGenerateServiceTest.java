package com.taobao.wireless.orange.manager;

import com.alibaba.fastjson2.JSON;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.constant.enums.ConfigStrategy;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceDAO;
import com.taobao.wireless.orange.publish.config.FullReleaseConfigGenerator;
import com.taobao.wireless.orange.publish.config.IncrementalReleaseConfigGenerator;
import com.taobao.wireless.orange.publish.config.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.publish.config.model.ReleaseConfig;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;


public class ConfigGenerateServiceTest extends BaseIntegrationTest {
    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;
    @Autowired
    private IncrementalReleaseConfigGenerator incrementalReleaseConfigGenerator;
    @Autowired
    private ONamespaceDAO namespaceDAO;

    private static final String testNamespaceId = "36c3e76c57694e62aba86cb318bbe180";

    @Test
    public void generateIncrementalReleaseConfig() {
        NamespaceIdNameRecord namespace = Optional.ofNullable(this.namespaceDAO.getByNamespaceId(testNamespaceId)).map(n -> new NamespaceIdNameRecord(n.getNamespaceId(), n.getName())).orElseThrow(() -> new RuntimeException("namespace not exist"));

        ReleaseConfig releaseConfig1 = this.incrementalReleaseConfigGenerator.generate(namespace, "0");
        ReleaseConfig releaseConfig2 = this.fullReleaseConfigGenerator.generate(namespace, "0");
        assertThat(releaseConfig1).isNotNull();
        assertThat(releaseConfig1.getStrategy()).isEqualTo(ConfigStrategy.INCREMENTAL);
        releaseConfig1.setStrategy(ConfigStrategy.FULL);
        assertThat(JSON.toJSONString(releaseConfig1)).isEqualTo(JSON.toJSONString(releaseConfig2));
    }
}
