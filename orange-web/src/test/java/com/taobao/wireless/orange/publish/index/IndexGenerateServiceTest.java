package com.taobao.wireless.orange.publish.index;

import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionDO;
import org.junit.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class IndexGenerateServiceTest {

    private static final DateTimeFormatter FORMATTER =
            DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS").withZone(ZoneId.systemDefault());

    @Test
    /**
     * 正常场景：间隔为 [30, 60, 120] 分钟，70 分钟步进，期望分两组
     */
    public void testGroupNamespaceVersionsByTimeGaps_NormalCase() {
        // Setup
        long now = System.currentTimeMillis();
        List<Integer> timeGaps = Arrays.asList(30, 60, 120); // 30分钟, 60分钟, 120分钟

        // Create test namespace versions with different change times
        List<ONamespaceVersionDO> namespaceVersions = new ArrayList<>();
        for (int i = 1; i < 7; i++) {
            ONamespaceVersionDO version = new ONamespaceVersionDO();
            // Create proper Snowflake IDs with timestamps
            long timestamp = now - (i * 70 * 60 * 1000L); // 70分钟间隔
            String datetimePrefix = FORMATTER.format(Instant.ofEpochMilli(timestamp));
            long changeVersion = Long.parseLong(datetimePrefix + "00");
            version.setNamespaceChangeVersion(String.valueOf(changeVersion));
            namespaceVersions.add(version);
        }

        // Execute
        List<String> result = IndexGenerateService.groupNamespaceVersionsByTimeGaps(now, namespaceVersions, timeGaps);

        // Verify
        assertThat(result).isNotNull();
        assertThat(result.getFirst()).isEqualTo(namespaceVersions.getFirst().getNamespaceChangeVersion());
        assertThat(result.get(1)).isEqualTo(namespaceVersions.get(1).getNamespaceChangeVersion());
        assertThat(result).hasSize(2);
    }

    @Test
    /**
     * 正常场景：不同的间隔组合，验证分组结果仍为两组
     */
    public void testGroupNamespaceVersionsByTimeGaps_NormalCase2() {
        // Setup
        long now = System.currentTimeMillis();
        List<Integer> timeGaps = Arrays.asList(60, 120, 130);

        // Create test namespace versions with different change times
        List<ONamespaceVersionDO> namespaceVersions = new ArrayList<>();
        for (int i = 1; i < 7; i++) {
            ONamespaceVersionDO version = new ONamespaceVersionDO();
            // Create proper Snowflake IDs with timestamps
            long timestamp = now - (i * 70 * 60 * 1000L); // 70分钟间隔
            String datetimePrefix = FORMATTER.format(Instant.ofEpochMilli(timestamp));
            long changeVersion = Long.parseLong(datetimePrefix + "00");
            version.setNamespaceChangeVersion(String.valueOf(changeVersion));
            namespaceVersions.add(version);
        }

        // Execute
        List<String> result = IndexGenerateService.groupNamespaceVersionsByTimeGaps(now, namespaceVersions, timeGaps);

        // Verify
        assertThat(result).isNotNull();
        assertThat(result.get(0)).isEqualTo(namespaceVersions.get(0).getNamespaceChangeVersion());
        assertThat(result.get(1)).isEqualTo(namespaceVersions.get(1).getNamespaceChangeVersion());
        assertThat(result).hasSize(2);
    }

    @Test
    /**
     * 正常场景：较小的时间间隔，期望结果为空
     */
    public void testGroupNamespaceVersionsByTimeGaps_NormalCase3() {
        // Setup
        long now = System.currentTimeMillis();
        List<Integer> timeGaps = Arrays.asList(60, 120, 240);

        // Create test namespace versions with different change times
        List<ONamespaceVersionDO> namespaceVersions = new ArrayList<>();
        for (int i = 1; i < 7; i++) {
            ONamespaceVersionDO version = new ONamespaceVersionDO();
            // Create proper Snowflake IDs with timestamps
            long timestamp = now - (i * 3 * 60 * 1000L);
            String datetimePrefix = FORMATTER.format(Instant.ofEpochMilli(timestamp));
            long changeVersion = Long.parseLong(datetimePrefix + "00");
            version.setNamespaceChangeVersion(String.valueOf(changeVersion));
            namespaceVersions.add(version);
        }

        // Execute
        List<String> result = IndexGenerateService.groupNamespaceVersionsByTimeGaps(now, namespaceVersions, timeGaps);

        // Verify
        assertThat(result).isEmpty();
    }

    @Test
    /**
     * 边界场景：样本数量小于阈值，应返回空列表
     */
    public void testGroupNamespaceVersionsByTimeGaps_LessThanThreshold() {
        // Setup
        long now = System.currentTimeMillis();
        List<Integer> timeGaps = Arrays.asList(30, 60);

        // Create only 2 namespace versions (less than threshold of 5)
        List<ONamespaceVersionDO> namespaceVersions = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            ONamespaceVersionDO version = new ONamespaceVersionDO();
            // Create proper Snowflake IDs with timestamps
            long timestamp = now - (i * 10 * 60 * 1000L);
            String datetimePrefix = FORMATTER.format(Instant.ofEpochMilli(timestamp));
            long changeVersion = Long.parseLong(datetimePrefix + "00");
            version.setNamespaceChangeVersion(String.valueOf(changeVersion));
            namespaceVersions.add(version);
        }

        // Execute
        List<String> result = IndexGenerateService.groupNamespaceVersionsByTimeGaps(now, namespaceVersions, timeGaps);

        // Verify
        assertThat(result).isNotNull();
        // Should be empty as we have less than threshold
        assertThat(result).isEmpty();
    }

    @Test
    /**
     * 边界场景：空输入，应返回空列表
     */
    public void testGroupNamespaceVersionsByTimeGaps_EmptyInput() {
        // Setup
        long now = System.currentTimeMillis();
        List<Integer> timeGaps = Arrays.asList(30, 60);
        List<ONamespaceVersionDO> namespaceVersions = new ArrayList<>();

        // Execute
        List<String> result = IndexGenerateService.groupNamespaceVersionsByTimeGaps(now, namespaceVersions, timeGaps);

        // Verify
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }

    @Test
    /**
     * 精确匹配：间隔刚好匹配阈值，期望返回一组
     */
    public void testGroupNamespaceVersionsByTimeGaps_ExactMatch() {
        // Setup
        long now = System.currentTimeMillis();
        List<Integer> timeGaps = Arrays.asList(30);

        // Create test namespace versions with exact time gap match
        List<ONamespaceVersionDO> namespaceVersions = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            ONamespaceVersionDO version = new ONamespaceVersionDO();
            // Create proper Snowflake IDs with timestamps
            long timestamp = now - (i * 30 * 60 * 1000L); // 30分钟间隔
            String datetimePrefix = FORMATTER.format(Instant.ofEpochMilli(timestamp));
            long changeVersion = Long.parseLong(datetimePrefix + "00");
            version.setNamespaceChangeVersion(String.valueOf(changeVersion));
            namespaceVersions.add(version);
        }

        // Execute
        List<String> result = IndexGenerateService.groupNamespaceVersionsByTimeGaps(now, namespaceVersions, timeGaps);

        // Verify
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
    }
}