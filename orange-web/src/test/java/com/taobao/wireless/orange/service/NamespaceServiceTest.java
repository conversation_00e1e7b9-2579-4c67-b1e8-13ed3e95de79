package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.model.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;

import static com.taobao.wireless.orange.BaseIntegrationTest.prepareNamespace;
import static com.taobao.wireless.orange.common.exception.ExceptionEnum.NAMESPACE_NOT_EXIST;
import static org.assertj.core.api.Assertions.assertThat;

public class NamespaceServiceTest extends BaseTest {

    @Autowired
    private NamespaceService namespaceService;

    private String testNamespaceId;
    private final String testNamespaceName = "test-namespace-name";

    @Before
    public void setUp() {
        NamespaceCreateDTO testNamespaceDTO = new NamespaceCreateDTO();
        testNamespaceDTO.setName(testNamespaceName);
        testNamespaceId = prepareNamespace(namespaceService, testNamespaceDTO).getData();
    }

    /**
     * 测试用例：测试创建命名空间功能 - 命名空间已存在
     * 场景：当尝试创建一个已存在名称的命名空间时，应该抛出异常
     */
    @Test
    public void testCreate_WhenNamespaceExists_ShouldThrowException() {
        NamespaceCreateDTO testNamespaceDTO = new NamespaceCreateDTO();
        testNamespaceDTO.setName(testNamespaceName);
        Result<String> result = prepareNamespace(namespaceService, testNamespaceDTO);
        assertThat(result.getSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ExceptionEnum.NAMESPACE_NAME_DUPLICATE.getCode());
    }

    /**
     * 测试用例：测试创建命名空间功能 - 成功场景
     * 场景：创建一个全新的命名空间，应该成功并返回ID
     */
    @Test
    public void testCreate_WithNewNamespace_ShouldSucceed() {
        NamespaceCreateDTO newNamespace = new NamespaceCreateDTO();
        String uniqueName = "test-namespace-" + UUID.randomUUID().toString().substring(0, 8);
        newNamespace.setName(uniqueName);
        newNamespace.setBizType(NamespaceBizType.MODULE);
        newNamespace.setBizId("new-biz-id");
        newNamespace.setOwners(Collections.singletonList("149016"));
        newNamespace.setTesters(Collections.singletonList("149017"));
        newNamespace.setAppKey("new-app-key");

        Result<String> result = namespaceService.create(newNamespace);
        assertThat(result.getData())
                .as("创建的命名空间ID不为空")
                .isNotBlank();
    }

    /**
     * 测试用例：测试创建命名空间功能 - 缺少必要字段
     * 场景：创建命名空间时缺少必要字段，应该抛出异常
     */
    @Test
    public void testCreate_WithMissingRequiredFields_ShouldThrowException() {
        NamespaceCreateDTO incompleteNamespace = new NamespaceCreateDTO();
        incompleteNamespace.setName("incomplete-namespace");
        // 故意不设置其他必要字段

        Result<String> result = namespaceService.create(incompleteNamespace);
        assertThat(result.getSuccess())
                .as("创建命名空间时缺少必要字段，应该抛出异常")
                .isFalse();
        assertThat(result.getCode()).isEqualTo(ExceptionEnum.PARAM_INVALID.getCode());
    }

    /**
     * 测试用例：测试查询命名空间列表功能 - 基本场景
     * 场景：正常情况下，传入条件、页码和大小，返回分页结果
     */
    @Test
    public void testQuery_ShouldReturnPageResult() {
        NamespaceQueryDTO condition = new NamespaceQueryDTO();
        condition.setAppKey(DEFAULT_APP_KEY);
        condition.setName(testNamespaceName);
        PaginationResult<NamespaceDTO> result = namespaceService.query(condition, new Pagination(1, 10));
        assertThat(result.getTotal()).isGreaterThan(0);
        assertThat(result.getData().getFirst().getNamespaceId()).isEqualTo(testNamespaceId);
    }

    /**
     * 测试用例：测试查询命名空间列表功能 - 多条件查询
     * 场景：使用多个条件组合查询，应返回符合条件的结果
     */
    @Test
    public void testQuery_WithMultipleConditions_ShouldReturnFilteredResults() {
        NamespaceQueryDTO condition = new NamespaceQueryDTO();
        condition.setAppKey(DEFAULT_APP_KEY);

        PaginationResult<NamespaceDTO> result = namespaceService.query(condition, new Pagination(1, 10));
        assertThat(result.getTotal())
                .as("应该有结果返回")
                .isGreaterThan(0);

        assertThat(result.getData())
                .as("结果应该匹配查询条件")
                .allSatisfy(ns -> {
                    assertThat(ns.getAppKey())
                            .as("结果应该匹配查询的AppKey")
                            .isEqualTo(DEFAULT_APP_KEY);
                    assertThat(ns.getBizType())
                            .as("结果应该匹配查询的业务类型")
                            .isEqualTo(NamespaceBizType.MODULE);
                });
    }

    /**
     * 测试用例：测试查询命名空间列表功能 - 无匹配结果
     * 场景：查询条件没有匹配的记录，应返回空结果集
     */
    @Test
    public void testQuery_WithNoMatchingConditions_ShouldReturnEmptyResults() {
        NamespaceQueryDTO condition = new NamespaceQueryDTO();
        condition.setAppKey("non-existent-app-key");
        condition.setName("non-existent-name");

        PaginationResult<NamespaceDTO> result = namespaceService.query(condition, new Pagination(1, 10));
        assertThat(result.getData())
                .as("没有匹配记录时结果列表应为空")
                .isEmpty();
    }

    /**
     * 测试用例：测试获取指定命名空间详情功能 - 正常场景
     * 场景：正常情况下，传入命名空间ID，返回对应的命名空间对象
     */
    @Test
    public void testGetByNamespaceId_ShouldReturnNamespace() {
        Result<NamespaceDTO> result = namespaceService.getByNamespaceId(testNamespaceId);
        assertThat(result).isNotNull();
        NamespaceDTO namespaceBO = result.getData();
        assertThat(namespaceBO.getNamespaceId()).isEqualTo(testNamespaceId);
        assertThat(namespaceBO.getName()).isEqualTo(testNamespaceName);
    }

    /**
     * 测试用例：测试获取不存在的命名空间
     * 场景：传入不存在的命名空间ID，应该返回null或抛出异常
     */
    @Test
    public void testGetByNamespaceId_WithNonExistentId_ShouldHandleGracefully() {
        String nonExistentId = "non-existent-id-" + UUID.randomUUID();

        Result<NamespaceDTO> result = namespaceService.getByNamespaceId(nonExistentId);
        assertThat(result.getSuccess())
                .as("不存在的命名空间ID")
                .isFalse();
        assertThat(result.getCode()).isEqualTo(NAMESPACE_NOT_EXIST.getCode());
    }

    /**
     * 测试用例：测试更新命名空间功能 - 正常场景
     * 场景：正常情况下，传入更新后的命名空间对象，返回更新结果
     */
    @Test
    public void testUpdate_ShouldReturnUpdateResult() {
        // 先获取原始数据
        Result<NamespaceDTO> original = namespaceService.getByNamespaceId(testNamespaceId);
        assertThat(original.getData())
                .as("测试前提：命名空间必须存在")
                .isNotNull();

        // 准备更新数据
        NamespaceUpdateDTO toUpdate = new NamespaceUpdateDTO();
        toUpdate.setNamespaceId(testNamespaceId);
        toUpdate.setDescription("Updated description " + System.currentTimeMillis());
        toUpdate.setOwners(Arrays.asList("149016", "149017"));

        namespaceService.update(toUpdate);

        // 验证更新结果
        Result<NamespaceDTO> updated = namespaceService.getByNamespaceId(testNamespaceId);
        assertThat(updated.getData().getDescription())
                .as("描述应该已更新")
                .isEqualTo(toUpdate.getDescription());
        assertThat(updated.getData().getOwners())
                .as("所有者列表应该已更新")
                .hasSize(toUpdate.getOwners().size());
    }

    /**
     * 测试用例：测试更新不存在的命名空间
     * 场景：更新一个不存在的命名空间，应该返回失败或抛出异常
     */
    @Test
    public void testUpdate_WithNonExistentNamespace_ShouldFail() {
        NamespaceUpdateDTO nonExistent = new NamespaceUpdateDTO();
        nonExistent.setNamespaceId("non-existent-" + UUID.randomUUID());
        nonExistent.setDescription("Non-existent Namespace");

        Result<Void> result = namespaceService.update(nonExistent);
        assertThat(result.isSuccess())
                .as("不存在的命名空间更新应该失败")
                .isFalse();
        assertThat(result.getCode())
                .as("不存在的命名空间更新应该失败")
                .isEqualTo(NAMESPACE_NOT_EXIST.getCode());
    }

    /**
     * 测试用例：测试部分更新命名空间字段
     * 场景：只更新命名空间的部分字段，其他字段保持不变
     */
    @Test
    public void testUpdate_WithPartialFields_ShouldOnlyUpdateSpecifiedFields() {
        // 先获取原始数据
        Result<NamespaceDTO> original = namespaceService.getByNamespaceId(testNamespaceId);
        assertThat(original.getData())
                .as("测试前提：命名空间必须存在")
                .isNotNull();

        // 只更新描述字段
        NamespaceUpdateDTO partialUpdate = new NamespaceUpdateDTO();
        partialUpdate.setNamespaceId(testNamespaceId);
        partialUpdate.setDescription("Partially updated description " + System.currentTimeMillis());

        namespaceService.update(partialUpdate);

        // 验证更新结果
        Result<NamespaceDTO> updated = namespaceService.getByNamespaceId(testNamespaceId);
        assertThat(updated.getData().getDescription())
                .as("描述应该已更新")
                .isEqualTo(partialUpdate.getDescription());
        assertThat(updated.getData().getOwners())
                .as("所有者列表应该保持不变")
                .hasSize(original.getData().getOwners().size());
    }

    @Test
    public void testHistoriesFilterByChangeType() {
        Pagination pagination = Pagination.builder()
                .pageNum(1)
                .pageSize(10)
                .build();

        String namespaceId = "36c3e76c57694e62aba86cb318bbe180";
        PaginationResult<NamespaceVersionDTO> result = namespaceService.histories(namespaceId, pagination);

        // 验证只返回FINISH_RELEASE类型
        assertThat(result)
                .as("结果不应为空")
                .isNotNull();
        assertThat(result.isSuccess())
                .as("操作应该成功")
                .isTrue();
        assertThat(result.getData()).isNotEmpty();

        // 验证所有返回的版本都是FINISH_RELEASE类型
        assertThat(result.getData())
                .as("所有版本都应该是FINISH_RELEASE类型且内容完整")
                .allSatisfy(version -> {
                    assertThat(version.getChangeType())
                            .as("版本类型应为FINISH_RELEASE")
                            .isEqualTo(NamespaceVersionChangeType.FINISH_IMPACT_RELEASE);
                    if (StringUtils.isNotBlank(version.getIndexVersion())) {
                        assertThat(version.getIndexResourceId())
                                .as("索引资源ID应该被填充")
                                .isNotNull();
                        assertThat(version.getIndexGmtCreate())
                                .as("索引创建时间应该被填充")
                                .isNotNull();
                    }
                });
    }
}