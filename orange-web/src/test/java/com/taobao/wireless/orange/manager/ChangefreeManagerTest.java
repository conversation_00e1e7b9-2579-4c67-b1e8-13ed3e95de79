package com.taobao.wireless.orange.manager;

import com.alibaba.goc.changefree.model.ChangeCheckRes;
import com.alibaba.goc.changefree.model.CheckStatusEnum;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.manager.release.ChangefreeManager;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


public class ChangefreeManagerTest extends BaseIntegrationTest {
    @Autowired
    private ChangefreeManager changefreeManager;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Test
    public void generate() {
        OReleaseOrderDO releaseOrderDO = releaseOrderDAO.lambdaQuery().list().getFirst();
        ChangeCheckRes check = changefreeManager.check(releaseOrderDAO.getByReleaseVersion(releaseOrderDO.getReleaseVersion()), true, Emergent.n);
        Assertions.assertThat(check.getCheckStatusEnum()).isEqualTo(CheckStatusEnum.CHECK_PASS);
    }
}
