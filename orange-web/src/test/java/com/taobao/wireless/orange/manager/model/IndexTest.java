package com.taobao.wireless.orange.manager.model;

import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.model.proto.IndexProto;
import com.taobao.wireless.orange.external.oss.OssService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

public class IndexTest extends BaseIntegrationTest {
    @Autowired
    private OssService ossService;

    @Test
    public void testSerializeAndDeserialize() {
        String resourceId = "8b7b0aa504654bd2b27f64c0c7045d59";
        byte[] bytes = ossService.readData(resourceId);

        assertThatCode(() -> {
            IndexProto actual = IndexProto.parseFrom(bytes);

            String expectedJson = """
                    {
                      "appKey" : "4272",
                      "baseVersion" : 0,
                      "cdn" : "orange-config-testing.oss-cn-hangzhou.aliyuncs.com",
                      "comboPolicy" : {
                        "batchSize" : 102400,
                        "enable" : true,
                        "maxComboCount" : 100,
                        "minComboCount" : 5,
                        "url" : "er.orange.tbcdn.cn"
                      },
                      "namespaces" : [ {
                        "changeVersion" : 2025072900294950400,
                        "gray" : {
                          "orders" : [ {
                            "grayRatio" : 1000,
                            "version" : 2025072822480271832
                          } ],
                          "resourceId" : "958098a46c314bd68e220da9d095f00d",
                          "resourceSize" : 228,
                          "version" : 2025072900294950400
                        },
                        "name" : "orange_test"
                      } ],
                      "schemaVersion" : "1.0",
                      "strategy" : "FULL",
                      "version" : 2025072900294950400
                    }""";

            IndexProto.Builder expectedBuilder = IndexProto.newBuilder();
            JsonFormat.parser().ignoringUnknownFields().merge(expectedJson, expectedBuilder);
            IndexProto expected = expectedBuilder.build();

            assertThat(actual).isEqualTo(expected);
        }).doesNotThrowAnyException();
    }
}