package com.taobao.wireless.orange.service.mapper

import com.taobao.wireless.orange.BaseTest
import com.taobao.wireless.orange.common.constant.OperatorConstants
import com.taobao.wireless.orange.common.constant.enums.ChangeType
import com.taobao.wireless.orange.common.constant.enums.ConditionStatus
import com.taobao.wireless.orange.common.constant.enums.VersionStatus
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionVersionDO
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionBO
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionVersionBO
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterConditionVersionBO
import com.taobao.wireless.orange.service.model.ConditionDTO
import com.taobao.wireless.orange.service.model.ConditionDetailDTO
import com.taobao.wireless.orange.service.model.ConditionExpressionDTO
import com.taobao.wireless.orange.service.model.ParameterConditionDTO
import org.assertj.core.api.Assertions
import org.assertj.core.api.ThrowingConsumer
import org.junit.Before
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import java.util.*

/**
 * ConditionMapper单元测试
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
class ConditionMapperTest : BaseTest() {
    @Autowired
    private val conditionMapper: ConditionMapper? = null

    private var testConditionBO: ConditionBO? = null
    private var testConditionVersionBO: ConditionVersionBO? = null
    private var testRelatedParameters: MutableList<ParameterConditionVersionBO?>? = null

    @Before
    fun setUp() {
        setupTestData()
    }

    private fun setupTestData() {
        // 创建测试用的ConditionBO
        testConditionBO = ConditionBO()
        testConditionBO!!.setConditionId(generateTestId())
        testConditionBO!!.setAppKey(DEFAULT_APP_KEY)
        testConditionBO!!.setNamespaceId(generateTestId())
        testConditionBO!!.setName("测试条件")
        testConditionBO!!.setColor("#FF0000")
        testConditionBO!!.setStatus(ConditionStatus.INIT)
        testConditionBO!!.setGmtCreate(getCurrentTime())
        testConditionBO!!.setGmtModified(getCurrentTime())
        testConditionBO!!.setCreator(DEFAULT_WORKER_ID)
        testConditionBO!!.setModifier(DEFAULT_WORKER_ID)

        // 创建测试用的ConditionVersion
        val conditionVersion = OConditionVersionDO()
        conditionVersion.setId(1L)
        conditionVersion.setConditionId(testConditionBO!!.getConditionId())
        conditionVersion.setAppKey(DEFAULT_APP_KEY)
        conditionVersion.setNamespaceId(testConditionBO!!.getNamespaceId())
        conditionVersion.setExpression("{\"operator\":\"AND\",\"children\":[{\"key\":\"userId\",\"operator\":\"EQUALS\",\"value\":\"123\"}]}")
        conditionVersion.setStatus(VersionStatus.INIT)
        conditionVersion.setChangeType(ChangeType.CREATE)
        conditionVersion.setGmtCreate(getCurrentTime())
        conditionVersion.setGmtModified(getCurrentTime())
        conditionVersion.setCreator(DEFAULT_WORKER_ID)
        conditionVersion.setModifier(DEFAULT_WORKER_ID)
        testConditionBO!!.setConditionVersion(conditionVersion)

        // 创建测试用的相关参数
        testRelatedParameters = ArrayList<ParameterConditionVersionBO?>()
        // 模拟参数条件对象
        val parameterCondition1: ParameterConditionVersionBO = object : ParameterConditionVersionBO() {
            override fun getParameterId(): String {
                return "param1"
            }

            override fun getParameterKey(): String {
                return "test_param_1"
            }

            override fun getConditionId(): String? {
                return testConditionBO!!.getConditionId()
            }

            override fun getValue(): String {
                return "{\"test\":\"value1\"}"
            }
        }
        val parameterCondition2: ParameterConditionVersionBO = object : ParameterConditionVersionBO() {
            override fun getParameterId(): String {
                return "param2"
            }

            override fun getParameterKey(): String {
                return "test_param_2"
            }

            override fun getConditionId(): String? {
                return testConditionBO!!.getConditionId()
            }

            override fun getValue(): String {
                return "{\"test\":\"value2\"}"
            }
        }
        testRelatedParameters!!.add(parameterCondition1)
        testRelatedParameters!!.add(parameterCondition2)
        testConditionBO!!.setRelatedParameters(testRelatedParameters)

        // 创建测试用的ConditionVersionBO
        testConditionVersionBO = ConditionVersionBO()
        testConditionVersionBO!!.setId(1L)
        testConditionVersionBO!!.setConditionId(generateTestId())
        testConditionVersionBO!!.setAppKey(DEFAULT_APP_KEY)
        testConditionVersionBO!!.setNamespaceId(generateTestId())
        testConditionVersionBO!!.setExpression("{\"operator\":\"OR\",\"children\":[{\"key\":\"platform\",\"operator\":\"IN\",\"value\":\"iOS,Android\"}]}")
        testConditionVersionBO!!.setStatus(VersionStatus.INIT)
        testConditionVersionBO!!.setChangeType(ChangeType.UPDATE)
        testConditionVersionBO!!.setGmtCreate(getCurrentTime())
        testConditionVersionBO!!.setGmtModified(getCurrentTime())
        testConditionVersionBO!!.setCreator(DEFAULT_WORKER_ID)
        testConditionVersionBO!!.setModifier(DEFAULT_WORKER_ID)

        // 为ConditionVersionBO设置关联的Condition
        val associatedCondition = ConditionBO()
        associatedCondition.setConditionId(testConditionVersionBO!!.getConditionId())
        associatedCondition.setName("版本条件")
        associatedCondition.setColor("#00FF00")
        associatedCondition.setStatus(ConditionStatus.INIT)
        testConditionVersionBO!!.setCondition(associatedCondition)
    }

    @Test
    fun testToConditionDetailDTO() {
        // 执行转换
        val result = conditionMapper!!.toConditionDetailDTO(testConditionBO)

        // 验证基本字段
        Assertions.assertThat<ConditionDetailDTO?>(result).isNotNull()
        Assertions.assertThat(result!!.getConditionId()).isEqualTo(testConditionBO!!.getConditionId())
        Assertions.assertThat(result.getAppKey()).isEqualTo(testConditionBO!!.getAppKey())
        Assertions.assertThat(result.getNamespaceId()).isEqualTo(testConditionBO!!.getNamespaceId())
        Assertions.assertThat(result.getName()).isEqualTo(testConditionBO!!.getName())
        Assertions.assertThat(result.getColor()).isEqualTo(testConditionBO!!.getColor())
        Assertions.assertThat<ConditionStatus?>(result.getStatus()).isEqualTo(testConditionBO!!.getStatus())
        Assertions.assertThat(result.getCreator()).isEqualTo(testConditionBO!!.getCreator())
        Assertions.assertThat(result.getModifier()).isEqualTo(testConditionBO!!.getModifier())

        // 验证从ConditionVersion映射的字段
        Assertions.assertThat(result.getGmtModified())
            .isEqualTo(testConditionBO!!.getConditionVersion().getGmtCreate())

        // 验证表达式解析
        Assertions.assertThat(result.getExpression()).isNotNull()
        Assertions.assertThat(result.getExpression().getOperator()).isEqualTo(OperatorConstants.AND)
        Assertions.assertThat(result.getExpression().getChildren()).hasSize(1)

        // 验证参数条件列表
        Assertions.assertThat<ParameterConditionDTO?>(result.getParameterConditions()).hasSize(2)
        Assertions.assertThat<ParameterConditionDTO?>(result.getParameterConditions())
            .allSatisfy(ThrowingConsumer { paramCondition: ParameterConditionDTO? ->
                Assertions.assertThat<ParameterConditionDTO?>(paramCondition)
                    .isInstanceOf(ParameterConditionDTO::class.java)
            })
    }

    @Test
    fun testToConditionDetailDTOList() {
        // 准备测试数据
        val condition2 = ConditionBO()
        condition2.setConditionId(generateTestId())
        condition2.setName("测试条件2")
        condition2.setColor("#0000FF")
        condition2.setStatus(ConditionStatus.INIT)

        val version2 = OConditionVersionDO()
        version2.setId(2L)
        version2.setConditionId(condition2.getConditionId())
        version2.setExpression("{\"operator\":\"NOT\",\"children\":[]}")
        version2.setGmtCreate(getCurrentTime())
        condition2.setConditionVersion(version2)
        condition2.setRelatedParameters(ArrayList<ParameterConditionVersionBO?>())

        val conditions = Arrays.asList<ConditionBO?>(testConditionBO, condition2)

        // 执行转换
        val result = conditionMapper!!.toConditionDetailDTOList(conditions)

        // 验证结果
        Assertions.assertThat<ConditionDetailDTO?>(result).hasSize(2)
        Assertions.assertThat(result!!.get(0)!!.getName()).isEqualTo("测试条件")
        Assertions.assertThat(result.get(1)!!.getName()).isEqualTo("测试条件2")
    }

    @Test
    fun testToConditionDTO() {
        // 执行转换
        val result = conditionMapper!!.toConditionDTO(testConditionBO)

        // 验证基本字段
        Assertions.assertThat<ConditionDTO?>(result).isNotNull()
        Assertions.assertThat(result!!.getName()).isEqualTo(testConditionBO!!.getName())
        Assertions.assertThat(result.getColor()).isEqualTo(testConditionBO!!.getColor())
        Assertions.assertThat<ConditionStatus?>(result.getStatus()).isEqualTo(testConditionBO!!.getStatus())

        // 验证从ConditionVersion映射的字段
        Assertions.assertThat(result.getConditionId())
            .isEqualTo(testConditionBO!!.getConditionVersion().getConditionId())
        Assertions.assertThat(result.getAppKey()).isEqualTo(testConditionBO!!.getConditionVersion().getAppKey())
        Assertions.assertThat(result.getNamespaceId())
            .isEqualTo(testConditionBO!!.getConditionVersion().getNamespaceId())
        Assertions.assertThat(result.getGmtCreate()).isEqualTo(testConditionBO!!.getConditionVersion().getGmtCreate())
        Assertions.assertThat(result.getGmtModified())
            .isEqualTo(testConditionBO!!.getConditionVersion().getGmtModified())
        Assertions.assertThat(result.getCreator()).isEqualTo(testConditionBO!!.getConditionVersion().getCreator())
        Assertions.assertThat(result.getModifier()).isEqualTo(testConditionBO!!.getConditionVersion().getModifier())

        // 验证表达式解析
        Assertions.assertThat<ConditionExpressionDTO?>(result.getExpression()).isNotNull()
        Assertions.assertThat(result.getExpression().getOperator()).isEqualTo(OperatorConstants.AND)
    }

    @Test
    fun testToConditionDTOList() {
        // 准备测试数据
        val condition2 = ConditionBO()
        condition2.name = "测试条件2"
        condition2.color = "#0000FF"
        condition2.setStatus(ConditionStatus.INVALID)

        val version2 = OConditionVersionDO()
        version2.setConditionId(generateTestId())
        version2.setExpression("{\"operator\":\"OR\",\"children\":[]}")
        condition2.setConditionVersion(version2)

        val conditions = Arrays.asList<ConditionBO?>(testConditionBO, condition2)

        // 执行转换
        val result = conditionMapper!!.toConditionDTOList(conditions)

        // 验证结果
        Assertions.assertThat<ConditionDTO?>(result).hasSize(2)
        Assertions.assertThat(result!!.get(0)!!.getName()).isEqualTo("测试条件")
        Assertions.assertThat(result.get(0)!!.getColor()).isEqualTo("#FF0000")
        Assertions.assertThat(result.get(1)!!.getName()).isEqualTo("测试条件2")
        Assertions.assertThat(result.get(1)!!.getColor()).isEqualTo("#0000FF")
    }

    @Test
    fun testConditionVersionToConditionDetailDTO() {
        // 执行转换
        val result = conditionMapper!!.conditionVersionToConditionDetailDTO(testConditionVersionBO)

        // 验证基本字段
        Assertions.assertThat<ConditionDetailDTO?>(result).isNotNull()
        Assertions.assertThat(result!!.conditionId).isEqualTo(testConditionVersionBO!!.getConditionId())
        Assertions.assertThat(result.appKey).isEqualTo(testConditionVersionBO!!.getAppKey())
        Assertions.assertThat(result.namespaceId).isEqualTo(testConditionVersionBO!!.getNamespaceId())
        Assertions.assertThat(result.gmtCreate).isEqualTo(testConditionVersionBO!!.getGmtCreate())
        Assertions.assertThat(result.gmtModified).isEqualTo(testConditionVersionBO!!.getGmtModified())
        Assertions.assertThat(result.creator).isEqualTo(testConditionVersionBO!!.getCreator())
        Assertions.assertThat(result.modifier).isEqualTo(testConditionVersionBO!!.getModifier())
        Assertions.assertThat(result.id).isEqualTo(testConditionVersionBO!!.getId())

        // 验证从关联Condition映射的字段
        Assertions.assertThat(result.getName()).isEqualTo("版本条件")
        Assertions.assertThat(result.getColor()).isEqualTo("#00FF00")
        Assertions.assertThat<ConditionStatus?>(result.getStatus()).isEqualTo(ConditionStatus.INIT)

        // 验证表达式解析
        Assertions.assertThat<ConditionExpressionDTO?>(result.getExpression()).isNotNull()
        Assertions.assertThat(result.getExpression().getOperator()).isEqualTo(OperatorConstants.OR)

        // 验证参数条件列表为空（因为设置为ignore）
        Assertions.assertThat<ParameterConditionDTO?>(result.getParameterConditions()).isNull()
    }

    @Test
    fun testConditionVersionsToConditionDetailDTOList() {
        // 准备测试数据
        val version2 = ConditionVersionBO()
        version2.setId(2L)
        version2.setConditionId(generateTestId())
        version2.setExpression("{\"operator\":\"NOT\",\"children\":[]}")

        val condition2 = ConditionBO()
        condition2.setName("版本条件2")
        condition2.setColor("#FFFF00")
        condition2.setStatus(ConditionStatus.INVALID)
        version2.setCondition(condition2)

        val versions = Arrays.asList<ConditionVersionBO?>(testConditionVersionBO, version2)

        // 执行转换
        val result = conditionMapper!!.conditionVersionsToConditionDetailDTOList(versions)

        // 验证结果
        Assertions.assertThat<ConditionDetailDTO?>(result).hasSize(2)
        Assertions.assertThat(result!!.get(0)!!.getName()).isEqualTo("版本条件")
        Assertions.assertThat(result.get(0)!!.getColor()).isEqualTo("#00FF00")
        Assertions.assertThat(result.get(1)!!.getName()).isEqualTo("版本条件2")
        Assertions.assertThat(result.get(1)!!.getColor()).isEqualTo("#FFFF00")
    }

    @Test
    fun testParseExpression() {
        // 测试正常的JSON表达式
        val validJson =
            "{\"operator\":\"AND\",\"children\":[{\"key\":\"userId\",\"operator\":\"EQUALS\",\"value\":\"123\"}]}"
        val result = conditionMapper!!.parseExpression(validJson)

        Assertions.assertThat<ConditionExpressionDTO?>(result).isNotNull()
        Assertions.assertThat(result!!.getOperator()).isEqualTo(OperatorConstants.AND)
        Assertions.assertThat<ConditionExpressionDTO?>(result.getChildren()).hasSize(1)

        // 测试null输入
        val nullResult = conditionMapper.parseExpression(null)
        Assertions.assertThat<ConditionExpressionDTO?>(nullResult).isNull()

        // 测试空字符串
        val emptyResult = conditionMapper.parseExpression("")
        Assertions.assertThat<ConditionExpressionDTO?>(emptyResult).isNull()
    }

    @Test
    fun testMapParameterConditions() {
        // 执行转换
        val result = conditionMapper!!.mapParameterConditions(testRelatedParameters)

        // 验证结果
        Assertions.assertThat<ParameterConditionDTO?>(result).hasSize(2)
        Assertions.assertThat<ParameterConditionDTO?>(result)
            .allSatisfy(ThrowingConsumer { dto: ParameterConditionDTO? ->
                Assertions.assertThat<ParameterConditionDTO?>(dto).isInstanceOf(ParameterConditionDTO::class.java)
            })

        // 测试null输入
        val nullResult = conditionMapper.mapParameterConditions(null)
        Assertions.assertThat<ParameterConditionDTO?>(nullResult).isNull()

        // 测试空列表
        val emptyResult = conditionMapper.mapParameterConditions(ArrayList<Any?>())
        Assertions.assertThat<ParameterConditionDTO?>(emptyResult).isEmpty()
    }

    @Test
    fun testToConditionDetailDTO_WithNullRelatedParameters() {
        // 设置空的相关参数
        testConditionBO!!.setRelatedParameters(null)

        // 执行转换
        val result = conditionMapper!!.toConditionDetailDTO(testConditionBO)

        // 验证结果
        Assertions.assertThat<ConditionDetailDTO?>(result).isNotNull()
        Assertions.assertThat<ParameterConditionDTO?>(result!!.getParameterConditions()).isNull()
    }

    @Test
    fun testToConditionDetailDTO_WithNullExpression() {
        // 设置空的表达式
        testConditionBO!!.getConditionVersion().setExpression(null)

        // 执行转换
        val result = conditionMapper!!.toConditionDetailDTO(testConditionBO)

        // 验证结果
        Assertions.assertThat<ConditionDetailDTO?>(result).isNotNull()
        Assertions.assertThat<ConditionExpressionDTO?>(result!!.getExpression()).isNull()
    }

    @Test
    fun testToConditionDTOList_WithEmptyList() {
        // 执行转换
        val result = conditionMapper!!.toConditionDTOList(ArrayList<ConditionBO?>())

        // 验证结果
        Assertions.assertThat<ConditionDTO?>(result).isEmpty()
    }

    @Test
    fun testToConditionDTOList_WithNullList() {
        // 执行转换
        val result = conditionMapper!!.toConditionDTOList(null)

        // 验证结果
        Assertions.assertThat<ConditionDTO?>(result).isNull()
    }
}
