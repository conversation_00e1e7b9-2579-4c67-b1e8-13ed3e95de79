package com.taobao.wireless.orange;

import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.NamespaceService;
import com.taobao.wireless.orange.service.model.NamespaceCreateDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;

/**
 * 集成测试基类
 * <p>
 * 继承自BaseTest，专门用于集成测试场景
 * 使用testing配置文件，并支持事务回滚
 * <p>
 * 功能特性：
 * 1. 继承BaseTest的所有功能
 * 2. 使用testing环境配置
 * 3. 支持事务回滚
 * 4. 适合完整的端到端测试
 * 5. 包含外部服务集成测试的工具方法
 * <p>
 * 使用场景：
 * - 完整业务流程的端到端测试
 * - 多个服务组件的集成测试
 * - 外部依赖服务的集成测试
 * - 配置相关的测试
 * <p>
 * 使用示例：
 * <pre>
 * public class ReleaseWorkflowIntegrationTest extends BaseIntegrationTest {
 *
 *     @Autowired
 *     private ReleaseOrderService releaseOrderService;
 *
 *     @Autowired
 *     private ConfigManager configManager;
 *
 *     @Test
 *     public void testCompleteReleaseWorkflow() {
 *         // 完整的发布流程集成测试
 *         String releaseVersion = createTestRelease();
 *         publishRelease(releaseVersion);
 *         verifyReleaseConfig(releaseVersion);
 *     }
 * }
 * </pre>
 *
 * <AUTHOR>
 */
public abstract class BaseIntegrationTest extends BaseTest {

    /**
     * 集成测试的自定义设置
     */
    @Override
    protected void customSetUp() throws Exception {
        super.customSetUp();
        logTestInfo("集成测试环境已准备就绪，使用testing配置");
        setupIntegrationTestEnvironment();
    }

    /**
     * 设置集成测试环境
     * 子类可以重写此方法进行特定的环境设置
     */
    protected void setupIntegrationTestEnvironment() {
        // 设置集成测试特有的环境配置
        logTestInfo("初始化集成测试环境");
    }

    // ==================== 集成测试工具方法 ====================

    public static Result<String> prepareNamespace(NamespaceService nsService, NamespaceCreateDTO testNamespaceDTO) {
        String testModuleId = "2613";
        if (StringUtils.isBlank(testNamespaceDTO.getName())) {
            testNamespaceDTO.setName("test_namespace_name");
        }
        testNamespaceDTO.setBizType(NamespaceBizType.MODULE);
        testNamespaceDTO.setBizId(testModuleId);
        testNamespaceDTO.setOwners(Collections.singletonList(DEFAULT_WORKER_ID));
        testNamespaceDTO.setTesters(Collections.singletonList(DEFAULT_WORKER_ID));
        testNamespaceDTO.setAppKey(DEFAULT_APP_KEY);
        return nsService.create(testNamespaceDTO);
    }

    /**
     * 等待异步操作完成
     *
     * @param maxWaitTimeMs 最大等待时间（毫秒）
     * @param checkInterval 检查间隔（毫秒）
     * @param condition     检查条件
     * @return 是否在指定时间内完成
     */
    protected boolean waitForAsyncOperation(long maxWaitTimeMs, long checkInterval,
                                            java.util.function.Supplier<Boolean> condition) {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < maxWaitTimeMs) {
            if (condition.get()) {
                return true;
            }
            waitFor(checkInterval);
        }
        return false;
    }

    /**
     * 等待异步操作完成（使用默认参数）
     *
     * @param condition 检查条件
     * @return 是否完成
     */
    protected boolean waitForAsyncOperation(java.util.function.Supplier<Boolean> condition) {
        return waitForAsyncOperation(30000, 1000, condition); // 默认等待30秒，每秒检查一次
    }

    /**
     * 验证外部服务连接
     *
     * @param serviceName 服务名称
     * @return 是否连接成功
     */
    protected boolean verifyExternalServiceConnection(String serviceName) {
        logTestInfo("验证外部服务连接: " + serviceName);
        // 子类可以实现具体的连接验证逻辑
        return true;
    }

    /**
     * 模拟外部服务调用
     *
     * @param serviceName 服务名称
     * @param operation   操作名称
     * @param parameters  参数
     * @return 模拟结果
     */
    protected Object mockExternalServiceCall(String serviceName, String operation, Object... parameters) {
        logTestInfo("模拟外部服务调用: " + serviceName + "." + operation);
        // 子类可以实现具体的模拟逻辑
        return "mock_result";
    }

    // ==================== 配置测试工具方法 ====================

    /**
     * 验证配置项
     *
     * @param configKey     配置键
     * @param expectedValue 期望值
     */
    protected void assertConfigValue(String configKey, String expectedValue) {
        logTestInfo("验证配置项: " + configKey + " = " + expectedValue);
        // 子类可以注入Environment或ConfigurationProperties来实现
    }

    /**
     * 临时修改配置项（仅在测试期间有效）
     *
     * @param configKey 配置键
     * @param newValue  新值
     */
    protected void temporarySetConfig(String configKey, String newValue) {
        logTestInfo("临时设置配置项: " + configKey + " = " + newValue);
        // 子类可以使用TestPropertySource或其他方式实现
    }

    // ==================== 数据验证工具方法 ====================

    /**
     * 验证完整的业务流程
     *
     * @param workflowName 流程名称
     * @param steps        验证步骤
     */
    protected void verifyWorkflow(String workflowName, Runnable... steps) {
        logTestInfo("开始验证业务流程: " + workflowName);
        for (int i = 0; i < steps.length; i++) {
            logTestInfo("执行流程步骤 " + (i + 1) + "/" + steps.length);
            steps[i].run();
        }
        logTestInfo("业务流程验证完成: " + workflowName);
    }

    /**
     * 验证数据一致性
     *
     * @param description       验证描述
     * @param verificationLogic 验证逻辑
     */
    protected void verifyDataConsistency(String description, Runnable verificationLogic) {
        logTestInfo("验证数据一致性: " + description);
        verificationLogic.run();
        logTestInfo("数据一致性验证通过: " + description);
    }

    // ==================== 性能测试工具方法 ====================

    /**
     * 测量操作执行时间
     *
     * @param operationName 操作名称
     * @param operation     操作逻辑
     * @return 执行时间（毫秒）
     */
    protected long measureExecutionTime(String operationName, Runnable operation) {
        logTestInfo("开始测量操作执行时间: " + operationName);
        long startTime = System.currentTimeMillis();
        operation.run();
        long executionTime = System.currentTimeMillis() - startTime;
        logTestInfo("操作执行时间: " + operationName + " = " + executionTime + "ms");
        return executionTime;
    }

    /**
     * 验证操作性能
     *
     * @param operationName    操作名称
     * @param operation        操作逻辑
     * @param maxAllowedTimeMs 最大允许时间（毫秒）
     */
    protected void assertPerformance(String operationName, Runnable operation, long maxAllowedTimeMs) {
        long executionTime = measureExecutionTime(operationName, operation);
        if (executionTime > maxAllowedTimeMs) {
            throw new AssertionError("操作 " + operationName + " 执行时间 " + executionTime +
                    "ms 超过了最大允许时间 " + maxAllowedTimeMs + "ms");
        }
    }

    // ==================== 清理方法 ====================

    /**
     * 集成测试的清理工作
     */
    @Override
    protected void cleanupTestData() {
        super.cleanupTestData();
        cleanupIntegrationTestData();
    }

    /**
     * 清理集成测试特有的数据
     */
    protected void cleanupIntegrationTestData() {
        logTestInfo("清理集成测试数据");
        // 子类可以实现特定的清理逻辑
    }
}
