package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;


public class UserServiceTest extends BaseTest {
    @Autowired
    private UserService userService;

    @Test
    public void queryUserByWorkNoList() {
        var result = this.userService.queryUserByWorkNoList(List.of(DEFAULT_WORKER_ID));
        assertThat(result.getData().get(DEFAULT_WORKER_ID).getEmpId()).isEqualTo(DEFAULT_WORKER_ID);
    }
}
