package com.taobao.wireless.orange.external

import com.taobao.wireless.orange.BaseTest
import com.taobao.wireless.orange.external.oss.OssService
import org.assertj.core.api.Assertions
import org.assertj.core.api.ThrowableAssert.ThrowingCallable
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value

class OssServiceTest : BaseTest() {
    @Autowired
    private val ossService: OssService? = null

    @Value("\${orange.oss.bucketName}")
    private val bucketName: String? = null

    @Test
    fun putAndGetTest() {
        val testData = "test data content for OSS upload and read test"
        val objectName = "test.txt"

        Assertions.assertThatCode(ThrowingCallable {
            // 上传数据
            ossService!!.uploadData(testData.toByteArray(), objectName)

            // 从OSS读取数据
            val readData = ossService.readData(objectName)
            val readDataString = String(readData)

            Assertions.assertThat(readDataString)
                .`as`("读取的数据应该与上传的数据完全一致")
                .isNotNull()
                .isNotEmpty()
                .isEqualTo(testData)

            Assertions.assertThat(readData)
                .`as`("读取的字节数组长度应该与原始数据长度一致")
                .hasSize(testData.toByteArray().size)
            logTestInfo("OSS putAndGet 测试成功: $objectName")
        }).doesNotThrowAnyException()
    }
}