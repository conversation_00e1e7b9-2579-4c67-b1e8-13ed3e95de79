package com.taobao.wireless.orange.switchdal;

import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.text.dal.dao.ProbeTaskDAO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskDO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;


/**
 * ProbeTaskMapper测试类
 *
 * <AUTHOR>
 */
public class ProbeTaskMapperTest extends BaseTest {

    @Autowired
    private ProbeTaskDAO probeTaskDAO;

    @Test
    public void testGetAgatewareCandidateProbeTasks() {
        List<ProbeTaskDO> result = probeTaskDAO.getAgatewareCandidateProbeTasks();
        result.forEach(probeTaskDO -> {
            assertThat(probeTaskDO.getAppKey()).isNotNull();
            assertThat(probeTaskDO.getIsEmergent()).isNotNull();
        });
    }
}