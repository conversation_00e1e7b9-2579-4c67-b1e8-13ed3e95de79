package com.taobao.wireless.orange.exception;


import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class CommonExceptionTest {

    @Test
    public void testReplace() {
        // Test single placeholder replacement
        ExceptionEnum exceptionEnum = ExceptionEnum.PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_MATCH;
        CommonException exception = CommonException.getDynamicException(exceptionEnum, "myKey");
        assertThat(exception.getMsg()).isEqualTo("参数[key=myKey]已经有新的版本");

        // Test multiple placeholders replacement
        ExceptionEnum multiPlaceholderEnum = ExceptionEnum.PARAMETER_CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH;
        CommonException multiException = CommonException.getDynamicException(multiPlaceholderEnum, "paramKey", "conditionName");
        assertThat(multiException.getMsg()).isEqualTo("参数[key=paramKey]条件[name=conditionName]已经有新的版本");

        // Test replacement with null parameter
        CommonException nullException = CommonException.getDynamicException(exceptionEnum);
        assertThat(nullException.getMsg()).isEqualTo("参数[key={}]已经有新的版本");

        // Test replacement with empty string
        CommonException emptyException = CommonException.getDynamicException(exceptionEnum, (Object) null);
        assertThat(emptyException.getMsg()).isEqualTo("参数[key=]已经有新的版本");

        // Test replacement with numeric parameter
        CommonException numericException = CommonException.getDynamicException(exceptionEnum, 123);
        assertThat(numericException.getMsg()).isEqualTo("参数[key=123]已经有新的版本");

        // Test no replacement needed (no placeholders)
        ExceptionEnum noPlaceholderEnum = ExceptionEnum.PARAM_INVALID;
        CommonException noPlaceholderException = CommonException.getDynamicException(noPlaceholderEnum);
        assertThat(noPlaceholderException.getMsg()).isEqualTo("入参不合法");
    }

    @Test
    public void testConstructorWithExceptionEnum() {
        // Given
        ExceptionEnum exceptionEnum = ExceptionEnum.PARAM_INVALID;

        // When
        CommonException exception = new CommonException(exceptionEnum);

        // Then
        assertThat(exception.getCode()).isEqualTo("A68001");
        assertThat(exception.getMsg()).isEqualTo("入参不合法");
        assertThat(exception.getMessage()).isEqualTo("入参不合法");
    }

    @Test
    public void testConstructorWithExceptionEnumAndMessage() {
        // Given
        ExceptionEnum exceptionEnum = ExceptionEnum.NAMESPACE_NOT_EXIST;
        String customMessage = "Custom error message";

        // When
        CommonException exception = new CommonException(exceptionEnum, customMessage);

        // Then
        assertThat(exception.getCode()).isEqualTo("A01001");
        assertThat(exception.getMsg()).isEqualTo(customMessage);
        assertThat(exception.getMessage()).isEqualTo(customMessage);
    }

    @Test
    public void testConstructorWithExceptionEnumMessageAndCause() {
        // Given
        ExceptionEnum exceptionEnum = ExceptionEnum.PARAMETER_NOT_FOUND;
        String customMessage = "Custom error message";
        Throwable cause = new RuntimeException("Root cause");

        // When
        CommonException exception = new CommonException(exceptionEnum, customMessage, cause);

        // Then
        assertThat(exception.getCode()).isEqualTo("A02001");
        assertThat(exception.getMsg()).isEqualTo(customMessage);
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    public void testConstructorWithExceptionEnumAndCause() {
        // Given
        ExceptionEnum exceptionEnum = ExceptionEnum.CONDITION_NOT_FOUND;
        Throwable cause = new RuntimeException("Root cause");

        // When
        CommonException exception = new CommonException(exceptionEnum, cause);

        // Then
        assertThat(exception.getCode()).isEqualTo("A03002");
        assertThat(exception.getMsg()).isEqualTo("条件不存在");
        assertThat(exception.getCause()).isEqualTo(cause);
    }

    @Test
    public void testGetDynamicExceptionWithSinglePlaceholder() {
        // Given
        ExceptionEnum exceptionEnum = ExceptionEnum.PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_MATCH;

        // When
        CommonException exception = CommonException.getDynamicException(exceptionEnum, "testKey");

        // Then
        assertThat(exception.getCode()).isEqualTo("A02004");
        assertThat(exception.getMsg()).isEqualTo("参数[key=testKey]已经有新的版本");
    }

    @Test
    public void testGetDynamicExceptionWithMultiplePlaceholders() {
        // Given
        ExceptionEnum exceptionEnum = ExceptionEnum.PARAMETER_CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH;

        // When
        CommonException exception = CommonException.getDynamicException(exceptionEnum, "testKey", "testCondition");

        // Then
        assertThat(exception.getCode()).isEqualTo("A02005");
        assertThat(exception.getMsg()).isEqualTo("参数[key=testKey]条件[name=testCondition]已经有新的版本");
    }

    @Test
    public void testGetDynamicExceptionWithNullParameter() {
        // Given
        ExceptionEnum exceptionEnum = ExceptionEnum.PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_MATCH;

        // When
        CommonException exception = CommonException.getDynamicException(exceptionEnum, (Object) null);

        // Then
        assertThat(exception.getCode()).isEqualTo("A02004");
        assertThat(exception.getMsg()).isEqualTo("参数[key=]已经有新的版本");
    }

    @Test
    public void testGetDynamicExceptionWithNoParameters() {
        // Given
        ExceptionEnum exceptionEnum = ExceptionEnum.PARAM_INVALID;

        // When
        CommonException exception = CommonException.getDynamicException(exceptionEnum);

        // Then
        assertThat(exception.getCode()).isEqualTo("A68001");
        assertThat(exception.getMsg()).isEqualTo("入参不合法");
    }
}