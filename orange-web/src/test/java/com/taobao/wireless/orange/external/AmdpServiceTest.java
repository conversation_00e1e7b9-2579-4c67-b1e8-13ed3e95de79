package com.taobao.wireless.orange.external;


import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.external.amdp.AmdpService;
import com.taobao.wireless.orange.external.amdp.model.User;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

public class AmdpServiceTest extends BaseTest {
    @Autowired
    private AmdpService amdpService;

    @Test
    public void queryUserByWorkNoList() {
        Map<String, User> stringUserMap = this.amdpService.queryUserByWorkNoList(Arrays.asList(DEFAULT_WORKER_ID));
        assertThat(stringUserMap.get(DEFAULT_WORKER_ID))
                .isNotNull()
                .extracting(User::getEmpId)
                .isEqualTo(DEFAULT_WORKER_ID);
    }
}