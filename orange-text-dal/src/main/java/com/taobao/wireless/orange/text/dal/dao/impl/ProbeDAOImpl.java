package com.taobao.wireless.orange.text.dal.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.text.dal.dao.ProbeDAO;
import com.taobao.wireless.orange.text.dal.entity.ProbeDO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskExt;
import com.taobao.wireless.orange.text.dal.mapper.ProbeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 探针描述 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class ProbeDAOImpl extends ServiceImpl<ProbeMapper, ProbeDO> implements ProbeDAO {

    @Override
    public List<ProbeTaskExt> getAvailableProbesWithMaxTaskID(List<String> appKeys) {
        return baseMapper.getAvailableProbesWithMaxTaskID(appKeys);
    }
}
