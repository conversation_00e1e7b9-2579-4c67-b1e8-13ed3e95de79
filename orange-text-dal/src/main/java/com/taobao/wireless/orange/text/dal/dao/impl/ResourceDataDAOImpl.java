package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.ResourceDataDO;
import com.taobao.wireless.orange.text.dal.mapper.ResourceDataMapper;
import com.taobao.wireless.orange.text.dal.dao.ResourceDataDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 配置中心的数据，在数据库中存一份 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class ResourceDataDAOImpl extends ServiceImpl<ResourceDataMapper, ResourceDataDO> implements ResourceDataDAO {

}
