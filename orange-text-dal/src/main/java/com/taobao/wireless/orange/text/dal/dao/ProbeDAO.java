package com.taobao.wireless.orange.text.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.text.dal.entity.ProbeDO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskExt;

import java.util.List;

/**
 * <p>
 * 探针描述 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface ProbeDAO extends IService<ProbeDO> {
    /**
     * 获取有效的探针以及对应的最大任务ID
     *
     * @param appKeys
     * @return
     */
    List<ProbeTaskExt> getAvailableProbesWithMaxTaskID(List<String> appKeys);
}
