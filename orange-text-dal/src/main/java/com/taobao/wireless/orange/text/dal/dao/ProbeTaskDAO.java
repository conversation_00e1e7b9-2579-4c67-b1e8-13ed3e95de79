package com.taobao.wireless.orange.text.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskDO;

import java.util.List;

/**
 * <p>
 * 探针推送任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface ProbeTaskDAO extends IService<ProbeTaskDO> {

    /**
     * 获取 agateware 探针任务的候选应用列表
     *
     * @return
     */
    List<ProbeTaskDO> getAgatewareCandidateProbeTasks();

}
