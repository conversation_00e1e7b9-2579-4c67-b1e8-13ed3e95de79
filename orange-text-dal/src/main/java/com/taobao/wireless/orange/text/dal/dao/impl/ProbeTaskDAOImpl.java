package com.taobao.wireless.orange.text.dal.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.text.dal.dao.ProbeTaskDAO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskDO;
import com.taobao.wireless.orange.text.dal.mapper.ProbeTaskMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 探针推送任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class ProbeTaskDAOImpl extends ServiceImpl<ProbeTaskMapper, ProbeTaskDO> implements ProbeTaskDAO {

    @Override
    public List<ProbeTaskDO> getAgatewareCandidateProbeTasks() {
        return baseMapper.getAgatewareCandidateProbeTasks();
    }
}
