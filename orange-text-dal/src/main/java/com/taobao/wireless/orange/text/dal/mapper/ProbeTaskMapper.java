package com.taobao.wireless.orange.text.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 探针推送任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Mapper
public interface ProbeTaskMapper extends BaseMapper<ProbeTaskDO> {

    @Select("SELECT app_key, max(`is_emergent`) is_emergent FROM orange_probe_task " +
            "WHERE is_available = 'n' " +
            "AND agateware_task_id IS NULL " +
            "AND app_key IN (SELECT DISTINCT app_key FROM orange_probe) " +
            "GROUP BY app_key")
    List<ProbeTaskDO> getAgatewareCandidateProbeTasks();
}
