package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.DataDO;
import com.taobao.wireless.orange.text.dal.mapper.DataMapper;
import com.taobao.wireless.orange.text.dal.dao.DataDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 杂七杂八的数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class DataDAOImpl extends ServiceImpl<DataMapper, DataDO> implements DataDAO {

}
