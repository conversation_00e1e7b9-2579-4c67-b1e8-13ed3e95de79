package com.taobao.wireless.orange.text.dal.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.text.dal.dao.NamespaceDAO;
import com.taobao.wireless.orange.text.dal.entity.NamespaceDO;
import com.taobao.wireless.orange.text.dal.mapper.NamespaceMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 所有的命名空间定义 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class NamespaceDAOImpl extends ServiceImpl<NamespaceMapper, NamespaceDO> implements NamespaceDAO {
}
