package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.AckNamespaceVersionIncDailyDO;
import com.taobao.wireless.orange.text.dal.mapper.AckNamespaceVersionIncDailyMapper;
import com.taobao.wireless.orange.text.dal.dao.AckNamespaceVersionIncDailyDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布单每日增量统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class AckNamespaceVersionIncDailyDAOImpl extends ServiceImpl<AckNamespaceVersionIncDailyMapper, AckNamespaceVersionIncDailyDO> implements AckNamespaceVersionIncDailyDAO {

}
