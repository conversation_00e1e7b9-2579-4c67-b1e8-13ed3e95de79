package com.taobao.wireless.orange.text.dal.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.Available;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 活跃用户任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_active_user_task")
public class ActiveUserTaskDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 是否可用
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * namespace_id
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 发布版本
     */
    @TableField("version")
    private String version;

    /**
     * 任务开始时间
     */
    @TableField("gmt_begin")
    private Date gmtBegin;

    /**
     * 下一个周期
     */
    @TableField("next_duration")
    private Long nextDuration;
}
