package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.NamespacePackageDO;
import com.taobao.wireless.orange.text.dal.mapper.NamespacePackageMapper;
import com.taobao.wireless.orange.text.dal.dao.NamespacePackageDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 聚合配置映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class NamespacePackageDAOImpl extends ServiceImpl<NamespacePackageMapper, NamespacePackageDO> implements NamespacePackageDAO {

}
