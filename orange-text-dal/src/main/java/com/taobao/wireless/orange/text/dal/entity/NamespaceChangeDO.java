package com.taobao.wireless.orange.text.dal.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.Available;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配置变更表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_namespace_change")
public class NamespaceChangeDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 是否有效
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * version表的版本
     */
    @TableField("version_version")
    private String versionVersion;

    /**
     * 变更ID
     */
    @TableField("change_version")
    private String changeVersion;

    /**
     * 配置ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * namespace
     */
    @TableField("name")
    private String name;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 版本列表，逗号分隔
     */
    @TableField("versions")
    private String versions;

    /**
     * 完整描述
     */
    @TableField("metas")
    private String metas;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 操作名称
     */
    @TableField("type")
    private Integer type;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 加载级别
     */
    @TableField("load_level")
    private Integer loadLevel;
}
