package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.AckDataDO;
import com.taobao.wireless.orange.text.dal.mapper.AckDataMapper;
import com.taobao.wireless.orange.text.dal.dao.AckDataDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 配置内容拉取到的ack 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class AckDataDAOImpl extends ServiceImpl<AckDataMapper, AckDataDO> implements AckDataDAO {

}
