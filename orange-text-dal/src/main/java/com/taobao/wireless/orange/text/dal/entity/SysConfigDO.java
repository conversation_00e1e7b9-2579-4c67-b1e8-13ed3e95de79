package com.taobao.wireless.orange.text.dal.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.Available;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * orange系统配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_sys_config")
public class SysConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 是否有效
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * 配置code
     */
    @TableField("code")
    private String code;

    /**
     * 配置子code
     */
    @TableField("sub_code")
    private String subCode;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 配置项key
     */
    @TableField("config_key")
    private String configKey;
}
