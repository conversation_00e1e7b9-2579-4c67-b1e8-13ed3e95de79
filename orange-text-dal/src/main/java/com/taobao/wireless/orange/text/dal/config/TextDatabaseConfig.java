package com.taobao.wireless.orange.text.dal.config;


import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.taobao.tddl.group.jdbc.TGroupDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@MapperScan(value = "com.taobao.wireless.orange.text.dal.mapper", sqlSessionFactoryRef = "orangeTextFactory")
public class TextDatabaseConfig {

    @Bean(name = "orangeTextDataSource")
    public DataSource dataSource(@Value("${spring.text.tddl.app}") String tddlApp,
                                 @Value("${spring.text.tddl.group}") String tddlGroup) {
        TGroupDataSource dataSource = new TGroupDataSource();
        dataSource.setAppName(tddlApp);
        dataSource.setDbGroupKey(tddlGroup);
        dataSource.init();
        return dataSource;
    }

    @Bean(name = "orangeTextFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("orangeTextDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setTypeAliasesPackage("com.taobao.wireless.orange.text.dal.model");
        sqlSessionFactoryBean.setMapperLocations(new org.springframework.core.io.support.PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        return sqlSessionFactoryBean.getObject();
    }

    @Bean("orangeTextSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("orangeTextFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
