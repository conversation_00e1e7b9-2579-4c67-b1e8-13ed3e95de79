package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.ActiveUserTaskDO;
import com.taobao.wireless.orange.text.dal.mapper.ActiveUserTaskMapper;
import com.taobao.wireless.orange.text.dal.dao.ActiveUserTaskDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活跃用户任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class ActiveUserTaskDAOImpl extends ServiceImpl<ActiveUserTaskMapper, ActiveUserTaskDO> implements ActiveUserTaskDAO {

}
