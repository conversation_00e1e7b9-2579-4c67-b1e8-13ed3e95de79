package com.taobao.wireless.orange.text.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.taobao.wireless.orange.text.dal.entity.ProbeDO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 探针描述 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Mapper
public interface ProbeMapper extends BaseMapper<ProbeDO> {

    List<ProbeTaskExt> getAvailableProbesWithMaxTaskID(@Param("appKeys") List<String> appKeys);
}