package com.taobao.wireless.orange.text.dal.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.Available;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * app版本列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_app_version")
public class AppVersionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * appkey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * appversion
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * is_available
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * app_version类型
     */
    @TableField("type")
    private String type;

    /**
     * 废弃
     */
    @TableField("app_version_code")
    private Long appVersionCode;

    /**
     * app_version的字符串对齐形态
     */
    @TableField("app_version_value")
    private String appVersionValue;
}
