package com.taobao.wireless.orange.text.dal.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.Available;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配置中心的数据，在数据库中存一份
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_resource_data")
public class ResourceDataDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * is_available
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * resourceId
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * data
     */
    @TableField("data")
    private String data;

    /**
     * MD5
     */
    @TableField("md5")
    private String md5;

    /**
     * 原始的数据
     */
    @TableField("src_content")
    private String srcContent;

    /**
     * 废弃
     */
    @TableField("from_id")
    private String fromId;

    /**
     * 附加参数1
     */
    @TableField("arg1")
    private String arg1;

    /**
     * 附加参数2
     */
    @TableField("arg2")
    private String arg2;

    /**
     * 存储内容类型：1-全量索引，2-差量索引，3-发布的配置，4-beta灰度临时配置
     */
    @TableField("type")
    private Integer type;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 多场景内容数据
     */
    @TableField("scenes_contents")
    private String scenesContents;

    /**
     * 配置内容的 MD5
     */
    @TableField("content_md5")
    private String contentMd5;
}
