package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.AppVersionDO;
import com.taobao.wireless.orange.text.dal.mapper.AppVersionMapper;
import com.taobao.wireless.orange.text.dal.dao.AppVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * app版本列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class AppVersionDAOImpl extends ServiceImpl<AppVersionMapper, AppVersionDO> implements AppVersionDAO {

}
