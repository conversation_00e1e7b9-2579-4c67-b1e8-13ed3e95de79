package com.taobao.wireless.orange.text.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 命名空间版本详情
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_namespace_version")
public class NamespaceVersionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 命名空间的id
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 版本
     */
    @TableField("version")
    private String version;

    /**
     * 内容
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 属于哪个appkey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 属于哪个app版本
     */
    @TableField("app_version")
    private String appVersion;

    /**
     * is_available
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * status
     */
    @TableField("status")
    private Integer status;

    /**
     * 资源文件的md5
     */
    @TableField("md5")
    private String md5;

    /**
     * 加载级别
     */
    @TableField("load_level")
    private Integer loadLevel;

    /**
     * 创建来源
     */
    @TableField("source")
    private Integer source;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 审核人
     */
    @TableField("reviewer")
    private String reviewer;

    /**
     * 预审核人
     */
    @TableField("pre_reviewers")
    private String preReviewers;

    /**
     * 创建来源传来的数据
     */
    @TableField("source_data")
    private String sourceData;

    /**
     * 上一个版本的资源
     */
    @TableField("previous_resource_id")
    private String previousResourceId;

    /**
     * 发布时间
     */
    @TableField("gmt_publish")
    private Date gmtPublish;

    /**
     * 谁将这行记录设置为不可用
     */
    @TableField("deleter")
    private String deleter;

    /**
     * 更新策略表达式
     */
    @TableField("strategy")
    private String strategy;

    /**
     * 指定覆盖的策略版本
     */
    @TableField("overwrite_strategy_versions")
    private String overwriteStrategyVersions;

    /**
     * 是否是紧急发布
     */
    @TableField("is_emergent")
    private Emergent isEmergent;

    /**
     * 有序的剩余版本
     */
    @TableField("versions")
    private String versions;

    /**
     * 所有下线版本
     */
    @TableField("offlines")
    private String offlines;

    /**
     * 灰度版本列表
     */
    @TableField("gray_versions")
    private String grayVersions;

    /**
     * 配置内容的 MD5
     */
    @TableField("content_md5")
    private String contentMd5;
}
