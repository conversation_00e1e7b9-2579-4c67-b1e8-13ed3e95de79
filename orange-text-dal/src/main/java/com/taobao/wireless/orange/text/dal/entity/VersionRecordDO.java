package com.taobao.wireless.orange.text.dal.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.Available;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 版本操作记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_version_record")
public class VersionRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 是否有效
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * Namespace
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 版本
     */
    @TableField("version")
    private String version;

    /**
     * 操作类型:1- 申请发布; 2-Beta 灰度; 3-定量灰度; 4-审核; 10-全量;
     */
    @TableField("type")
    private Integer type;

    /**
     * 搜索参数
     */
    @TableField("arg0")
    private String arg0;

    /**
     * 补充说明
     */
    @TableField("attribute")
    private String attribute;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 状态 200-OK
     */
    @TableField("status")
    private Integer status;

    /**
     * 输入参数(JSON)
     */
    @TableField("params")
    private String params;

    /**
     * 结果记录(JSON)
     */
    @TableField("result")
    private String result;

    /**
     * 检测卡口结果(JSON)
     */
    @TableField("check_result")
    private String checkResult;

    /**
     * appKey
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 版本，默认v1
     */
    @TableField("ver")
    private Integer ver;

    /**
     * 最后修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
