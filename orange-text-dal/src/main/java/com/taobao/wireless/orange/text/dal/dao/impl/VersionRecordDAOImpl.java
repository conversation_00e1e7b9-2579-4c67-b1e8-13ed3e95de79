package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.VersionRecordDO;
import com.taobao.wireless.orange.text.dal.mapper.VersionRecordMapper;
import com.taobao.wireless.orange.text.dal.dao.VersionRecordDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 版本操作记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class VersionRecordDAOImpl extends ServiceImpl<VersionRecordMapper, VersionRecordDO> implements VersionRecordDAO {

}
