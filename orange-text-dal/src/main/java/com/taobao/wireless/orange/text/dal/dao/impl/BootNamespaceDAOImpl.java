package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.BootNamespaceDO;
import com.taobao.wireless.orange.text.dal.mapper.BootNamespaceMapper;
import com.taobao.wireless.orange.text.dal.dao.BootNamespaceDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 启动链路依赖的 namespace 清单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class BootNamespaceDAOImpl extends ServiceImpl<BootNamespaceMapper, BootNamespaceDO> implements BootNamespaceDAO {

}
