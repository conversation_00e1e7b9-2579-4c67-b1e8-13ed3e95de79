package com.taobao.wireless.orange.text.dal.dao.impl;

import com.taobao.wireless.orange.text.dal.entity.AckNamespaceVersionInc15mDO;
import com.taobao.wireless.orange.text.dal.mapper.AckNamespaceVersionInc15mMapper;
import com.taobao.wireless.orange.text.dal.dao.AckNamespaceVersionInc15mDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布单每15分钟的增量ack数量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class AckNamespaceVersionInc15mDAOImpl extends ServiceImpl<AckNamespaceVersionInc15mMapper, AckNamespaceVersionInc15mDO> implements AckNamespaceVersionInc15mDAO {

}
