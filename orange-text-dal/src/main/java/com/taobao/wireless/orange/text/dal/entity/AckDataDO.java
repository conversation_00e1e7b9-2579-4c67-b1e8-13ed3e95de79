package com.taobao.wireless.orange.text.dal.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 配置内容拉取到的ack
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Getter
@Setter
@TableName("orange_ack_data")
public class AckDataDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 发布单的版本
namespaceVersion
     */
    @TableField("nv")
    private String nv;

    /**
     * ack设备数量(去重的)
     */
    @TableField("did_cnt")
    private Long didCnt;
}
