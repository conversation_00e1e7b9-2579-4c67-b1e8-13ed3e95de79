## 目录结构
解压后生成以下两个子目录

* orange-service，包含各中间件的使用示例代码，代码在src/main/java目录下的com.taobao.wireless包中。
* orange-start，包含启动类`com.taobao.wireless.orange.web.Application`。中间件使用示例的单元测试代码在`src/test/java`目录下的`com.taobao.wireless`包中。日志配置文件为`src/main/resources`目录下的logback-spring.xml。
* 使用springmvc的代码在`src/main/java`目录下的`com.taobao.wireless`包中。
* autoconfig的配置在`src/main/resources/META-INF/autoconf/auto-config.xml`中，属性文件antx.properties在根目录下。

## 使用方式
### 在开发工具中执行
将工程导入eclipse或者idea后，直接执行包含main方法的类`com.taobao.wireless.orange.web.Application`。

### 使用fat jar的方式
这也是pandora boot应用发布的方式。首先执行下列命令打包

```sh
mvn package
```

如果选择了auto-config，可在命令后加

```sh
-Dautoconfig.userProperties={fullPath}/bootstrap-start/antx.properties
```

通过-D参数指定antx.properties的位置，否则会进入autoconfig的交互模式

然后进入`orange-start/target`目录，执行fat jar

```sh
java -Dpandora.location=${sar} -jar orange-start-1.0.0.jar
```

其中${sar}为sar包的路径

### 使用 SarLauncher

这也是pandora boot应用发布到服务器上启动的方式。首先执行下列命令打包

```sh
mvn clean package -DskipTests
```

如果选择了auto-config，可在命令后加

```sh
-Dautoconfig.userProperties=orange-start/antx.properties
```

通过-D参数指定antx.properties的位置，否则会进入autoconfig的交互模式

打包之后，会有一个 unzip 日志，比如：

```
main:
[unzip] Expanding: /private/tmp/orange/orange-start/target/orange-start-1.0.0.jar into /private/tmp/orange/orange-start/target/orange
```

进入到unzip的目录，启动SarLauncher：

```
cd orange-start/target/orange/

java -Dpandora.location=${sar} com.taobao.pandora.boot.loader.SarLauncher
```

其中${sar}为sar包的路径

### 通过mvn命令直接启动
第一次调用前先要执行

```sh
mvn install
```

如果maven工程的Artifact，group id，version等都未变化，只需执行一次即可。

然后直接通过命令执行start子工程

```sh
mvn -pl orange-start pandora-boot:run
```

以上两个命令，如果选择了auto-config，可在命令后加

```sh
-Dautoconfig.userProperties={fullPath}/orange-start/antx.properties
```

通过-D参数指定antx.properties的位置，否则会进入autoconfig的交互模式properties的位置

## 升级指南

* https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/changelog

## Docker 模板

* APP-META 目录里
* https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/docker

## aone发布
请参考文档 https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/aone-guide

## 用户提示
* 启动工程报错：`-Djps.track.ap.dependencies=false`,可以参考：https://gitlab.alibaba-inc.com/middleware-container/bootstrap/issues/118812
* jdk17应用本地IDEA启动要添加相应的JVM参数，例如：
```
--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/sun.net.util=ALL-UNNAMED
```
* jdk17应用中间件的单元测试要添加相应的JVM参数，例如工程根目录pom文件中的maven-surefire-plugin插件添加了JVM参数

## 相关链接
### Pandora Boot
* 钉钉交流群 ： 11701173
* wiki ： https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/home
* FAQ: https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/faq

### 开发者应用中心
* 线上 ： https://start.alibaba-inc.com
* 日常 ： https://start.taobao.net
* 文档 ： https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/application-center

### Logger配置

* logger配置: https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/log-config

### Docker相关链接
* 如果工程有docker模板，目录是 APP-META，docker模板的说明文件是：APP-META/README.md
* docker参考说明：https://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/docker

### 构建工具:amaven(默认启用)
* https://ata.alibaba-inc.com/articles/243464
* https://ata.alibaba-inc.com/articles/175972

### maven插件
error-prone(默认全局启用),jacoco-maven-plugin(默认全局启用),maven-surefire-plugin
* https://yuque.antfin.com/jh374881/ynknvt/oe6t7m
* https://yuque.antfin.com/jh374881/ynknvt/ycd83p

### JVM参数
* https://yuque.antfin.com/aone355606/migration/from-8-to-11#neuYz
### HSF
* gitbook ： http://mw.alibaba-inc.com/products/hsf/_book/
* wiki ： http://gitlab.alibaba-inc.com/middleware/hsf2-0/wikis/home
* HSF用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf

### TDDL
* gitbook ： http://mw.alibaba-inc.com/products/tddl/_book/
* wiki ： http://gitlab.alibaba-inc.com/middleware/tddl5-wiki/wikis/home
* TDDL用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tddl

### Diamond
* gitbook ： http://mw.alibaba-inc.com/products/diamondserver/_book/
* wiki ： http://gitlab.alibaba-inc.com/middleware/diamond/wikis/home
* diamond用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-diamond

### Tair
* 文档中心 ： http://baike.corp.taobao.com/index.php/CS_RD/tair
* tair用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tair

### Notify
* 文档中心 ： http://mw.alibaba-inc.com/products/notify/_book/index.html
* 用户文档 ： http://baike.corp.taobao.com/index.php/Notify
* notify用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-notify


### EagleEye
* gitbook ： http://mw.alibaba-inc.com/products/eagleeye/_book/index.html
* 用户文档 ： http://tbdocs.alibaba-inc.com/display/HSF/EagleEye
* eagleeye用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-eagleeye








  
本仓库于 2024-12-05 17:14:19 使用了源码自动生成模板 pandora-boot-initializr 。详情见template_info.md文件。
