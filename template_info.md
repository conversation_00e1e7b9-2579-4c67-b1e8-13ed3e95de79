# 源码自动生成模板 pandora-boot-initializr

### 概述

* 模板: pandora-boot-initializr
* 模板使用时间: 2024-12-05 17:14:19

### Docker
* Image: reg.docker.alibaba-inc.com/bootstrap2/pandora-boot
* Tag: 0.2
* SHA256: eb7881697d25310df646b4a2a4cf9bc53392274e2812eca4a8dffa289654c95c

### 用户输入参数
* bootVersion: "3.2.5" 
* repoUrl: "**************************:orange/orange-be.git" 
* javaVersion: "21" 
* appName: "orange" 
* groupId: "com.taobao.wireless" 
* dockerfile: "dockerfile" 
* artifactId: "orange" 
* style: "hsf,metaq,tddl,diamond,tair,notify,eagleeye,autoconfig" 
* sarVersion: "2025-06-release-fix-hsf-jdk21-SNAPSHOT" 
* operator: "149016" 

### 上下文参数
* appName: orange
* operator: 149016
* gitUrl: **************************:orange/orange-be.git
* branch: master


### 命令行
	sudo docker run --rm -v /home/<USER>/71_20241205171409325_551305908_code/orange/1733390048759_orange:/workspace -e bootVersion="3.2.5" -e repoUrl="**************************:orange/orange-be.git" -e javaVersion="21" -e appName="orange" -e groupId="com.taobao.wireless" -e dockerfile="dockerfile" -e artifactId="orange" -e style="hsf,metaq,tddl,diamond,tair,notify,eagleeye,autoconfig" -e sarVersion="2025-06-release-fix-hsf-jdk21-SNAPSHOT" -e operator="149016"  reg.docker.alibaba-inc.com/bootstrap2/pandora-boot:0.2

