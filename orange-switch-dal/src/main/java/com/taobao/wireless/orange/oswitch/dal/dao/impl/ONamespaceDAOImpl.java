package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.common.constant.enums.NamespaceStatus;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.ONamespaceMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 命名空间表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
@Service
public class ONamespaceDAOImpl extends ServiceImpl<ONamespaceMapper, ONamespaceDO> implements ONamespaceDAO {

    @Override
    public ONamespaceDO getByNamespaceId(String namespaceId) {
        return this.lambdaQuery()
                .eq(ONamespaceDO::getNamespaceId, namespaceId)
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .one();
    }

    @Override
    public List<ONamespaceDO> getByNamespaceIds(List<String> namespaceIds) {
        if (CollectionUtils.isEmpty(namespaceIds)) {
            return List.of();
        }

        return this.lambdaQuery()
                .in(ONamespaceDO::getNamespaceId, namespaceIds)
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .list();
    }

    @Override
    public boolean updateByNamespaceId(ONamespaceDO namespaceDO) {
        return this.update(namespaceDO, new LambdaQueryWrapper<ONamespaceDO>().eq(ONamespaceDO::getNamespaceId, namespaceDO.getNamespaceId()));
    }
}
