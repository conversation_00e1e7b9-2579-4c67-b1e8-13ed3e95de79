package com.taobao.wireless.orange.oswitch.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 参数表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface OParameterDAO extends IService<OParameterDO> {

    List<OParameterDO> getParametersByParameterIds(List<String> parameterIds);

    Map<String, OParameterDO> getParameterMapByParameterIds(List<String> parameterIds);

    Map<String, Long> getCountGroupByNamespaceId(List<String> namespaceIds);
}
