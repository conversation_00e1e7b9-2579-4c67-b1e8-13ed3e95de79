package com.taobao.wireless.orange.oswitch.dal.config;


import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.taobao.tddl.group.jdbc.TGroupDataSource;
import com.taobao.wireless.orange.oswitch.dal.handler.CustomMetaObjectHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

@Configuration
@MapperScan(value = "com.taobao.wireless.orange.oswitch.dal.mapper", sqlSessionFactoryRef = "orangeSwitchFactory")
public class SwitchDatabaseConfig {
    @Primary
    @Bean(name = "orangeSwitchDataSource")
    public DataSource dataSource(@Value("${spring.switch.tddl.app}") String tddlApp,
                                 @Value("${spring.switch.tddl.group}") String tddlGroup) {
        TGroupDataSource dataSource = new TGroupDataSource();
        dataSource.setAppName(tddlApp);
        dataSource.setDbGroupKey(tddlGroup);
        dataSource.init();
        return dataSource;
    }

    @Primary
    @Bean(name = "orangeSwitchFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("orangeSwitchDataSource") DataSource dataSource,
                                               @Qualifier("customMetaObjectHandler") CustomMetaObjectHandler customMetaObjectHandler) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setTypeAliasesPackage("com.taobao.wireless.orange.oswitch.dal.model");
        sqlSessionFactoryBean.setTypeHandlersPackage("com.taobao.wireless.orange.oswitch.dal.handler");
        sqlSessionFactoryBean.setMapperLocations(new org.springframework.core.io.support.PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        // 插件设置（分页）
        sqlSessionFactoryBean.setPlugins(mybatisPlusInterceptor());
        // 设置自动填充处理器
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setMetaObjectHandler(customMetaObjectHandler);
        sqlSessionFactoryBean.setGlobalConfig(globalConfig);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean("orangeSwitchSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("orangeSwitchFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> configuration.setMapUnderscoreToCamelCase(true);
    }

    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}
