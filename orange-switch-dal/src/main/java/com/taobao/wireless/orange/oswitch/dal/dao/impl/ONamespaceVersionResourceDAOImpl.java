package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionResourceDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.ONamespaceVersionResourceMapper;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceVersionResourceDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 命名空间版本资源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Service
public class ONamespaceVersionResourceDAOImpl extends ServiceImpl<ONamespaceVersionResourceMapper, ONamespaceVersionResourceDO> implements ONamespaceVersionResourceDAO {

}
