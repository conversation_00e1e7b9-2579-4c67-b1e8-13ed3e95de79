package com.taobao.wireless.orange.oswitch.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;

import java.util.List;

/**
 * <p>
 * 命名空间表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15
 */
public interface ONamespaceDAO extends IService<ONamespaceDO> {
    ONamespaceDO getByNamespaceId(String namespaceId);
    List<ONamespaceDO> getByNamespaceIds(List<String> namespaceIds);
    boolean updateByNamespaceId(ONamespaceDO namespaceDO);
}
