package com.taobao.wireless.orange.oswitch.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.ConfigType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 命名空间版本资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Getter
@Setter
@TableName("o_namespace_version_resource")
public class ONamespaceVersionResourceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 类型
     */
    @TableField("type")
    private ConfigType type;

    /**
     * 产生本次内容对应的 change_version
     */
    @TableField("namespace_change_version")
    private String namespaceChangeVersion;

    /**
     * 是否最新版本
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * 内容资源 ID
     */
    @TableField("resource_id")
    private String resourceId;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
