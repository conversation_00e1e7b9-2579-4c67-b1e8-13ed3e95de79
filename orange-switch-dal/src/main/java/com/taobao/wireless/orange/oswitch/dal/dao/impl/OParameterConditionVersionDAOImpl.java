package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.taobao.wireless.orange.oswitch.dal.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.OParameterConditionVersionMapper;
import com.taobao.wireless.orange.oswitch.dal.dao.OParameterConditionVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 参数条件版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OParameterConditionVersionDAOImpl extends ServiceImpl<OParameterConditionVersionMapper, OParameterConditionVersionDO> implements OParameterConditionVersionDAO {

}
