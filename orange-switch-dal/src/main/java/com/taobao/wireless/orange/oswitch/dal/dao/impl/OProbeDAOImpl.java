package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.oswitch.dal.dao.OProbeDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OProbeDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.OProbeMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 探针表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OProbeDAOImpl extends ServiceImpl<OProbeMapper, OProbeDO> implements OProbeDAO {
}
