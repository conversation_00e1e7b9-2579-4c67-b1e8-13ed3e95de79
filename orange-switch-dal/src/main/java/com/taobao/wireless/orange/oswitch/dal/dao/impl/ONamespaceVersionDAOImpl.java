package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceVersionDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.ONamespaceVersionMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 命名空间版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class ONamespaceVersionDAOImpl extends ServiceImpl<ONamespaceVersionMapper, ONamespaceVersionDO> implements ONamespaceVersionDAO {

    @Override
    public List<ONamespaceVersionDO> getAvailableNamespaceVersionsByAppKey(String appKey) {
        return this.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.y)
                .list();
    }

    /**
     * 根据 changeVersionRange (左开右闭）区间查询有效的 namespaceVersion
     *
     * @param appKey
     * @param changeVersionRange
     * @return
     */
    @Override
    public List<ONamespaceVersionDO> getAvailableNamespaceVersionsByAppKey(String appKey, Pair<String, String> changeVersionRange) {
        return this.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.y)
                .gt(changeVersionRange.getLeft() != null, ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getLeft())
                .le(changeVersionRange.getRight() != null, ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getRight())
                .list();
    }

    @Override
    public ONamespaceVersionDO getAvailableNamespaceVersionByNamespaceId(String namespaceId) {
        return this.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.y)
                .oneOpt()
                .orElse(null);
    }

    @Override
    public void invalidateCurrentVersion(String namespaceId) {
        this.lambdaUpdate()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.y)
                .set(ONamespaceVersionDO::getIsAvailable, Available.n)
                .update();
    }
}
