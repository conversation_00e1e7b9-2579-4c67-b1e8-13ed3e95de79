package com.taobao.wireless.orange.oswitch.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.*;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 发布单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Getter
@Setter
@TableName("o_release_order")
public class OReleaseOrderDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 发布版本号
     */
    @TableField("release_version")
    private String releaseVersion;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 发布对象类型
     */
    @TableField("biz_type")
    private ReleaseOrderBizType bizType;

    /**
     * 发布对象ID
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 发布类型
     */
    @TableField("release_type")
    private ReleaseType releaseType;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态
     */
    @TableField("status")
    private ReleaseOrderStatus status;

    /**
     * 发布比例（十万分之一为单位）
     */
    @TableField("gray_ratio")
    private Integer grayRatio;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * Tiga 灰度任务ID
     */
    @TableField("tiga_task_id")
    private Long tigaTaskId;

    /**
     * Tiga 相关信息
     */
    @TableField("tiga_metadata")
    private String tigaMetadata;

    /**
     * 是否紧急发布
     */
    @TableField("is_emergent")
    private Emergent isEmergent;

    /**
     * 当前阶段类型
     */
    @TableField("current_stage_type")
    private StageType currentStageType;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TigaMetadata {
        private Long taskId;
        private Long templateId;
    }
}
