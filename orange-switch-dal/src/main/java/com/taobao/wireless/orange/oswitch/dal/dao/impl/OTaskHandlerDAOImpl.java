package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.common.constant.enums.TaskHandlerStatus;
import com.taobao.wireless.orange.oswitch.dal.dao.OTaskHandlerDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OTaskHandlerDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.OTaskHandlerMapper;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务处理人 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class OTaskHandlerDAOImpl extends ServiceImpl<OTaskHandlerMapper, OTaskHandlerDO> implements OTaskHandlerDAO {

    @Override
    public Map<TaskHandlerStatus, Long> getCountGroupByStatus(String userId) {
        QueryWrapper<OTaskHandlerDO> wrapper = new QueryWrapper<>();
        wrapper.select("status, count(*) as count")
                .eq("user_id", userId)
                .groupBy("status")
                .having("count(*) > 0");

        return this.baseMapper.selectMaps(wrapper).stream()
                .collect(Collectors.toMap(
                        map -> TaskHandlerStatus.valueOf(map.get("status").toString()), map -> Long.valueOf(map.get("count").toString())));
    }
}
