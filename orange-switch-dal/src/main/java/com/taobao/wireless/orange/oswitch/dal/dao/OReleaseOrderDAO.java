package com.taobao.wireless.orange.oswitch.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 发布单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
public interface OReleaseOrderDAO extends IService<OReleaseOrderDO> {

    OReleaseOrderDO getByReleaseVersion(String releaseVersion);

    Map<String, OReleaseOrderDO> getReleaseOrderMapByReleaseVersions(List<String> releaseVersions);

    Map<ReleaseOrderStatus, Long> getCountGroupByStatus(String namespaceId);
}
