package com.taobao.wireless.orange.oswitch.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 命名空间版本表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@TableName("o_namespace_version")
@Data
public class ONamespaceVersionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 命名空间ID
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 命名空间版本号
     */
    @TableField("namespace_version")
    private String namespaceVersion;

    /**
     * 命名空间变更版本号
     */
    @TableField("namespace_change_version")
    private String namespaceChangeVersion;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private NamespaceVersionChangeType changeType;

    /**
     * 发起变更的发布单版本号
     */
    @TableField("release_version")
    private String releaseVersion;

    /**
     * 是否最新版本
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * 第一次应用该变更的索引版本号
     */
    @TableField("index_version")
    private String indexVersion;

    /**
     * 是否紧急发布
     */
    @TableField("is_emergent")
    private Emergent isEmergent;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
