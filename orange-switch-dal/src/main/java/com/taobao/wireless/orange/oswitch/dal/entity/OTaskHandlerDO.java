package com.taobao.wireless.orange.oswitch.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.TaskHandlerStatus;
import com.taobao.wireless.orange.common.constant.enums.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 任务处理人
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Getter
@Setter
@TableName("o_task_handler")
public class OTaskHandlerDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 任务类型
     */
    @TableField("task_type")
    private TaskType taskType;

    /**
     * 处理人ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 处理状态
     */
    @TableField("status")
    private TaskHandlerStatus status;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;
}