package com.taobao.wireless.orange.oswitch.dal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.querys.MySqlQuery;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.baomidou.mybatisplus.generator.query.SQLQuery;
import org.apache.ibatis.type.JdbcType;

import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class CodeGenerator {

    public static void main(String[] args) {
        generateForDataSource(
                "****************************************************************************",
                "ORANGE_CONFIG_APP",
                "com.taobao.wireless.orange.oswitch.dal",
                "o_",
                "O%s"
        );
    }

    private static void generateForDataSource(String url, String username, String parentPackage, String tablePrefix, String nameFormat) {
        String outputDir = Paths.get(System.getProperty("user.dir")) + "/orange-switch-dal/src/main/java";

        FastAutoGenerator.create(url, username, "")
                .globalConfig(builder -> builder.disableOpenDir()
                        .author("guoqi.guoqi")
                        .dateType(DateType.ONLY_DATE)
                        .outputDir(outputDir))
                .dataSourceConfig(builder -> builder.databaseQueryClass(SQLQuery.class)
                        .typeConvert(new MySqlTypeConvert())
                        .typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            if (JdbcType.TINYINT == metaInfo.getJdbcType()) {
                                return DbColumnType.INTEGER;
                            }
                            return typeRegistry.getColumnType(metaInfo);
                        })
                        .dbQuery(new MySqlQuery())
                )
                .packageConfig(builder -> builder.parent(parentPackage) // 设置统一的父包名
                        .entity("entity")
                        .mapper("mapper")
                        .service("dao")
                        .serviceImpl("dao.impl")
                        .xml("mapper.xml")
                )
                .strategyConfig((scanner, builder) -> builder
                        .addInclude(getTables(scanner.apply("请输入表名，多个英文逗号分隔？所有输入 all")))
                        .addTablePrefix(tablePrefix)
                        // 禁用 controller
                        .controllerBuilder()
                        .disable()
                        // entity 配置
                        .entityBuilder()
                        .enableFileOverride()
                        .formatFileName(nameFormat + "DO") // 旧: %sDO or 新: O%sDO
                        .enableLombok()
                        .addTableFills(new Column("gmt_create", FieldFill.INSERT))
                        .addTableFills(new Column("gmt_modified", FieldFill.INSERT_UPDATE))
                        .addTableFills(new Column("creator", FieldFill.INSERT))
                        .addTableFills(new Column("modifier", FieldFill.INSERT_UPDATE))
                        .enableTableFieldAnnotation()
                        // service 配置
                        .serviceBuilder()
                        .enableFileOverride()
                        .formatServiceFileName(nameFormat + "DAO")
                        .formatServiceImplFileName(nameFormat + "DAOImpl")
                        // mapper 配置
                        .mapperBuilder()
                        .enableFileOverride()
                        .enableMapperAnnotation()
                        .formatMapperFileName(nameFormat + "Mapper") // 旧: %sMapper or 新: O%sMapper
                        .formatXmlFileName(nameFormat + "Mapper") // 旧: %sMapper.xml or 新: O%sMapper.xml
                )
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }

    protected static List<String> getTables(String tables) {
        return "all".equals(tables) ? Collections.emptyList() : Arrays.asList(tables.split(","));
    }
}
