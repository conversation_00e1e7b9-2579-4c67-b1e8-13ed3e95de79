package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.taobao.wireless.orange.common.constant.enums.StageType;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.OReleaseOrderStageMapper;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderStageDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布单阶段详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Service
public class OReleaseOrderStageDAOImpl extends ServiceImpl<OReleaseOrderStageMapper, OReleaseOrderStageDO> implements OReleaseOrderStageDAO {

    @Override
    public OReleaseOrderStageDO getByReleaseVersionAndType(String releaseVersion, StageType stageType) {
        return lambdaQuery()
                .eq(OReleaseOrderStageDO::getReleaseVersion, releaseVersion)
                .eq(OReleaseOrderStageDO::getType, stageType)
                .one();
    }
}
