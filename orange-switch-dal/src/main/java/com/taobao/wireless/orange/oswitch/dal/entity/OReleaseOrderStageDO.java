package com.taobao.wireless.orange.oswitch.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.StageStatus;
import com.taobao.wireless.orange.common.constant.enums.StageType;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 发布单阶段详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@TableName("o_release_order_stage")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OReleaseOrderStageDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * app key
     */
    @TableField("app_key")
    private String appKey;

    /**
     * namespace id
     */
    @TableField("namespace_id")
    private String namespaceId;

    /**
     * 发布单版本
     */
    @TableField("release_version")
    private String releaseVersion;

    /**
     * 阶段类型
     */
    @TableField("type")
    private StageType type;

    /**
     * 阶段状态
     */
    @TableField("status")
    private StageStatus status;

    /**
     * 阶段顺序(从0开始)
     */
    @TableField("stage_order")
    private Integer stageOrder;

    /**
     * 创建者
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改者
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;
}
