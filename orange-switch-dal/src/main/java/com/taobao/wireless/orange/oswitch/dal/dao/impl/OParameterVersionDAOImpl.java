package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.OParameterVersionMapper;
import com.taobao.wireless.orange.oswitch.dal.dao.OParameterVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 参数版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OParameterVersionDAOImpl extends ServiceImpl<OParameterVersionMapper, OParameterVersionDO> implements OParameterVersionDAO {

}
