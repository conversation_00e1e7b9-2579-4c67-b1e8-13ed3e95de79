package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderOperationDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.OReleaseOrderOperationMapper;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderOperationDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 发布单操作表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Service
public class OReleaseOrderOperationDAOImpl extends ServiceImpl<OReleaseOrderOperationMapper, OReleaseOrderOperationDO> implements OReleaseOrderOperationDAO {

}
