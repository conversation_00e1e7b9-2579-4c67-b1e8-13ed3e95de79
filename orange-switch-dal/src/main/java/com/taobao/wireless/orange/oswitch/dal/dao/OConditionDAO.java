package com.taobao.wireless.orange.oswitch.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 条件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface OConditionDAO extends IService<OConditionDO> {

    /**
     * 根据条件名称列表获取条件对象列表
     *
     * @param namespaceId
     * @param conditionNames
     * @return
     */
    List<OConditionDO> getByNames(String namespaceId, List<String> conditionNames);

    /**
     * 根据 conditionId 获取条件
     *
     * @param conditionId
     * @return
     */
    OConditionDO getByConditionId(String conditionId);


    Map<String, OConditionDO> getConditionMapByNamespaceId(String namespaceId);
    /**
     * 获取指定顺序的条件ID列表
     *
     * @param namespaceId
     * @param conditionNames
     * @return
     */
    List<String> getOrderedConditionIdsByName(String namespaceId, List<String> conditionNames);

    /**
     * 获取条件ID到名称的映射
     */
    Map<String, String> getConditionId2NameMap(List<String> conditionIds);

    Map<String, OConditionDO> getConditionMap(List<String> conditionIds);
}
