package com.taobao.wireless.orange.oswitch.dal.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class CustomMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        String workerId = ThreadContextUtil.getWorkerId();
        this.strictInsertFill(metaObject, "creator", String.class, workerId);
        this.strictInsertFill(metaObject, "modifier", String.class, workerId);
        this.strictInsertFill(metaObject, "gmtCreate", Date.class, new Date());
        this.strictUpdateFill(metaObject, "gmtModified", Date.class, new Date());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "modifier", String.class, ThreadContextUtil.getWorkerId());
        this.strictUpdateFill(metaObject, "gmtModified", Date.class, new Date());
    }
}
