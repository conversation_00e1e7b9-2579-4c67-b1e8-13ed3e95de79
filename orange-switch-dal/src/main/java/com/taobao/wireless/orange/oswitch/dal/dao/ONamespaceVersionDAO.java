package com.taobao.wireless.orange.oswitch.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionDO;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <p>
 * 命名空间版本表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface ONamespaceVersionDAO extends IService<ONamespaceVersionDO> {
    List<ONamespaceVersionDO> getAvailableNamespaceVersionsByAppKey(String appKey);

    List<ONamespaceVersionDO> getAvailableNamespaceVersionsByAppKey(String appKey, Pair<String, String> changeVersionRange);

    ONamespaceVersionDO getAvailableNamespaceVersionByNamespaceId(String namespaceId);

    void invalidateCurrentVersion(String namespaceId);
}
