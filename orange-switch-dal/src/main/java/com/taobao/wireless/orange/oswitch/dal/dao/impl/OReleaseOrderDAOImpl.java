package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.OReleaseOrderMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 发布单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Service
public class OReleaseOrderDAOImpl extends ServiceImpl<OReleaseOrderMapper, OReleaseOrderDO> implements OReleaseOrderDAO {

    @Override
    public OReleaseOrderDO getByReleaseVersion(String releaseVersion) {
        return lambdaQuery()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                .one();
    }

    @Override
    public Map<String, OReleaseOrderDO> getReleaseOrderMapByReleaseVersions(List<String> releaseVersions) {
        if (CollectionUtils.isEmpty(releaseVersions)) {
            return Map.of();
        }

        return lambdaQuery().in(OReleaseOrderDO::getReleaseVersion, releaseVersions)
                .list()
                .stream()
                .collect(Collectors.toMap(OReleaseOrderDO::getReleaseVersion, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public Map<ReleaseOrderStatus, Long> getCountGroupByStatus(String namespaceId) {
        QueryWrapper<OReleaseOrderDO> wrapper = new QueryWrapper<>();
        wrapper.select("status, count(*) as count")
                .eq("namespace_id", namespaceId)
                .groupBy("status")
                .having("count(*) > 0");

        return this.baseMapper.selectMaps(wrapper).stream()
                .collect(Collectors.toMap(
                        map -> ReleaseOrderStatus.valueOf(map.get("status").toString()), map -> Long.valueOf(map.get("count").toString())));
    }
}
