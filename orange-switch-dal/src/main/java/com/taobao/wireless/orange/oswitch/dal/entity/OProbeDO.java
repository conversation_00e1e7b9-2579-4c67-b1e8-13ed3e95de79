package com.taobao.wireless.orange.oswitch.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.Available;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 探针表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
@TableName("o_probe")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OProbeDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用KEY
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 索引版本号
     */
    @TableField("index_version")
    private String indexVersion;

    /**
     * 探针内容
     */
    @TableField("content")
    private String content;

    /**
     * agateware 任务ID
     */
    @TableField("agateware_task_id")
    private String agatewareTaskId;

    /**
     * 是否最新版本
     */
    @TableField("is_available")
    private Available isAvailable;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;
}
