package com.taobao.wireless.orange.oswitch.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.common.constant.enums.StageType;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;

/**
 * <p>
 * 发布单阶段详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
public interface OReleaseOrderStageDAO extends IService<OReleaseOrderStageDO> {

    OReleaseOrderStageDO getByReleaseVersionAndType(String releaseVersion, StageType stageType);
}
