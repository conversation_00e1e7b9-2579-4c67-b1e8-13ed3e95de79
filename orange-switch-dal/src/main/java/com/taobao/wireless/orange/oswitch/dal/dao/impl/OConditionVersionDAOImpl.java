package com.taobao.wireless.orange.oswitch.dal.dao.impl;

import com.taobao.wireless.orange.oswitch.dal.entity.OConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.mapper.OConditionVersionMapper;
import com.taobao.wireless.orange.oswitch.dal.dao.OConditionVersionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 条件版本表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OConditionVersionDAOImpl extends ServiceImpl<OConditionVersionMapper, OConditionVersionDO> implements OConditionVersionDAO {

}
