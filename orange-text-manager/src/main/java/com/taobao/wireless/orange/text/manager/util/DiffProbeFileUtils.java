package com.taobao.wireless.orange.text.manager.util;

import com.taobao.wireless.orange.text.manager.model.DiffProbe;
import com.taobao.wireless.orange.text.manager.model.DiffProbeFile;
import com.taobao.wireless.orange.text.manager.model.ProbeMetaItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 老探针文件解析工具
 * from orange-console
 */
@Component
@Slf4j
public class DiffProbeFileUtils {
    private static final String SPLIT_LINE = "\n";

    private static final String SPLIT_ATT = ",";
    private static final String SPLIT_META = ";";
    private static final String SPLIT_META_ATT = ":";
    // 需要确认为啥这俩的时间格式不一致
    private static final DateTimeFormatter FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final String FORMAT_VERSION = "yyyyMMddHHmmssSSS";

    /**
     * 解析文件
     *
     * @param probeFileString 探针文本
     * @return 探针解析结果
     */
    public static DiffProbeFile decodeProbeFile(String probeFileString) {
        String[] probeFileArray = Arrays.stream(probeFileString.split(SPLIT_LINE))
                .filter(StringUtils::isNotBlank)
                .toArray(String[]::new);

        if (ObjectUtils.isEmpty(probeFileArray)) {
            return null;
        }

        DiffProbeFile probeFile = new DiffProbeFile();
        probeFile.setList(new ArrayList<>());
        boolean readAttr = false;
        for (String data : probeFileArray) {
            String[] split = data.trim().split(SPLIT_ATT);

            //第一行是属性描述
            if (!readAttr) {
                String version = split[2];
                if (!isVersionValidate(version)) {
                    return null;
                }
                probeFile.setVersion(version);
                probeFile.setCdn(split[0]);
                probeFile.setProtocol(split[1]);
                readAttr = true;
                continue;
            }

            //之后是探针描述
            String appKey = split[0];
            String probeVersion = split[1];
            String cdn = split[2];
            String metasString = split[3];

            List<ProbeMetaItem> metas = Arrays.stream(StringUtils.split(metasString, SPLIT_META)).map(each -> {
                        String[] eachArr = StringUtils.split(each, SPLIT_META_ATT);
                        if (eachArr.length != 3) {
                            return null;
                        }
                        String baseVersion = eachArr[0];
                        String resourceId = eachArr[1];
                        String md5 = eachArr[2];

                        ProbeMetaItem item = new ProbeMetaItem();
                        item.setBaseVersion(baseVersion);
                        item.setResourceId(resourceId);
                        item.setMd5(md5);
                        return item;
                    })
                    .filter(Objects::nonNull)
                    .toList();

            DiffProbe probe = new DiffProbe();
            probe.setAppKey(appKey);
            probe.setProbeVersion(probeVersion);
            probe.setMetaList(metas);
            probe.setCdn(StringUtils.isBlank(cdn) ? probeFile.getCdn() : cdn);
            probeFile.getList().add(probe);
        }

        return probeFile;
    }

    private static boolean isVersionValidate(String dateText) {
        try {
            LocalDate.parse(dateText, FORMAT);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static Date changeVersionToDate(String changeVersion) {
        String dateStr = StringUtils.substring(changeVersion, 2);
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_VERSION);
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("changeVersionToDate error", e);
            return null;
        }
    }
}
