<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.taobao.wireless</groupId>
        <artifactId>orange</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>orange-service</artifactId>
    <name>orange-service</name>

    <properties>
        <java.version>1.8</java.version>
        <protobuf.version>3.7.0</protobuf.version>
        <grpc.version>1.9.1</grpc.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>orange-switch-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>agateware-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>orange-text-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx-plugin-trace-eagleeye</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
