package com.taobao.wireless.orange.service.metaq;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.taobao.wireless.orange.common.constant.enums.StageStatus;
import com.taobao.wireless.orange.common.constant.enums.StageType;
import com.taobao.wireless.orange.common.constant.enums.TigaActionType;
import com.taobao.wireless.orange.common.thread.OThreadContext;
import com.taobao.wireless.orange.common.thread.OThreadContextHolder;
import com.taobao.wireless.orange.common.util.UserUtil;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderStageDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.tiga.release.common.task.TaskStatus;
import com.taobao.wireless.tiga.release.console.api.task.model.metaq.TaskChange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TigaReleaseListener implements MessageListenerConcurrently {

    @Autowired
    private OReleaseOrderStageDAO releaseOrderStageDAO;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        try {
            var threadContext = new OThreadContext();
            threadContext.setWorkerId("tiga-release");
            threadContext.setWorkerName("tiga-release");
            OThreadContextHolder.set(threadContext);

            for (MessageExt msg : msgs) {
                TaskChange change = TaskChange.deserialize(msg.getBody());
                if (change == null) {
                    log.warn("TigaReleaseListener receive invalid change message: {}", msg);
                    continue;
                }

                if (!change.getStatus().isFinished()) {
                    log.info("TigaReleaseListener receive unfinished change message, releaseVersion: {}", change.getSourceOrderId());
                    continue;
                }

                log.info("TigaReleaseListener receive finished change message: {}", JSON.toJSONString(change));
                threadContext.setWorkerId(UserUtil.formatWorkerId(change.getOperator()));
                threadContext.setWorkerName(UserUtil.formatWorkerId(change.getOperator()));
                updateStageStatus(change);
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Throwable e) {
            log.error("TigaReleaseListener consume message error", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        } finally {
            OThreadContextHolder.clear();
        }
    }

    private void updateStageStatus(TaskChange change) {
        if (TigaActionType.SWITCH.getCode().equals(change.getActionType())) {
            String releaseVersion = change.getSourceOrderId();

            OReleaseOrderStageDO stage = releaseOrderStageDAO.getByReleaseVersionAndType(releaseVersion, StageType.SMALLFLOW_GRAY);

            if (stage == null || stage.getStatus().isCompleted()) {
                return;
            }

            StageStatus stageStatus = switch (change.getStatus()) {
                case TaskStatus.SKIPPED -> StageStatus.SKIPPED;
                case TaskStatus.CANCELLED -> StageStatus.CANCELED;
                // TIMEOUT 为 tiga 圈选设备超时
                case TaskStatus.SUCCESS, TaskStatus.TIMEOUT -> StageStatus.SUCCESS;
                case TaskStatus.FAILED -> StageStatus.FAILED;
                default -> null;
            };

            if (stageStatus != null) {
                releaseOrderStageDAO.updateById(OReleaseOrderStageDO.builder()
                        .id(stage.getId())
                        .status(stageStatus)
                        .build());
            }
        }
    }
}
