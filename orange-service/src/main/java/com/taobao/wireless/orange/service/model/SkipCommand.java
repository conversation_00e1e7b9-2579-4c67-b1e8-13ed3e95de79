package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.SkipType;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class SkipCommand {
    @NotNull(message = "跳过的类型为空")
    @EnumValidation(clazz = SkipType.class, message = "跳过的类型不合法")
    private SkipType skipType;
    @NotBlank(message = "原因不能为空")
    private String reason;
}
