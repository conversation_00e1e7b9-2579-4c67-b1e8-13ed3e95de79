package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BatchProcessor;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.external.amdp.AmdpService;
import com.taobao.wireless.orange.external.amdp.model.User;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class UserService {

    @Autowired
    private AmdpService amdpService;

    @Autowired
    private BatchProcessor batchProcessor;

    private static final int USER_QUERY_BATCH_SIZE = 100;

    /**
     * 根据工号批量查询用户
     */
    @AttributeValidate
    public Result<Map<String, User>> queryUserByWorkNoList(@NotEmpty(message = "工号列表不能为空") List<String> workNoList) {
        var config = new BatchProcessor.BatchConfig<String>()
                .batchSize(USER_QUERY_BATCH_SIZE)
                .validator(workNo -> workNo != null && !workNo.trim().isEmpty())
                .preprocessor(String::trim);

        var userMap = batchProcessor.processBatchToMapAsync(
                workNoList,
                amdpService::queryUserByWorkNoList,
                config
        );

        return Result.success(userMap);
    }

    /**
     * 根据关键字模糊搜索用户
     *
     * @param keyword 关键字
     * @return 用户列表
     */
    @AttributeValidate
    public Result<List<User>> fuzzySearchUserByKeyword(@NotBlank(message = "关键字不能为空") String keyword) {
        return Pipe.of(keyword)
                .map(amdpService::fuzzySearchUserByKeyword)
                .map(Result::new)
                .get();
    }
}
