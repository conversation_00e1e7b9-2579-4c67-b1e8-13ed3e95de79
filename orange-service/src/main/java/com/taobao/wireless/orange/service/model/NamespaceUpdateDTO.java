package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.NamespaceStatus;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
public class NamespaceUpdateDTO {
    /**
     * 命名空间ID
     */
    @NotBlank(message = "命名空间ID不能为空")
    private String namespaceId;

    /**
     * 负责人列表
     */
    private List<String> owners;

    /**
     * 测试人员列表
     */
    private List<String> testers;

    /**
     * 状态
     */
    @EnumValidation(clazz = NamespaceStatus.class, message = "状态不合法")
    private NamespaceStatus status;

    /**
     * 描述
     */
    private String description;
}
