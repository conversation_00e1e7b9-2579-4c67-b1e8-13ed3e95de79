package com.taobao.wireless.orange.service.probe;

import com.alibaba.fastjson2.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.eagleeye.EagleEye;
import com.taobao.wireless.orange.common.constant.enums.AserverIndexType;
import com.taobao.wireless.orange.publish.probe.model.AserverProbe;
import com.taobao.wireless.orange.text.manager.util.DiffProbeFileUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.external.switchcenter.SwitchConfig.maxWmccPublishMinutes;

@Component
@Slf4j(topic = "probe")
public class ProbeDateCheckerJob extends JavaProcessor {
    @Autowired
    private AgatewareProbeDataService agatewareProbeDataService;

    @Autowired
    private WmccDpProbeDataService wmccDpProbeDataService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            // traceId, traceName, 90-入口型
            EagleEye.startTrace(null, getClass().getName(), EagleEye.TYPE_CUSTOM_TRACE);

            // 1.获取 agateware DP 探针数据
            List<AserverProbe> agatewareProbes = agatewareProbeDataService.getProbes();
            Map<String, List<AserverProbe>> indexType2Probes = agatewareProbes.stream().collect(Collectors.groupingBy(AserverProbe::getIndexType));
            List<AserverProbe> agatewareDpProbes = indexType2Probes.getOrDefault(AserverIndexType.DP.getCode(), List.of());

            // 2.获取 wmcc DP 探针数据
            List<AserverProbe> wmccDpProbes = wmccDpProbeDataService.getProbes();

            // 3.比较 DP 探针差异
            List<String> diffAppKeys = getProbeDiffAppKeys(wmccDpProbes, agatewareDpProbes);
            if (CollectionUtils.isEmpty(diffAppKeys)) {
                log.info("Probe_check success, no diff appKeys");
            } else {
                log.error("Probe_check error, exist {} diff appKeys: {}", diffAppKeys.size(), String.join(",", diffAppKeys));
            }

            // todo: 4. 校验一下 switch 探针的正确性

            return new ProcessResult(true);
        } catch (Throwable e) {
            log.error("Probe_check error", e);
            return new ProcessResult(false, e.getMessage());
        } finally {
            EagleEye.endTrace(null);
        }
    }

    /**
     * 计算两个探针列表的差异，以 probe1 为准
     *
     * @param probe1
     * @param probe2
     * @return 不相等的 appKeys
     */
    private List<String> getProbeDiffAppKeys(@NonNull List<AserverProbe> probe1, @NonNull List<AserverProbe> probe2) {
        Map<String, AserverProbe> appKey2Probe = probe2.stream().collect(Collectors.toMap(AserverProbe::getAppkey, Function.identity()));
        List<String> appKeys = new ArrayList<>();

        for (AserverProbe aserverProbe : probe1) {
            String appKey = aserverProbe.getAppkey();
            if (appKey2Probe.get(appKey) == null) {
                log.error("Probe_check error appKey: {} not found in probe2", appKey);
                appKeys.add(appKey);
            } else if (!appKey2Probe.get(appKey).equals(aserverProbe)) {
                String probe1Version = aserverProbe.getProbeVersion();
                String probe2Version = appKey2Probe.get(appKey).getProbeVersion();
                String compare = probe1Version.compareTo(probe2Version) > 0 ? ">" : "<";
                log.error("Probe_check error appKey: {}, probe1[{}{}{}]probe2", appKey, probe1Version, compare, probe2Version);

                log.error("Probe_check error detail appKey: {}, probe1: {}, probe2: {}", appKey, JSON.toJSONString(aserverProbe), JSON.toJSONString(appKey2Probe.get(appKey)));

                // 如果老探针(probe1)索引是在当前之间往前 MAX_PUBLISH_MINUTES 内，说明可能是因为新老链路发布时间差导致的临时短时间不一致，不添加到 appKeys 里进行告警
                Date probe1Date = DiffProbeFileUtils.changeVersionToDate(aserverProbe.getProbeVersion());
                if (probe1Date != null && (System.currentTimeMillis() - probe1Date.getTime()) / 1000 / 60 <= maxWmccPublishMinutes) {
                    continue;
                }

                Date probe2Date = DiffProbeFileUtils.changeVersionToDate(appKey2Probe.get(appKey).getProbeVersion());
                if (probe2Date != null && (System.currentTimeMillis() - probe2Date.getTime()) / 1000 / 60 <= maxWmccPublishMinutes) {
                    continue;
                }

                appKeys.add(appKey);
            }
        }
        return appKeys;
    }
}
