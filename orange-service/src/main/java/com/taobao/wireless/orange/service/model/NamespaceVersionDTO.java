package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import lombok.Data;

import java.util.Date;

@Data
public class NamespaceVersionDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 应用KEY
     */
    private String appKey;

    /**
     * 命名空间ID
     */
    private String namespaceId;

    /**
     * 命名空间正式版本号
     */
    private String namespaceVersion;

    /**
     * 命名空间变更版本号
     */
    private String namespaceChangeVersion;

    /**
     * 变更类型
     */
    private NamespaceVersionChangeType changeType;

    /**
     * 发起变更的发布单版本号
     */
    private String releaseVersion;

    /**
     * 是否最新版本
     */
    private Available isAvailable;

    /**
     * 第一次应用该变更的索引版本号
     */
    private String indexVersion;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 索引资源ID
     */
    private String indexResourceId;

    /**
     * 索引文件生成时间
     */
    private Date indexGmtCreate;
}
