package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.constant.enums.NamespaceType;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.manager.common.PageUtil;
import com.taobao.wireless.orange.oswitch.manager.namespace.NamespaceManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.UserNamespaceManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ONamespaceBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.version.NamespaceVersionManager;
import com.taobao.wireless.orange.service.model.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 命名空间服务
 */
@Service
public class NamespaceService {

    @Autowired
    private NamespaceManager namespaceManager;
    @Autowired
    private ONamespaceDAO namespaceDAO;
    @Autowired
    private NamespaceVersionManager namespaceVersionManager;
    @Autowired
    private UserNamespaceManager userNamespaceManager;

    /**
     * 获取所有命名空间的列表。
     *
     * @return Namespace对象列表
     */
    @AttributeValidate
    public PaginationResult<NamespaceDTO> query(@NotNull(message = "命名空间查询条件不能为空") NamespaceQueryDTO namespaceQueryDTO, Pagination pagination) {
        return Pipe.of(namespaceQueryDTO)
                .map(BeanUtil.createFromProperties(ONamespaceBO.class))
                .map(q -> namespaceManager.query(q, pagination))
                .map(PageUtil.convertToPaginationResult(NamespaceDTO.class))
                .get();
    }

    /**
     * 创建新的命名空间
     *
     * @param updateNamespace 要创建的Namespace对象
     * @return 新创建的命名空间ID
     */
    @AttributeValidate
    public Result<String> create(@NotNull(message = "命名空间对象不能为空") NamespaceCreateDTO updateNamespace) {
        return Pipe.of(updateNamespace)
                .map(BeanUtil.createFromProperties(ONamespaceBO.class))
                .map(namespaceManager::create)
                .map(Result::new)
                .get();
    }

    /**
     * 获取指定命名空间的详细信息。
     *
     * @param namespaceId 命名空间ID
     * @return 包含详细信息的Namespace对象
     */
    @AttributeValidate
    public Result<NamespaceDTO> getByNamespaceId(@NotBlank(message = "命名空间ID不能为空") String namespaceId) {
        return Pipe.of(namespaceId)
                .map(namespaceManager::getByNamespaceId)
                .map(BeanUtil.createFromProperties(NamespaceDTO.class))
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public Result<NamespaceDTO> getByNamespaceName(@NotBlank(message = "命名空间名称不能为空") String namespaceName) {
        return Pipe.of(namespaceName)
                .map(namespaceManager::getByNamespaceName)
                .map(BeanUtil.createFromProperties(NamespaceDTO.class))
                .map(Result::new)
                .get();
    }

    /**
     * 更新现有的命名空间信息。
     *
     * @param namespace 更新后的Namespace对象
     */
    @AttributeValidate
    public Result<Void> update(@NotNull(message = "命名空间对象不能为空") NamespaceUpdateDTO namespace) {
        Pipe.of(namespace)
                .map(BeanUtil.createFromProperties(ONamespaceBO.class))
                .map(namespaceManager::update);
        return Result.success();
    }

    /**
     * 删除命名空间
     *
     * @param namespaceId 命名空间ID
     */
    @AttributeValidate
    public Result<Void> delete(@NotBlank(message = "命名空间ID不能为空") String namespaceId) {
        // todo:
        return Result.success();
    }

    /**
     * 查询命名空间版本历史
     *
     * @param namespaceId 命名空间ID
     * @param pagination  分页信息
     * @return 命名空间版本历史
     */
    @AttributeValidate
    public PaginationResult<NamespaceVersionDTO> histories(String namespaceId, Pagination pagination) {
        return Pipe.of(namespaceId)
                .map(q -> namespaceVersionManager.histories(q, pagination))
                .map(PageUtil.convertToPaginationResult(NamespaceVersionDTO.class))
                .get();
    }

    /**
     * 获取当前用户常用的namespace列表
     *
     * @param limit 返回数量限制，默认10，最大50
     * @return 常用namespace列表
     */
    @AttributeValidate
    public Result<List<NamespaceDTO>> getFrequentNamespaces(
            @Min(value = 1, message = "返回数量限制不能小于1")
            @Max(value = 50, message = "返回数量限制不能大于50")
            Integer limit) {
        String workerId = ThreadContextUtil.getWorkerId();
        if (workerId == null) {
            return Result.success(new ArrayList<>());
        }

        List<String> namespaceIds = userNamespaceManager.getUserFrequentNamespaces(NamespaceType.SWITCH, workerId, limit);
        return Pipe.of(namespaceIds)
                .map(this::getOrderedNamespaceList)
                .map(Result::new)
                .get();
    }

    /**
     * 获取当前用户最近访问的namespace列表
     *
     * @param limit 返回数量限制，默认10，最大50
     * @return 最近访问namespace列表
     */
    @AttributeValidate
    public Result<List<NamespaceDTO>> getRecentNamespaces(
            @Min(value = 1, message = "返回数量限制不能小于1")
            @Max(value = 50, message = "返回数量限制不能大于50")
            Integer limit) {
        String workerId = ThreadContextUtil.getWorkerId();
        if (workerId == null) {
            return Result.success(new ArrayList<>());
        }

        List<String> namespaceIds = userNamespaceManager.getUserRecentNamespaces(NamespaceType.SWITCH, workerId, limit);
        return Pipe.of(namespaceIds)
                .map(this::getOrderedNamespaceList)
                .map(Result::new)
                .get();
    }

    /**
     * 根据 namespaceId 列表获取 namespace 列表，保持传入的顺序
     *
     * @param namespaceIds
     * @return
     */
    private List<NamespaceDTO> getOrderedNamespaceList(List<String> namespaceIds) {
        Map<String, NamespaceDTO> namespaceId2Namespace = namespaceDAO.getByNamespaceIds(namespaceIds)
                .stream()
                .collect(Collectors.toMap(ONamespaceDO::getNamespaceId,
                        BeanUtil.createFromProperties(NamespaceDTO.class)));

        return namespaceIds.stream()
                .map(namespaceId2Namespace::get)
                .collect(Collectors.toList());
    }
}
