package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.OperationStatus;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import lombok.Data;

import java.util.Date;

@Data
public class ReleaseOrderOperationDTO {
    private Long id;
    /**
     * 操作类型
     */
    private OperationType type;

    /**
     * 输入参数(JSON)
     */
    private String params;

    /**
     * 结果信息(JSON)
     */
    private String result;

    /**
     * 操作状态
     */
    private OperationStatus status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;
}
