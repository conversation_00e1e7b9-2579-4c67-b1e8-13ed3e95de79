package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import lombok.Data;

import java.util.Date;

@Data
public class ParameterConditionDTO {
    /**
     * 参数ID
     */
    private String parameterId;

    /**
     * 参数名
     */
    private String parameterKey;

    /**
     * 条件ID
     */
    private String conditionId;

    /**
     * 条件名称
     */
    private String conditionName;

    /**
     * 条件颜色
     */
    private String conditionColor;

    /**
     * 发布版本号
     */
    private String releaseVersion;

    /**
     * 修改前发布版本号
     */
    private String previousReleaseVersion;

    /**
     * 参数条件值
     */
    private String value;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 状态
     */
    private VersionStatus status;

    /**
     * 变更类型
     */
    private ChangeType changeType;
}
