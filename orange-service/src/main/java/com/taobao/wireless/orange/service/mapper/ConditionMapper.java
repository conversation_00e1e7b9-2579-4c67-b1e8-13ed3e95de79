package com.taobao.wireless.orange.service.mapper;

import com.alibaba.fastjson2.JSON;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionVersionBO;
import com.taobao.wireless.orange.service.model.ConditionDTO;
import com.taobao.wireless.orange.service.model.ConditionDetailDTO;
import com.taobao.wireless.orange.service.model.ConditionExpressionDTO;
import com.taobao.wireless.orange.service.model.ParameterConditionDTO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * MapStruct映射器，用于条件相关的对象转换
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Mapper(componentModel = "spring")
public interface ConditionMapper {

    /**
     * 将ConditionBO列表转换为ConditionDetailDTO列表
     *
     * @param conditions 条件业务对象列表
     * @return 条件详情DTO列表
     */
    List<ConditionDetailDTO> toConditionDetailDTOList(List<ConditionBO> conditions);

    /**
     * 将ConditionBO转换为ConditionDetailDTO
     *
     * @param condition 条件业务对象
     * @return 条件详情DTO
     */
    @Mapping(target = "gmtModified", source = "conditionVersion.gmtCreate")
    @Mapping(target = "expression", source = "conditionVersion.expression", qualifiedByName = "parseExpression")
    @Mapping(target = "parameterConditions", source = "relatedParameters", qualifiedByName = "mapParameterConditions")
    ConditionDetailDTO toConditionDetailDTO(ConditionBO condition);

    /**
     * 将ConditionBO列表转换为ConditionDTO列表
     *
     * @param conditions 条件业务对象列表
     * @return 条件DTO列表
     */
    @IterableMapping(qualifiedByName = "toConditionDTO")
    List<ConditionDTO> toConditionDTOList(List<ConditionBO> conditions);

    /**
     * 将ConditionBO转换为ConditionDTO
     *
     * @param condition 条件业务对象
     * @return 条件DTO
     */
    @Named("toConditionDTO")
    @Mapping(target = "expression", source = "conditionVersion.expression", qualifiedByName = "parseExpression")
    ConditionDTO toConditionDTO(ConditionBO condition);

    /**
     * 将ConditionVersionBO列表转换为ConditionDetailDTO列表
     *
     * @param conditionVersions 条件版本业务对象列表
     * @return 条件详情DTO列表
     */
    List<ConditionDetailDTO> conditionVersionsToConditionDetailDTOList(List<ConditionVersionBO> conditionVersions);

    /**
     * 将ConditionVersionBO转换为ConditionDetailDTO
     *
     * @param conditionVersion 条件版本业务对象
     * @return 条件详情DTO
     */
    @Mapping(target = "expression", source = "expression", qualifiedByName = "parseExpression")
    @Mapping(target = "status", source = "condition.status")
    @Mapping(target = "name", source = "condition.name")
    @Mapping(target = "color", source = "condition.color")
    ConditionDetailDTO conditionVersionToConditionDetailDTO(ConditionVersionBO conditionVersion);

    /**
     * 解析条件表达式JSON字符串
     *
     * @param expressionJson 表达式JSON字符串
     * @return 条件表达式DTO
     */
    @Named("parseExpression")
    default ConditionExpressionDTO parseExpression(String expressionJson) {
        if (StringUtils.isBlank(expressionJson)) {
            return null;
        }
        return JSON.parseObject(expressionJson, ConditionExpressionDTO.class);
    }

    /**
     * 将参数条件版本列表转换为参数条件DTO列表
     *
     * @param relatedParameters 相关参数列表
     * @return 参数条件DTO列表
     */
    @Named("mapParameterConditions")
    default List<ParameterConditionDTO> mapParameterConditions(List<?> relatedParameters) {
        return BeanUtil.createListFromProperties(relatedParameters, ParameterConditionDTO.class);
    }
}