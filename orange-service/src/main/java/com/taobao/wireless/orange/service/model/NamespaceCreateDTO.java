package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class NamespaceCreateDTO {
    /**
     * 应用KEY
     */
    @NotBlank(message = "应用KEY不能为空")
    private String appKey;

    /**
     * 命名空间的类型
     */
    @NotNull(message = "命名空间的类型不能为空")
    private NamespaceBizType bizType;

    /**
     * 命名空间类型对应的实体ID
     */
    @NotBlank(message = "命名空间类型对应的实体ID不能为空")
    private String bizId;

    /**
     * 负责人列表
     */
    @NotEmpty(message = "负责人列表不能为空")
    private List<String> owners;

    /**
     * 测试人员列表
     */
    @NotEmpty(message = "测试人员列表不能为空")
    private List<String> testers;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 描述
     */
    private String description;
}
