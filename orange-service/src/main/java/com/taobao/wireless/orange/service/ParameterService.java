package com.taobao.wireless.orange.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.constant.enums.ParameterStatus;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.oswitch.dal.dao.OConditionDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OParameterDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterDO;
import com.taobao.wireless.orange.oswitch.manager.common.PageUtil;
import com.taobao.wireless.orange.oswitch.manager.namespace.ParameterManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterBO;
import com.taobao.wireless.orange.service.mapper.ParameterMapper;
import com.taobao.wireless.orange.service.model.ParameterDetailDTO;
import com.taobao.wireless.orange.service.model.ParameterDirectUpdateDTO;
import com.taobao.wireless.orange.service.model.ParameterKeyExistQuery;
import com.taobao.wireless.orange.service.model.ParameterQueryDTO;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ParameterService {
    @Autowired
    private ParameterManager parameterManager;
    @Autowired
    private OParameterDAO parameterDAO;
    @Autowired
    private OConditionDAO conditionDAO;
    @Autowired
    private ParameterMapper parameterMapper;

    @AttributeValidate
    public PaginationResult<ParameterDetailDTO> query(@NotNull(message = "参数查询条件不能为空") ParameterQueryDTO query, Pagination pagination) {
        // 转换查询条件
        ParameterBO parameterCondition = BeanUtil.createFromProperties(query, ParameterBO.class);

        // 查询参数数据
        Page<ParameterBO> pageResult = parameterManager.query(parameterCondition, pagination);

        PaginationResult<ParameterDetailDTO> result = PageUtil.convertToPaginationResult(pageResult, ParameterDetailDTO.class);

        // 如果没有数据，直接返回空结果
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return result;
        }

        // 转换结果
        result.setData(convert(query.getNamespaceId(), pageResult.getRecords()));
        return result;
    }

    /**
     * 更新参数基础信息（不创建发布单）
     *
     * @param parameterUpdate 参数更新对象
     */
    @AttributeValidate
    public Result<Void> update(@NotNull(message = "参数更新对象不能为空") ParameterDirectUpdateDTO parameterUpdate) {
        parameterDAO.lambdaUpdate()
                .eq(OParameterDO::getParameterId, parameterUpdate.getParameterId())
                .set(StringUtils.isNotBlank(parameterUpdate.getDescription()), OParameterDO::getDescription, parameterUpdate.getDescription())
                .update();
        return Result.success();
    }

    @AttributeValidate
    public Result<Boolean> checkParameterKeyExists(ParameterKeyExistQuery existQuery) {
        return Result.success(parameterDAO.lambdaQuery()
                .eq(OParameterDO::getNamespaceId, existQuery.getNamespaceId())
                .eq(OParameterDO::getParameterKey, existQuery.getParameterKey())
                .ne(OParameterDO::getStatus, ParameterStatus.INVALID)
                .exists());
    }

    private List<ParameterDetailDTO> convert(String namespaceId, List<ParameterBO> params) {
        Map<String, OConditionDO> conditionId2Condition = conditionDAO.getConditionMapByNamespaceId(namespaceId);
        return params.stream()
                .map(param -> parameterMapper.toParameterDetailDTO(param, conditionId2Condition))
                .toList();
    }
}
