package com.taobao.wireless.orange.service.mapper;

import com.alibaba.fastjson2.JSON;
import com.taobao.wireless.orange.common.constant.enums.ReleaseType;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.oswitch.dal.dao.OConditionDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionDO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionVersionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterConditionVersionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterVersionBO;
import com.taobao.wireless.orange.oswitch.manager.release.model.ReleaseOrderBO;
import com.taobao.wireless.orange.oswitch.manager.release.model.ReleaseOrderChangeBO;
import com.taobao.wireless.orange.oswitch.manager.release.model.RatioGrayProgressBO;
import com.taobao.wireless.orange.service.model.*;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_NAME;

/**
 * MapStruct映射器，用于发布单相关的对象转换
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Mapper(componentModel = "spring", uses = {ConditionMapper.class})
public abstract class ReleaseOrderMapper {

    @Autowired
    protected OConditionDAO conditionDAO;

    /**
     * 将ReleaseOrderChangeBO转换为ReleaseOrderChangesDTO
     *
     * @param releaseOrderChangeBO 发布单变更业务对象
     * @return 发布单变更DTO
     */
    @Mapping(target = "parameterChanges", source = "parameterChanges", qualifiedByName = "mapParameterChanges")
    @Mapping(target = "conditionChanges", source = "conditionChanges")
    public abstract ReleaseOrderChangesDTO toReleaseOrderChangesDTO(ReleaseOrderChangeBO releaseOrderChangeBO);

    /**
     * 将ReleaseOrderCreateDTO转换为ReleaseOrderBO
     *
     * @param releaseOrderCreateDTO 发布单创建DTO
     * @return 发布单业务对象
     */
    @Mapping(target = "parameterVersions", source = "parameterChanges", qualifiedByName = "mapParameterChangesToVersions")
    @Mapping(target = "conditionVersions", source = "conditionChanges", qualifiedByName = "mapConditionChangesToVersions")
    public abstract ReleaseOrderBO toReleaseOrderBO(ReleaseOrderCreateDTO releaseOrderCreateDTO);

    /**
     * 将ReleaseOrderBO转换为ReleaseOrderDetailDTO
     *
     * @param releaseOrderBO 发布单业务对象
     * @return 发布单详情DTO
     */
    @Mapping(target = "parameterKeys", source = "parameterVersions", qualifiedByName = "extractParameterKeys")
    @Mapping(target = "conditionIds", source = "conditionVersions", qualifiedByName = "extractConditionIds")
    public abstract ReleaseOrderDetailDTO toReleaseOrderDetailDTO(ReleaseOrderBO releaseOrderBO);

    /**
     * 将 RatioGrayProgressBO 映射为 RatioGrayProgressDTO
     */
    public abstract RatioGrayProgressDTO toRatioGrayProgressDTO(RatioGrayProgressBO ratioGrayProgressBO);

    /**
     * 兼容历史行为：当源列表为空时，目标字段置为 null（而不是空列表）
     */
    @AfterMapping
    protected void nullifyEmptyRatioNodes(RatioGrayProgressBO source,
                                          @MappingTarget RatioGrayProgressDTO target) {
        if (source == null) {
            return;
        }
        if (CollectionUtils.isEmpty(source.getRatioGrayProgressNodes())) {
            target.setRatioGrayProgressNodes(null);
        }
    }

    /**
     * 映射参数变更为参数详情DTO列表
     */
    @Named("mapParameterChanges")
    protected List<ParameterDetailDTO> mapParameterChanges(List<ParameterVersionBO> parameterVersions) {
        if (CollectionUtils.isEmpty(parameterVersions)) {
            return null;
        }

        // 收集所有条件ID
        List<String> conditionIds = parameterVersions.stream()
                .filter(p -> CollectionUtils.isNotEmpty(p.getParameterConditionVersions()))
                .flatMap(p -> p.getParameterConditionVersions().stream())
                .map(ParameterConditionVersionBO::getConditionId)
                .distinct()
                .toList();

        // 查询条件信息
        Map<String, OConditionDO> conditionId2Condition = conditionDAO.getConditionMap(conditionIds);

        return parameterVersions.stream().map(p -> {
            ParameterDetailDTO parameterDetailDTO = BeanUtil.createFromProperties(p, ParameterDetailDTO.class);
            if (CollectionUtils.isEmpty(p.getParameterConditionVersions())) {
                return parameterDetailDTO;
            }

            List<ParameterConditionDTO> parameterConditions = BeanUtil.createListFromProperties(p.getParameterConditionVersions(), ParameterConditionDTO.class)
                    .stream()
                    .peek(c -> {
                        if (DEFAULT_CONDITION_ID.equals(c.getConditionId())) {
                            c.setConditionName(DEFAULT_CONDITION_NAME);
                        } else {
                            var condition = conditionId2Condition.get(c.getConditionId());
                            if (condition != null) {
                                c.setConditionName(condition.getName());
                                c.setConditionColor(condition.getColor());
                            }
                        }
                    })
                    .toList();
            parameterDetailDTO.setParameterConditions(parameterConditions);
            return parameterDetailDTO;
        }).toList();
    }

    @Named("mapReleaseType")
    protected ReleaseType mapReleaseType(ReleaseType releaseType) {
        return releaseType;
    }

    /**
     * 将参数变更DTO列表转换为参数版本BO列表
     */
    @Named("mapParameterChangesToVersions")
    protected List<ParameterVersionBO> mapParameterChangesToVersions(List<ParameterChangeDTO> parameterChanges) {
        if (CollectionUtils.isEmpty(parameterChanges)) {
            return new ArrayList<>();
        }

        return parameterChanges.stream().map(parameterChangeDTO -> {
            ParameterVersionBO parameterVersionBO = BeanUtil.createFromProperties(parameterChangeDTO, ParameterVersionBO.class);
            parameterVersionBO.setParameterBO(BeanUtil.createFromProperties(parameterChangeDTO, ParameterBO.class));
            parameterVersionBO.setConditionNamesOrder(parameterChangeDTO.getConditionNamesOrder());

            List<ParameterConditionChangeDTO> parameterConditionChanges = parameterChangeDTO.getParameterConditionChanges();
            if (CollectionUtils.isNotEmpty(parameterConditionChanges)) {
                List<ParameterConditionVersionBO> parameterConditionVersionBOS = BeanUtil.createListFromProperties(parameterConditionChanges, ParameterConditionVersionBO.class);
                parameterVersionBO.setParameterConditionVersions(parameterConditionVersionBOS);
            }

            return parameterVersionBO;
        }).collect(Collectors.toList());
    }

    /**
     * 将条件变更DTO列表转换为条件版本BO列表
     */
    @Named("mapConditionChangesToVersions")
    protected List<ConditionVersionBO> mapConditionChangesToVersions(List<ConditionChangeDTO> conditionChanges) {
        if (CollectionUtils.isEmpty(conditionChanges)) {
            return new ArrayList<>();
        }

        return conditionChanges.stream().map(conditionChangeDTO -> {
            ConditionVersionBO conditionVersionBO = BeanUtil.createFromProperties(conditionChangeDTO, ConditionVersionBO.class);
            conditionVersionBO.setCondition(BeanUtil.createFromProperties(conditionChangeDTO, OConditionDO.class));
            conditionVersionBO.setExpression(JSON.toJSONString(conditionChangeDTO.getExpression()));
            return conditionVersionBO;
        }).collect(Collectors.toList());
    }

    /**
     * 提取参数键列表
     */
    @Named("extractParameterKeys")
    protected List<String> extractParameterKeys(List<ParameterVersionBO> parameterVersions) {
        if (CollectionUtils.isEmpty(parameterVersions)) {
            return null;
        }
        return parameterVersions.stream()
                .map(ParameterVersionBO::getParameterKey)
                .collect(Collectors.toList());
    }

    /**
     * 提取条件ID列表
     */
    @Named("extractConditionIds")
    protected List<String> extractConditionIds(List<ConditionVersionBO> conditionVersions) {
        if (CollectionUtils.isEmpty(conditionVersions)) {
            return null;
        }
        return conditionVersions.stream()
                .map(ConditionVersionBO::getConditionId)
                .collect(Collectors.toList());
    }
}
