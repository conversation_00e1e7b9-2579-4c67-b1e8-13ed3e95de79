package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.oswitch.manager.namespace.model.NamespaceSnapshot;
import lombok.Data;

import java.util.List;

@Data
public class ReleaseOrderChangesDTO {
    /**
     * 本次发布单涉及的参数变更
     */
    private List<ParameterDetailDTO> parameterChanges;
    /**
     * 本次发布单涉及的条件变更
     */
    private List<ConditionDetailDTO> conditionChanges;
    /**
     * 上一个版本的命名空间快照
     */
    private NamespaceSnapshot previousNamespace;
}
