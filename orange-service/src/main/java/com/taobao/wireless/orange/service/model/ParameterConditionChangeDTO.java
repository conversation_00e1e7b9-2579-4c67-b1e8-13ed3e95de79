package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ParameterConditionChangeDTO {
    /**
     * 参数键名(当本次引用的非新增参数时使用)
     */
    private String conditionId;

    /**
     * 条件名(当本次引用的为新增参数时使用)
     */
    private String conditionName;

    /**
     * 参数条件值
     */
    @NotBlank(message = "参数条件值不能为空")
    private String value;

    /**
     * 变更类型
     */
    @EnumValidation(clazz = ChangeType.class, message = "变更类型不合法")
    private ChangeType changeType;
}
