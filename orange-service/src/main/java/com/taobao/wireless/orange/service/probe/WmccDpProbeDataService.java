package com.taobao.wireless.orange.service.probe;

import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.taobao.wireless.orange.publish.probe.model.AserverProbe;
import com.taobao.wireless.orange.service.mapper.DiffProbeMapper;
import com.taobao.wireless.orange.text.manager.model.DiffProbeFile;
import com.taobao.wireless.orange.text.manager.util.DiffProbeFileUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@DiamondListener(dataId = "orange-probe-5", groupId = "orange")
@Slf4j(topic = "probe")
public class WmccDpProbeDataService implements DiamondDataCallback {

    @Autowired
    private DiffProbeMapper diffProbeMapper;

    @Getter
    private List<AserverProbe> probes = List.of();

    @Override
    public void received(String data) {
        try {
            DiffProbeFile probeFile = DiffProbeFileUtils.decodeProbeFile(data);
            if (probeFile != null) {
                probes = diffProbeMapper.convert(probeFile);
            }
        } catch (Throwable e) {
            log.error("DpProbeDataService received error", e);
        }
    }
}