package com.taobao.wireless.orange.service.model;

import com.taobao.wmcc.client.publish.PublishTaskInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
public class RatioGrayProgressDTO {
    /**
     * 正式发布进度
     */
    private ReleaseProgressNode releaseProgressNode;

    /**
     * 灰度发布进度列表
     */
    private List<RatioGrayProgressNode> ratioGrayProgressNodes;

    @Data
    public static class ReleaseProgressNode {
        /**
         * 开始时间（用户发起灰度时间）
         */
        private Date startTime;
        /**
         * 探针生成时间(定时任务调度时间)
         */
        private Date scheduleTime;
        /**
         * WMCC 任务ID
         */
        private String agatewareTaskId;

        /**
         * WMCC 任务状态
         */
        private PublishTaskInfo agatewareTaskInfo;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class RatioGrayProgressNode extends ReleaseProgressNode {
        /**
         * 灰度比例
         */
        private Integer grayRatio;
    }
}
