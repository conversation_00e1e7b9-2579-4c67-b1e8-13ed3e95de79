package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ParameterDetailDTO extends ParameterDTO {
    /**
     * 条件顺序
     */
    private String conditionsOrder;
    /**
     * 最新发布版本号
     */
    private String releaseVersion;
    /**
     * 修改前发布版本号
     */
    private String previousReleaseVersion;
    /**
     * 参数条件值
     */
    private List<ParameterConditionDTO> parameterConditions;
    /**
     * 当前进行中的发布单
     */
    private ReleaseOrderDTO inPublishReleaseOrder;

    private ChangeType changeType;
}
