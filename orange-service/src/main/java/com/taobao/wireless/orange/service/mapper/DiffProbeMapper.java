package com.taobao.wireless.orange.service.mapper;

import com.taobao.wireless.orange.common.constant.enums.AserverIndexType;
import com.taobao.wireless.orange.publish.probe.model.AserverProbe;
import com.taobao.wireless.orange.text.manager.model.DiffProbe;
import com.taobao.wireless.orange.text.manager.model.DiffProbeFile;
import com.taobao.wireless.orange.text.manager.model.ProbeMetaItem;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.Objects;

/**
 * MapStruct映射器，用于DiffProbe相关的对象转换
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Mapper(componentModel = "spring")
public interface DiffProbeMapper {

    /**
     * 将DiffProbeFile转换为AserverProbe列表
     *
     * @param diffProbeFile 差异探针文件
     * @return AserverProbe列表
     */
    default List<AserverProbe> convert(DiffProbeFile diffProbeFile) {
        if (diffProbeFile == null || CollectionUtils.isEmpty(diffProbeFile.getList())) {
            return List.of();
        }

        return diffProbeFile.getList().stream()
                .filter(Objects::nonNull)
                .map(diffProbe -> toAserverProbe(diffProbe, diffProbeFile))
                .toList();
    }

    /**
     * 将DiffProbe转换为AserverProbe
     *
     * @param diffProbe     差异探针
     * @param diffProbeFile 差异探针文件（用于获取全局配置）
     * @return AserverProbe对象
     */
    default AserverProbe toAserverProbe(DiffProbe diffProbe, DiffProbeFile diffProbeFile) {
        return AserverProbe.builder()
                .appkey(diffProbe.getAppKey())
                .indexType(AserverIndexType.DP.getCode())
                .host(diffProbe.getCdn())
                .protocol(diffProbeFile.getProtocol())
                .probeVersion(diffProbe.getProbeVersion())
                .versions(toVersionList(diffProbe.getMetaList()))
                .build();
    }

    /**
     * 将ProbeMetaItem转换为AserverProbe.Version
     *
     * @param metaItem 探针元数据项
     * @return AserverProbe.Version对象
     */
    AserverProbe.Version toVersion(ProbeMetaItem metaItem);

    /**
     * 将ProbeMetaItem列表转换为AserverProbe.Version列表
     *
     * @param metaList 探针元数据项列表
     * @return AserverProbe.Version列表
     */
    List<AserverProbe.Version> toVersionList(List<ProbeMetaItem> metaList);
}
