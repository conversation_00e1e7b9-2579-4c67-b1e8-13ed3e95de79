package com.taobao.wireless.orange.service;


import com.alibaba.fastjson.JSON;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.external.tiga.TigaService;
import com.taobao.wireless.orange.oswitch.dal.dao.OConditionDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionDO;
import com.taobao.wireless.orange.oswitch.manager.namespace.ConditionManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.mapper.ExpressionMapper;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionExpressionBO;
import com.taobao.wireless.orange.service.mapper.ConditionMapper;
import com.taobao.wireless.orange.service.model.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ConditionService {

    @Autowired
    private ConditionManager conditionManager;

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private ConditionMapper conditionMapper;

    @Autowired
    private ExpressionMapper expressionMapper;
    @Autowired
    private TigaService tigaService;

    /**
     * 更新条件
     *
     * @param condition 条件更新对象
     */
    @AttributeValidate
    public Result<Void> update(@NotNull(message = "条件更新对象不能为空") ConditionDirectUpdateDTO condition) {
        conditionDAO.lambdaUpdate()
                .eq(OConditionDO::getConditionId, condition.getConditionId())
                .set(StringUtils.isNotBlank(condition.getName()), OConditionDO::getName, condition.getName())
                .set(StringUtils.isNotBlank(condition.getColor()), OConditionDO::getColor, condition.getColor())
                .update();
        return Result.success();
    }

    @AttributeValidate
    public Result<Map<String, ConditionDTO>> getConditionMapByIds(@NotEmpty(message = "条件ID列表不能为空") List<String> conditionIds) {
        return Pipe.of(conditionIds)
                .map(conditionDAO::getConditionMap)
                .map(m -> m.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey,
                                e -> BeanUtil.createFromProperties(e.getValue(), ConditionDTO.class))))
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public PaginationResult<ConditionDetailDTO> query(@NotNull(message = "条件查询条件不能为空") ConditionQueryDTO query, Pagination pagination) {
        return Pipe.of(query)
                .map(BeanUtil.createFromProperties(ConditionBO.class))
                .map(q -> conditionManager.query(q, pagination))
                .map(r -> {
                    PaginationResult<ConditionDetailDTO> result = BeanUtil.createFromProperties(r, PaginationResult.class);
                    result.setData(conditionMapper.toConditionDetailDTOList(r.getRecords()));
                    return result;
                })
                .get();
    }

    /**
     * 获取所有条件(用户下拉选择)
     *
     * @param query
     * @return
     */
    @AttributeValidate
    public Result<List<ConditionDTO>> getAll(@NotNull(message = "条件查询条件不能为空") ConditionQueryDTO query) {
        return Pipe.of(query)
                .map(BeanUtil.createFromProperties(ConditionBO.class))
                .map(q -> conditionManager.getAllOnlineConditions(q))
                .map(conditionMapper::toConditionDTOList)
                .map(Result::new)
                .get();
    }

    /**
     * 获取条件线上版本的详情
     *
     * @param conditionId 条件ID
     * @return
     */
    @AttributeValidate
    public Result<ConditionDetailDTO> getByConditionId(@NotBlank(message = "条件ID不能为空") String conditionId) {
        return Pipe.of(conditionId)
                .map(conditionManager::getOnlineConditionDetailByConditionId)
                .map(conditionMapper::toConditionDetailDTO)
                .map(Result::new)
                .get();
    }

    /**
     * 查询设备量
     */
    @AttributeValidate
    public Result<Long> countDevices(ExpressionDeviceCountQuery query) {
        return Pipe.of(JSON.parseObject(JSON.toJSONString(query.getExpression()),
                        ConditionExpressionBO.class))
                .map(expressionMapper::toLogicExpression)
                .map(e -> tigaService.getGrayscaleDeviceCount(query.getAppKey(), e))
                .map(Result::new)
                .get();
    }
}
