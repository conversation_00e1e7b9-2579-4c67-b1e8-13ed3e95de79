package com.taobao.wireless.orange.service.model;

import com.taobao.wmcc.client.constants.BriefTaskStatus;
import lombok.Data;

import java.util.List;

@Data
public class ReleaseOrderDetailDTO extends ReleaseOrderDTO {
    private String namespaceName;
    /**
     * 本次发布单涉及变更的参数
     */
    private List<String> parameterKeys;
    /**
     * 本次发布单涉及变更的条件
     */
    private List<String> conditionIds;

    /**
     * WMCC 任务ID
     */
    private String agatewareTaskId;

    /**
     * WMCC 任务状态
     */
    private BriefTaskStatus agatewareTaskInfo;

    /**
     * 发布单阶段列表
     */
    private List<ReleaseOrderStageDTO> releaseOrderStages;
}
