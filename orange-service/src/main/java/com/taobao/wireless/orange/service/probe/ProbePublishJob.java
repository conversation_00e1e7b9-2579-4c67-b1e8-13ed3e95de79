package com.taobao.wireless.orange.service.probe;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.wireless.orange.publish.probe.SwitchProbePublishService;
import com.taobao.wireless.orange.publish.probe.TextProbePublishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "probe")
public class ProbePublishJob extends JavaProcessor {
    @Autowired
    private SwitchProbePublishService switchProbePublishService;

    @Autowired
    private TextProbePublishService textProbePublishService;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            switchProbePublishService.publish();
            textProbePublishService.publish();
            log.info("Probe_generate success");
            return new ProcessResult(true);
        } catch (Throwable e) {
            log.error("Probe_generate error", e);
            return new ProcessResult(false, e.getMessage());
        }
    }

    @Override
    public void kill(JobContext context) {
        // 任务超时被中断
        log.error("Probe_generate job killed");
        super.kill(context);
    }
}
