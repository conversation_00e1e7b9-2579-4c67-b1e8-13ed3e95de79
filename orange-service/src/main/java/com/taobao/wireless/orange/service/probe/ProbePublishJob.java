package com.taobao.wireless.orange.service.probe;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.wireless.orange.publish.probe.AbstractProbePublishService;
import com.taobao.wireless.orange.publish.probe.SwitchProbePublishService;
import com.taobao.wireless.orange.publish.probe.TextProbePublishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "probe")
public class ProbePublishJob extends JavaProcessor {
    @Autowired
    private SwitchProbePublishService switchProbePublishService;

    @Autowired
    private TextProbePublishService textProbePublishService;

    @Override
    public ProcessResult process(JobContext context) {
        Throwable publishException = null;
        try {
            switchProbePublishService.publish();
        } catch (Throwable e) {
            log.error("Switch_probe_publish error", e);
            publishException = e;
        }

        try {
            textProbePublishService.publish();
        } catch (Throwable e) {
            log.error("Text_probe_publish error", e);
            publishException = e;
        }

        if (publishException != null) {
            log.error("Probe_generate error", publishException);
            return new ProcessResult(false, publishException.getMessage());
        }

        log.info("Probe_generate success");
        return new ProcessResult(true);
    }

    @Override
    public void kill(JobContext context) {
        // 任务超时被中断
        log.error("Probe_generate job killed");
        super.kill(context);
    }
}
