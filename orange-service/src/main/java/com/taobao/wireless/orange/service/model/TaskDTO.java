package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.TaskBizType;
import com.taobao.wireless.orange.common.constant.enums.TaskStatus;
import com.taobao.wireless.orange.common.constant.enums.TaskType;
import lombok.Data;

import java.util.Date;

/**
 * 任务DTO
 */
@Data
public class TaskDTO {
    /**
     * 任务唯一ID
     */
    private String taskId;

    /**
     * 任务类型
     */
    private TaskType type;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 关联业务ID
     */
    private String bizId;

    /**
     * 关联业务类型
     */
    private TaskBizType bizType;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务创建时间
     */
    private Date gmtCreate;

    /**
     * 任务修改时间
     */
    private Date gmtModified;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;
}
