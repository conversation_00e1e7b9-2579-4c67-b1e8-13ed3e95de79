package com.taobao.wireless.orange.service.probe;

import com.alibaba.fastjson2.JSON;
import com.taobao.wireless.agateware.client.AgatewareClient;
import com.taobao.wireless.agateware.client.AgatewareClientImpl;
import com.taobao.wireless.agateware.client.listener.KVConfigListener;
import com.taobao.wireless.orange.publish.probe.model.AserverProbe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static com.taobao.wireless.orange.external.wmcc.WmccPublishService.CONFIG_NAMESPACE;
import static com.taobao.wireless.orange.external.wmcc.WmccPublishService.CONFIG_TYPE;

@Slf4j
@Service
public class AgatewareProbeDataService {

    private final ProbeConfigAcquirer probeConfigAcquirer = new ProbeConfigAcquirer();

    public AgatewareProbeDataService() {
        AgatewareClient client = new AgatewareClientImpl();
        try {
            client.start();
            client.addListener(CONFIG_NAMESPACE, CONFIG_TYPE, probeConfigAcquirer);
            log.info("Agateware 客户端启动成功并已注册配置监听，namespace={}, type={}", CONFIG_NAMESPACE, CONFIG_TYPE);
        } catch (Exception ex) {
            log.error("Agateware 客户端启动或注册监听失败，namespace={}, type={}", CONFIG_NAMESPACE, CONFIG_TYPE, ex);
        }
    }

    public List<AserverProbe> getProbes() {
        Collection<String> values = probeConfigAcquirer.getAllValues();
        if (values == null || values.isEmpty()) {
            return List.of();
        }

        List<AserverProbe> parsed = new ArrayList<>(values.size());
        for (String value : values) {
            if (StringUtils.isBlank(value)) {
                continue;
            }
            try {
                AserverProbe probe = JSON.parseObject(value, AserverProbe.class);
                if (probe != null) {
                    parsed.add(probe);
                }
            } catch (Exception parseEx) {
                log.warn("解析 AserverProbe 失败，忽略该条配置: " + value, parseEx);
            }
        }
        return parsed;
    }

    private static class ProbeConfigAcquirer extends KVConfigListener<String, String> {
        @Override
        protected String parseKey(String configKey) {
            return configKey;
        }

        @Override
        protected String parseValue(String configValue) {
            return configValue;
        }
    }
}
