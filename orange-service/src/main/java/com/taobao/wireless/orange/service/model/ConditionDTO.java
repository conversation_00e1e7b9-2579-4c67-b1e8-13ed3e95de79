package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.ConditionStatus;
import lombok.Data;

import java.util.Date;

@Data
public class ConditionDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 条件ID
     */
    private String conditionId;

    /**
     * 应用KEY
     */
    private String appKey;

    /**
     * 命名空间ID
     */
    private String namespaceId;

    /**
     * 条件名称
     */
    private String name;

    /**
     * 条件颜色
     */
    private String color;

    /**
     * 条件表达式
     */
    private ConditionExpressionDTO expression;

    /**
     * 状态
     */
    private ConditionStatus status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;
}
