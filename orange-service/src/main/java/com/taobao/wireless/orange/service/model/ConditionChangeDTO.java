package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import lombok.Data;

@Data
public class ConditionChangeDTO {
    /**
     * 条件id(修改和删除条件时使用)
     */
    private String conditionId;

    /**
     * 条件名称（新增条件时必填）
     */
    private String name;

    /**
     * 变更类型
     */
    @EnumValidation(clazz = ChangeType.class, message = "变更类型不合法")
    private ChangeType changeType;

    /**
     * 条件表达式
     */
    private ConditionExpressionDTO expression;

    /**
     * 修改前发布版本号
     */
    private String previousReleaseVersion;
}
