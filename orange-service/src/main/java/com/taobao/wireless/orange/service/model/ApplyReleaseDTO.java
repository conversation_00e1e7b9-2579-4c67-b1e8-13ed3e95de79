package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.annotation.EnumValidation;
import com.taobao.wireless.orange.common.constant.enums.ReleaseLevel;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ApplyReleaseDTO {
    @NotNull
    @EnumValidation(clazz = ReleaseLevel.class, message = "发布类型不合法")
    private ReleaseLevel releaseLevel;

    @NotBlank(message = "原因不能为空")
    private String reason;
}
