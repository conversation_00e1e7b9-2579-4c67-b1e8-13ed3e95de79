package com.taobao.wireless.orange.service.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import com.taobao.wireless.orange.common.constant.enums.ParameterStatus;
import com.taobao.wireless.orange.common.constant.enums.ParameterValueType;

/**
 * 参数表
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Data
public class ParameterDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 参数ID
     */
    private String parameterId;

    /**
     * 应用KEY
     */
    private String appKey;

    /**
     * 命名空间ID
     */
    private String namespaceId;

    /**
     * 参数键名
     */
    private String parameterKey;

    /**
     * 参数值类型
     */
    private ParameterValueType valueType;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态
     */
    private ParameterStatus status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;
}
