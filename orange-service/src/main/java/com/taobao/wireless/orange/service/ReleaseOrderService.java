package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.oswitch.manager.common.PageUtil;
import com.taobao.wireless.orange.oswitch.manager.release.ReleaseOrderManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.*;
import com.taobao.wireless.orange.service.mapper.ReleaseOrderMapper;
import com.taobao.wireless.orange.service.model.*;
import com.taobao.wireless.tiga.release.console.api.task.model.dto.TaskStageListDTO;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateInstanceDTO;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ReleaseOrderService {
    @Autowired
    private ReleaseOrderManager releaseOrderManager;
    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;
    @Autowired
    private ReleaseOrderMapper releaseOrderMapper;

    /**
     * 查询发布单列表
     *
     * @param query      发布单查询条件
     * @param pagination 分页信息
     * @return 发布单列表
     */
    @AttributeValidate
    public PaginationResult<ReleaseOrderDTO> query(@NotNull(message = "发布单查询条件不能为空") ReleaseOrderQueryDTO query,
                                                   @NotNull(message = "分页信息不能为空") Pagination pagination) {
        return Pipe.of(query)
                .map(BeanUtil.createFromProperties(ReleaseOrderBO.class))
                .map(q -> releaseOrderManager.query(q, pagination))
                .map(PageUtil.convertToPaginationResult(ReleaseOrderDTO.class))
                .get();
    }

    /**
     * 获取发布单状态统计
     *
     * @param namespaceId 命名空间ID
     * @return 发布单状态统计
     */
    @AttributeValidate
    public Result<Map<ReleaseOrderStatus, Long>> getCountGroupByStatus(@NotBlank(message = "命名空间ID不能为空") String namespaceId) {
        return Pipe.of(namespaceId)
                .map(releaseOrderDAO::getCountGroupByStatus)
                .map(Result::new)
                .get();
    }

    /**
     * 获取发布单详情
     *
     * @param releaseVersion 发布版本号
     * @return 发布单详情
     */
    @AttributeValidate
    public Result<ReleaseOrderDetailDTO> getDetail(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getDetail)
                .map(releaseOrderMapper::toReleaseOrderDetailDTO)
                .map(Result::new)
                .get();
    }

    /**
     * 获取发布单变更内容
     *
     * @param releaseVersion 发布版本号
     * @return 发布单变更内容
     */
    @AttributeValidate
    public Result<ReleaseOrderChangesDTO> getChanges(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getChanges)
                .map(releaseOrderMapper::toReleaseOrderChangesDTO)
                .map(Result::new)
                .get();
    }

    /**
     * 获取发布单操作记录
     *
     * @param releaseVersion 发布版本号
     * @param operationTypes 操作类型列表
     * @return 发布单操作记录
     */
    @AttributeValidate
    public Result<List<ReleaseOrderOperationDTO>> getOperations(@NotBlank(message = "发布版本号不能为空") String releaseVersion,
                                                                @Nullable List<OperationType> operationTypes) {
        return Pipe.of(releaseOrderManager.getOperations(releaseVersion, operationTypes))
                .map(BeanUtil.createListFromProperties(ReleaseOrderOperationDTO.class))
                .map(Result::new)
                .get();
    }

    /**
     * 创建发布单
     *
     * @param releaseOrder 发布单创建条件
     * @return 发布版本号
     */
    @AttributeValidate
    public Result<String> create(@NotNull(message = "发布单创建条件不能为空") ReleaseOrderCreateDTO releaseOrder) {
        return Pipe.of(releaseOrder)
                .map(releaseOrderMapper::toReleaseOrderBO)
                .map(releaseOrderManager::create)
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public Result<Void> applyRelease(String releaseVersion, @NotNull(message = "发布申请不能为空") ApplyReleaseDTO applyRelease) {
        releaseOrderManager.applyRelease(releaseVersion, BeanUtil.createFromProperties(applyRelease, ApplyReleaseCommandBO.class));
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> publish(String releaseVersion) {
        releaseOrderManager.publish(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> cancel(String releaseVersion) {
        releaseOrderManager.cancel(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> ratioGray(String releaseVersion, RatioGrayDTO ratioGray) {
        releaseOrderManager.ratioGray(releaseVersion, BeanUtil.createFromProperties(ratioGray, RatioGrayCommandBO.class));
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> skip(String releaseVersion, SkipCommand skipCommand) {
        releaseOrderManager.skip(releaseVersion, BeanUtil.createFromProperties(skipCommand, SkipCommandBO.class));
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> tigaGray(String releaseVersion, SmallFlowGrayCommand smallFlowGrayCommand) {
        releaseOrderManager.tigaGray(releaseVersion, BeanUtil.createFromProperties(smallFlowGrayCommand, SmallFlowGrayCommandBO.class));
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> verifyReply(String releaseVersion, @NotNull(message = "验证回复不能为空") VerifyReplyDTO verifyReplyDTO) {
        releaseOrderManager.verifyReply(releaseVersion, BeanUtil.createFromProperties(verifyReplyDTO, VerifyCommandBO.class));
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> startVerify(String releaseVersion) {
        releaseOrderManager.startVerify(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<DebugInfoDTO> getDebugInfo(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getDebugInfo)
                .map(BeanUtil.createFromProperties(DebugInfoDTO.class))
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public Result<TaskStageListDTO> getTigaTaskStageList(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getTigaTaskStageList)
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public Result<List<TemplateInstanceDTO>> getGrayTemplates(String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getGrayTemplates)
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public Result<Void> changefreeCallback(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        releaseOrderManager.refreshApplyStageStatus(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<List<Map<String, String>>> getScanBetaLogs(ScanBetaLogQuery query) {
        ScanBetaLogQueryBO queryBO = new ScanBetaLogQueryBO();
        queryBO.setReleaseVersion(query.getReleaseVersion());
        queryBO.setType(query.getType().name());
        List<Map<String, String>> logs = releaseOrderManager.getBetaScanLogs(queryBO, query.getPagination());
        return Result.success(logs);
    }

    @AttributeValidate
    public Result<RatioGrayProgressDTO> getRatioGrayProgress(@NotBlank(message = "发布版本号不能为空") String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getRatioGrayProgress)
                .map(releaseOrderMapper::toRatioGrayProgressDTO)
                .map(Result::new)
                .get();
    }
}
