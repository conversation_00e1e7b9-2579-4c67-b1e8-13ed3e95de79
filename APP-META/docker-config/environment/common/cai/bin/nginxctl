#!/bin/bash

ARGV="$@"
cd $(dirname $0)/..
BASE=$(pwd)
BASE_CONF_DIR="$BASE/conf"

# if set to "1", skip use RUNNING_CONF_DIR
test -z "$RUNNING_SKIP" && RUNNING_SKIP=0
if [ "$RUNNING_SKIP" -ne 1 ]; then
    RUNNING_CONF_DIR="$BASE/.running_conf"
    NGINX="/opt/taobao/tengine/bin/tengine -c $RUNNING_CONF_DIR/nginx-proxy.conf -p $BASE"
else
    NGINX="/opt/taobao/tengine/bin/tengine -c $BASE_CONF_DIR/nginx-proxy.conf -p $BASE"
fi
NGINX_PID="$BASE/logs/tengine-proxy.pid"
CURL="/usr/bin/curl"
STATUSURL="http://localhost:80/status.taobao"
LSTATUSURL="http://localhost:80/nginx_status"

ULIMIT_MAX_FILES="ulimit -S -n $(ulimit -H -n)"

if [ "x$ULIMIT_MAX_FILES" != "x" ] ; then
    $ULIMIT_MAX_FILES
fi

ERROR=0

if [ "x$ARGV" = "x" ] ; then
    echo "$0 {start|stop|restart|reload|configtest|quit|rotate|nginx_status|status|upgrade}"
    exit 0
fi

merge_conf() {
    if [ "$RUNNING_SKIP" -ne 1 ]; then
        echo "init $RUNNING_CONF_DIR/"
        rm -fr $RUNNING_CONF_DIR/ && mkdir -p $RUNNING_CONF_DIR/
        echo "copy from /opt/taobao/tengine/conf/  to  $RUNNING_CONF_DIR/"
        rsync -a -r --copy-unsafe-links /opt/taobao/tengine/conf/ $RUNNING_CONF_DIR/
        echo "copy from $BASE_CONF_DIR/   to  $RUNNING_CONF_DIR/"
        rsync -a -r --copy-unsafe-links $BASE_CONF_DIR/ $RUNNING_CONF_DIR/
        echo "init $RUNNING_CONF_DIR/ done"
    fi
}

if [ ! -d "$BASE/data" ] ; then
    mkdir $BASE/data
fi

if [ ! -d "$BASE/logs" ] ; then
    mkdir $BASE/logs
fi

case "$ARGV" in
    start)
    merge_conf
    echo "$NGINX"
    $NGINX
    ERROR=$?
    if [ $ERROR -eq 0 ] ; then
        $CURL --silent $LSTATUSURL -H 'Host: status.taobao.com' -I | grep 'HTTP/1.1 200' 
        ERROR=$?
    fi
    ;;
    stop|reload|quit)
    if [ $ARGV = "reload" ];then
        merge_conf
    fi
    echo "$NGINX $ARGV"
    $NGINX -s $ARGV
    ERROR=$?
    ;;
    restart)
    if [ -f $NGINX_PID ] ; then
        echo "$NGINX -s stop"
        $NGINX -s stop
        ERROR=$?
        [ $ERROR -eq 0 ] || exit $ERROR
        sleep 1
    fi
    merge_conf
    echo "$NGINX"
    $NGINX
    ERROR=$?
    if [ $ERROR -eq 0 ] ; then
        $CURL --silent $LSTATUSURL -H 'Host: status.taobao.com' -I | grep 'HTTP/1.1 200' 
        ERROR=$?
    fi
    ;;
    rotate)
    echo "$NGINX -s reopen"
    $NGINX -s reopen
    ERROR=$?
    ;;
    status)
    echo 'checking nginx online ...'
    $CURL --silent $STATUSURL -H 'Host: status.taobao.com' -I | grep 'HTTP/1.1 200' > /dev/null
    ERROR=$?
    if [ $ERROR -eq 0 ] ; then
        echo 'nginx online'
    else
        echo 'nginx offline'
    fi
    ;;
    nginx_status)
    echo 'checking nginx working ...'
    $CURL --silent $LSTATUSURL -H 'Host: status.taobao.com' -I | grep 'HTTP/1.1 200' > /dev/null
    ERROR=$?
    if [ $ERROR -eq 0 ] ; then
        echo 'nginx ok'
    else
        echo 'nginx failed'
    fi
    ;;
    configtest)
    merge_conf
    echo "$NGINX -t"
    $NGINX -t
    ERROR=$?
    ;;
    upgrade)
    echo "Nginx upagrading, fork the new master and worker processes."
    if [ ! -f $NGINX_PID ] ; then
        $NGINX
        exit $?
    fi
    merge_conf
    kill -USR2 `cat $NGINX_PID`
    ERROR=$?
    if [ $ERROR -ne 0 ] ; then
        echo "Fork failed and check your configure or your $NGINX_PID."
        exit $ERROR
    fi
    sleep 1
    echo "Done, stop the old master and worker processes gracefully "
    kill -QUIT `cat $NGINX_PID.oldbin`
    ERROR=$?
    ;;
    *)
    echo "$0 {start|stop|restart|reload|configtest|quit|rotate|status|nginx_status|upgrade}"
    ERROR=$?
esac

exit $ERROR
