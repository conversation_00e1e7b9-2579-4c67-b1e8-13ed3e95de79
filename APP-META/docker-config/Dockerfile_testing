# 基础镜像的Dockerfile ： https://yuque.antfin.com/aone/docker/rm2g1d
# 基于基础镜像
FROM hub.docker.alibaba-inc.com/ali/os:8u

# 指定应用名字，配置在$APP_NAME.release文件里
# 从 build.tools.docker.args=--build-arg APP_NAME=${APP_NAME} 传入
ARG APP_NAME
ENV APP_NAME=${APP_NAME}

# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件
RUN rpm -ivh --nodeps http://yum.tbsite.net/taobao/7/x86_64/current/ajdk21/ajdk21-21.0.3.0.3-20240613114250.alios7.x86_64.rpm && \
ln -s /opt/taobao/install/ajdk21_21.0.3.0.3/ /opt/taobao/java && \
rpm -ivh --nodeps "http://yum.tbsite.net/taobao/7/x86_64/current/tengine-proxy/tengine-proxy-2.1.13-20170802132414.el7u2.x86_64.rpm" && \
rpm -ivh --nodeps "http://yum.tbsite.net/alios/7/os/x86_64/Packages/taobao-cronolog-1.6.2-15.alios7.x86_64.rpm" && \
mkdir -p /home/<USER>/$APP_NAME/target && \
echo "/home/<USER>/$APP_NAME/bin/appctl.sh stop" > /home/<USER>/stop.sh && \
echo "/home/<USER>/$APP_NAME/bin/appctl.sh restart" >> /home/<USER>/start.sh && \
echo "/home/<USER>/$APP_NAME/bin/preload.sh" > /home/<USER>/health.sh && \
chmod +x /home/<USER>/*.sh && \
yum clean all

# 增加jdk路径
ENV PATH "$PATH:/opt/taobao/java/bin/"

# 将应用nginx脚本复制到镜像中
COPY environment/common/cai/ /home/<USER>/cai/

# 将应用启动脚本和配置文件复制到镜像中
COPY environment/common/bin/ /home/<USER>/${APP_NAME}/bin

# 设置文件操作权限
RUN chmod -R a+x /home/<USER>/${APP_NAME}/bin/ /home/<USER>/cai/bin/

# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME /home/<USER>/$APP_NAME/logs \
/home/<USER>/logs \
/home/<USER>/cai/logs \
/home/<USER>/diamond \
/home/<USER>/snapshots \
/home/<USER>/configclient \
/home/<USER>/notify \
/home/<USER>/catserver \
/home/<USER>/liaoyuan-out \
/home/<USER>/vipsrv-dns/vipsrv-cache \
/home/<USER>/vipsrv-failover \
/home/<USER>/vipsrv-cache \
/home/<USER>/csp \
/home/<USER>/.rocketmq_offsets \
/home/<USER>/amsdata \
/home/<USER>/amsdata_all

# 启动容器时进入的工作目录
WORKDIR /home/<USER>/$APP_NAME/bin

#容器启动时自动执行的脚本，我们一般会将应用启动脚本放在这里，相当于系统自启应用
ENTRYPOINT ["/home/<USER>/start.sh"]

# 从Sar包镜像中复制Sar包到应用镜像
# Sar包升级文档：http://gitlab.alibaba-inc.com/middleware-container/pandora/wikis/sar-upgrade
# Sar包镜像列表：http://docker.alibaba-inc.com/#/imageDesc/2751570/detail
COPY --from=reg.docker.alibaba-inc.com/pandora-sar/sar:2025-06-release-fix-hsf-jdk21-SNAPSHOT /opt/taobao-hsf.tgz /home/<USER>/$APP_NAME/target/taobao-hsf.tgz

############# test #################
# 健康检查默认3秒钟做一次,默认做100次也就是300秒,这里设置最多等180秒
ENV ali_start_timeout=180

# 设置打开jpda 调试端口。如果需要则打开下面的注释内容
ENV JPDA_ENABLE=1

# 设置spring profile或者自定义的jvm参数。如果需要则打开下面的注释内容
# ENV SERVICE_OPTS=-Dspring.profiles.active=testing

RUN mkdir -p /etc/ilogtail/users && echo "${APP_NAME}-testing" > /etc/ilogtail/user_defined_id && \
    sudo touch /etc/ilogtail/users/1647796581073291 && sudo touch /etc/ilogtail/users/1647796581073291

# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz
