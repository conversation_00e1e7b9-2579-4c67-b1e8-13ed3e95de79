package com.taobao.wireless.orange.oswitch.manager.common;

import com.taobao.wireless.orange.external.redis.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TairManager {
    @Autowired
    private RedisService redisService;

    private static final String NS_LOCK_PREFIX = "orange_ns_lock_";

    private static final int LOCK_EXPIRE_SECONDS = 30;

    /**
     * NS 加锁
     *
     * @param namespaceId
     * @return 是否加锁成功
     */
    public boolean lockNamespace(String namespaceId) {
        return lock(getNamespaceLockKey(namespaceId), LOCK_EXPIRE_SECONDS);
    }

    /**
     * NS 解锁
     *
     * @param namespaceId
     */
    public void unlockNamespace(String namespaceId) {
        unlock(getNamespaceLockKey(namespaceId));
    }

    private String getNamespaceLockKey(String namespaceId) {
        return NS_LOCK_PREFIX + namespaceId;
    }

    private boolean lock(String key, int expireSeconds) {
        return redisService.setnx(key, "1", expireSeconds) == 1;
    }

    private void unlock(String key) {
        redisService.delete(key);
    }
}
