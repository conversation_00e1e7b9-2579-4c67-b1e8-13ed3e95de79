package com.taobao.wireless.orange.oswitch.manager.release.operation;

import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationAdditionalData;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import lombok.Data;

/**
 * 发布发布单操作上下文
 */
@Data
public class OperationContext<T extends OperationAdditionalData, R extends OperationResult> {
    private String releaseVersion;
    private OReleaseOrderDO releaseOrder;
    private ONamespaceDO namespace;

    private T additionalData; // 用于传递额外的操作数据，如灰度比例等
    private R operationResult; // 操作结果

    public OperationContext(String releaseVersion) {
        this.releaseVersion = releaseVersion;
    }

    public OperationContext(String releaseVersion, T additionalData) {
        this.releaseVersion = releaseVersion;
        this.additionalData = additionalData;
    }
}