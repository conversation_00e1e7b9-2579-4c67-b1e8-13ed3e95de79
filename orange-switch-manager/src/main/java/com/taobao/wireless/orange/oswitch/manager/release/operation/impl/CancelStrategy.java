package com.taobao.wireless.orange.oswitch.manager.release.operation.impl;

import com.alibaba.change.core2.hsf.pojo.ChangeResult;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.oswitch.dal.dao.*;
import com.taobao.wireless.orange.oswitch.dal.entity.*;
import com.taobao.wireless.orange.oswitch.manager.namespace.version.NamespaceVersionManager;
import com.taobao.wireless.orange.oswitch.manager.release.ChangefreeManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationAdditionalData;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import com.taobao.wireless.orange.oswitch.manager.release.operation.AbstractOperationTemplate;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import com.taobao.wireless.orange.oswitch.manager.task.TaskManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 取消操作策略实现
 */
@Component
public class CancelStrategy extends AbstractOperationTemplate<OperationAdditionalData, OperationResult> {

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private TaskManager taskManager;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private OConditionDAO conditionDAO;
    @Autowired
    private ChangefreeManager changefreeManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.CANCEL;
    }

    @Override
    public boolean needLock() {
        return true;
    }

    @Override
    public ReleaseOrderStatus getTargetReleaseOrderStatus(OperationContext context) {
        return ReleaseOrderStatus.CANCELED;
    }

    @Override
    public OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext context) {
        return OReleaseOrderStageDO.builder()
                .status(StageStatus.CANCELED)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void executeOperation(OperationContext context) {
        String releaseVersion = context.getReleaseVersion();
        var releaseOrder = context.getReleaseOrder();

        // 更新版本对象状态为取消
        updateVersionStatusToCanceled(releaseVersion);

        // 使新增的参数和条件失效
        invalidNewParametersAndConditions(releaseVersion);

        // 更新namespace版本
        namespaceVersionManager.createNamespaceVersion(releaseOrder, NamespaceVersionChangeType.CANCEL_RELEASE);

        // 如果百分比发布过，说明有 cf 执行单，需要关闭，否则不需要处理
        if (releaseOrder.getGrayRatio() != null && releaseOrder.getGrayRatio() > 0) {
            changefreeManager.end(releaseOrder.getReleaseVersion(), ChangeResult.CANCEL);
        }
    }

    /**
     * 更新发布单阶段状态
     */
    @Override
    public void updateReleaseOrderStage(OperationContext context) {
        String releaseVersion = context.getReleaseVersion();

        releaseOrderStageDAO.lambdaUpdate()
                .eq(OReleaseOrderStageDO::getReleaseVersion, releaseVersion)
                .eq(OReleaseOrderStageDO::getStatus, StageStatus.IN_PROGRESS)
                .set(OReleaseOrderStageDO::getStatus, StageStatus.CANCELED)
                .update();
    }

    @Override
    public void handleTask(OperationContext context) {
        taskManager.cancelReleaseOrderTask(context.getReleaseVersion());
    }

    /**
     * 更新版本对象状态为取消
     *
     * @param releaseVersion 发布版本
     */
    private void updateVersionStatusToCanceled(String releaseVersion) {
        // 更新参数版本状态
        parameterVersionDAO.lambdaUpdate()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterVersionDO::getStatus, VersionStatus.CANCELED)
                .update();

        // 更新条件版本状态
        conditionVersionDAO.lambdaUpdate()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OConditionVersionDO::getStatus, VersionStatus.CANCELED)
                .update();

        // 更新参数条件版本状态
        parameterConditionVersionDAO.lambdaUpdate()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterConditionVersionDO::getStatus, VersionStatus.CANCELED)
                .update();
    }

    /**
     * 使新增的参数和条件失效
     *
     * @param releaseVersion 发布版本
     */
    private void invalidNewParametersAndConditions(String releaseVersion) {
        // 处理新增的参数
        List<String> newParameterIds = parameterVersionDAO.lambdaQuery()
                .select(OParameterVersionDO::getParameterId)
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .eq(OParameterVersionDO::getChangeType, ChangeType.CREATE)
                .list()
                .stream()
                .map(OParameterVersionDO::getParameterId)
                .toList();

        if (CollectionUtils.isNotEmpty(newParameterIds)) {
            parameterDAO.lambdaUpdate()
                    .in(OParameterDO::getParameterId, newParameterIds)
                    .set(OParameterDO::getStatus, ParameterStatus.INVALID)
                    .update();
        }

        // 处理新增的条件
        List<String> newConditionIds = conditionVersionDAO.lambdaQuery()
                .select(OConditionVersionDO::getConditionId)
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .eq(OConditionVersionDO::getChangeType, ChangeType.CREATE)
                .list()
                .stream()
                .map(OConditionVersionDO::getConditionId)
                .toList();

        if (CollectionUtils.isNotEmpty(newConditionIds)) {
            conditionDAO.lambdaUpdate()
                    .in(OConditionDO::getConditionId, newConditionIds)
                    .set(OConditionDO::getStatus, ConditionStatus.INVALID)
                    .update();
        }
    }
}