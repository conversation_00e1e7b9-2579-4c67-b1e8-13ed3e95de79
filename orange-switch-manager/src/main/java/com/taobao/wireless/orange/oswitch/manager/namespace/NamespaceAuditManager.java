package com.taobao.wireless.orange.oswitch.manager.namespace;

import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.external.amdp.AmdpService;
import com.taobao.wireless.orange.external.amdp.model.User;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class NamespaceAuditManager {
    private static final String DEFAULT_APPROVER = "149016";

    @Autowired
    private AmdpService amdpService;

    /**
     * 获取命名空间的审批人列表
     *
     * 1. 紧急发布：一级和二级主管
     * 2. 非紧急发布：测试人员
     * 3. 无审批人：一级主管
     * 4. 异常情况：默认审批人
     *
     * @param namespace
     * @return
     */
    public List<String> getApprovers(ONamespaceDO namespace, Emergent emergent) {
        var workerId = ThreadContextUtil.getWorkerId();
        if (Emergent.n.equals(emergent)) {
            var approvers = namespace.getTesters().stream()
                    .filter(tester -> !tester.equals(workerId))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(approvers)) {
                return approvers;
            }
        }

        List<String> approvers = new ArrayList<>();
        // 一级主管
        String supervisorEmpId = getSupervisorEmpId(new User(workerId));
        if (StringUtils.isNotBlank(supervisorEmpId)) {
            approvers.add(supervisorEmpId);

            // 紧急审批需要一级和二级主管
            if (Emergent.y.equals(emergent)) {
                String secondSupervisorEmpId = getSupervisorEmpId(new User(supervisorEmpId));
                if (StringUtils.isNotBlank(secondSupervisorEmpId)) {
                    approvers.add(secondSupervisorEmpId);
                }
            }
        }

        return CollectionUtils.isEmpty(approvers) ? List.of(DEFAULT_APPROVER) : approvers;
    }

    private String getSupervisorEmpId(User user) {
        String supervisorEmpId = user.getSupervisorEmpId();
        if (StringUtils.isNotBlank(supervisorEmpId)) {
            return supervisorEmpId;
        }

        User temp1 = amdpService.queryUserByWorkNoList(List.of(user.getEmpId())).get(user.getEmpId());

        if (temp1 != null && StringUtils.isNotBlank(temp1.getSupervisorEmpId())) {
            return temp1.getSupervisorEmpId();
        }
        return DEFAULT_APPROVER;
    }
}
