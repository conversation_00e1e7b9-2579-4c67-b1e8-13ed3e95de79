package com.taobao.wireless.orange.oswitch.manager.namespace;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.constant.enums.ConditionStatus;
import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import com.taobao.wireless.orange.common.constant.enums.NamespaceStatus;
import com.taobao.wireless.orange.common.constant.enums.ParameterStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.common.util.UserUtil;
import com.taobao.wireless.orange.external.mtl.MtlService;
import com.taobao.wireless.orange.oswitch.dal.dao.OConditionDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OParameterDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.*;
import com.taobao.wireless.orange.oswitch.manager.common.PageUtil;
import com.taobao.wireless.orange.oswitch.manager.common.PermissionManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.NamespaceSnapshot;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ONamespaceBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;

@Service
public class NamespaceManager {

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private MtlService mtlService;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private ParameterManager parameterManager;

    @Autowired
    private ConditionManager conditionManager;

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private PermissionManager permissionManager;

    /**
     * 查询命名空间列表，支持分页和条件查询
     *
     * @param query      查询条件
     * @param pagination 分页信息
     * @return 命名空间分页列表结果
     */
    public Page<ONamespaceBO> query(ONamespaceBO query, Pagination pagination) {
        String workerId = ThreadContextUtil.getWorkerId();

        var namespaceDOPage = namespaceDAO.lambdaQuery()
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .eq(StringUtils.isNotBlank(query.getAppKey()), ONamespaceDO::getAppKey, query.getAppKey())
                .eq(query.getStatus() != null, ONamespaceDO::getStatus, query.getStatus())
                .eq(StringUtils.isNotBlank(query.getName()), ONamespaceDO::getName, query.getName())
                .and(BooleanUtils.isTrue(query.getHasPermission()), queryWrapper -> queryWrapper
                        .like(ONamespaceDO::getOwners, workerId)
                        .or()
                        .like(ONamespaceDO::getTesters, workerId))
                .and(StringUtils.isNotBlank(query.getKeyword()), wrapper -> wrapper
                        .like(ONamespaceDO::getName, query.getKeyword())
                        .or()
                        .like(ONamespaceDO::getDescription, query.getKeyword())
                )
                .page(PageUtil.build(pagination));

        var namespaceBOPage = PageUtil.convertToPage(namespaceDOPage, ONamespaceBO.class);
        if (CollectionUtils.isNotEmpty(namespaceBOPage.getRecords())) {
            // 填充额外展示信息
            setBizName(namespaceBOPage.getRecords());
            setParameterCount(namespaceBOPage.getRecords());
        }
        return namespaceBOPage;
    }

    /**
     * 创建新的命名空间
     *
     * @param namespace 命名空间对象
     * @return 创建成功后的命名空间ID
     * @throws CommonException 如果存在同名命名空间或创建失败
     */
    public String create(ONamespaceBO namespace) {
        // 检查是否存在同名命名空间
        checkNamespaceDuplicate(namespace);

        // 设置初始状态
        namespace.setStatus(NamespaceStatus.VALID);

        // 人员工号格式化
        namespace.getOwners().forEach(UserUtil::formatWorkerIdWithoutZero);
        namespace.getTesters().forEach(UserUtil::formatWorkerIdWithoutZero);

        // 生成 namespaceId
        String namespaceId = SerializeUtil.UUID();
        namespace.setNamespaceId(namespaceId);

        if (!namespaceDAO.save(namespace)) {
            throw new CommonException(ExceptionEnum.CREATE_NAMESPACE_FAIL);
        }

        return namespaceId;
    }

    /**
     * 更新命名空间信息
     *
     * @param namespace 要更新的命名空间对象
     * @return 更新是否成功
     * @throws CommonException 如果命名空间不存在
     */
    public boolean update(ONamespaceBO namespace) {
        // 检查是否存在该命名空间
        ONamespaceDO namespaceDO = Optional.ofNullable(
                        namespaceDAO.getByNamespaceId(namespace.getNamespaceId()))
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));

        // 权限校验
        validatePermission(namespaceDO);

        return namespaceDAO.updateByNamespaceId(namespace);
    }

    /**
     * 根据 Namespace ID 获取单个命名空间详情
     *
     * @param namespaceId 命名空间ID
     * @return 命名空间对象
     * @throws CommonException 如果命名空间不存在
     */
    public ONamespaceDO getByNamespaceId(String namespaceId) {
        return Optional.ofNullable(namespaceDAO.getByNamespaceId(namespaceId))
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
    }

    /**
     * 根据 namespaceName 获取单个命名空间详情
     *
     * @param namespaceName
     * @return
     */
    public ONamespaceDO getByNamespaceName(String namespaceName) {
        return namespaceDAO.lambdaQuery()
                .eq(ONamespaceDO::getName, namespaceName)
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
    }

    /**
     * 为当前 namespace 创建快照
     *
     * @param namespaceId
     */
    public NamespaceSnapshot createOnlineSnapshot(String namespaceId) {
        NamespaceSnapshot namespaceSnapshot = new NamespaceSnapshot();

        List<String> parameterIds = parameterDAO.lambdaQuery()
                .eq(OParameterDO::getNamespaceId, namespaceId)
                .eq(OParameterDO::getStatus, ParameterStatus.ONLINE)
                .list()
                .stream()
                .map(OParameterDO::getParameterId)
                .toList();

        if (CollectionUtils.isNotEmpty(parameterIds)) {
            var parameterId2ParameterVersion = parameterManager.getOnlineParameterVersionMap(parameterIds);
            var parameterId2ParamConditions = parameterManager.getOnlineParameterConditionsMap(parameterIds);

            List<NamespaceSnapshot.ParameterSnapshot> parameterSnapshots = parameterId2ParameterVersion.keySet().stream().map(
                    parameterId -> {
                        OParameterVersionDO parameterVersion = parameterId2ParameterVersion.get(parameterId);
                        NamespaceSnapshot.ParameterSnapshot parameterSnapshot =
                                BeanUtil.createFromProperties(parameterVersion, NamespaceSnapshot.ParameterSnapshot.class);
                        List<String> conditionsOrder = parameterManager.getConditionsOrder(parameterVersion);
                        Map<String, OParameterConditionVersionDO> conditionId2ParameterCondition = parameterId2ParamConditions.get(parameterId).stream()
                                .collect(Collectors.toMap(OParameterConditionVersionDO::getConditionId, Function.identity()));
                        List<OParameterConditionVersionDO> parameterConditionVersions = conditionsOrder.stream()
                                .map(conditionId2ParameterCondition::get)
                                .collect(Collectors.toList());
                        parameterConditionVersions.add(conditionId2ParameterCondition.get(DEFAULT_CONDITION_ID));
                        parameterSnapshot.setParameterConditions(BeanUtil.createListFromProperties(parameterConditionVersions,
                                NamespaceSnapshot.ParameterConditionSnapshot.class));
                        return parameterSnapshot;
                    }).collect(Collectors.toList());
            namespaceSnapshot.setParameterSnapshots(parameterSnapshots);
        }

        List<OConditionDO> conditions = conditionDAO.lambdaQuery()
                .eq(OConditionDO::getNamespaceId, namespaceId)
                .eq(OConditionDO::getStatus, ConditionStatus.ONLINE)
                .list()
                .stream()
                .toList();

        if (CollectionUtils.isNotEmpty(conditions)) {
            var conditionId2Condition = conditions.stream().collect(Collectors.toMap(OConditionDO::getConditionId, i -> i));

            List<String> conditionIds = conditions.stream().map(OConditionDO::getConditionId).collect(Collectors.toList());
            var conditionId2ConditionVersion = conditionManager.getOnlineConditionVersionMap(conditionIds);

            List<NamespaceSnapshot.ConditionSnapshot> conditionSnapshots = conditionIds.stream().map(conditionId -> {
                NamespaceSnapshot.ConditionSnapshot conditionSnapshot =
                        BeanUtil.createFromProperties(conditionId2ConditionVersion.get(conditionId),
                                NamespaceSnapshot.ConditionSnapshot.class);
                OConditionDO condition = conditionId2Condition.get(conditionId);

                conditionSnapshot.setName(condition.getName());
                conditionSnapshot.setColor(condition.getColor());
                return conditionSnapshot;
            }).collect(Collectors.toList());

            namespaceSnapshot.setConditionSnapshots(conditionSnapshots);
        }

        return namespaceSnapshot;
    }

    private void validatePermission(ONamespaceDO namespace) {
        if (permissionManager.isAdmin()) {
            return;
        }

        String workerId = ThreadContextUtil.getWorkerId();

        if (namespace != null && (StringUtils.isBlank(workerId) || !namespace.getOwners().contains(workerId))) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION);
        }
    }

    /**
     * 检查是否存在同名命名空间
     *
     * @param namespace 待检查的命名空间对象
     * @throws CommonException 如果存在同名命名空间
     */
    private void checkNamespaceDuplicate(ONamespaceBO namespace) {
        long count = namespaceDAO.lambdaQuery()
                .eq(ONamespaceDO::getAppKey, namespace.getAppKey())
                .eq(ONamespaceDO::getName, namespace.getName())
                .ne(ONamespaceDO::getStatus, NamespaceStatus.DELETE)
                .count();

        if (count > 0) {
            throw new CommonException(ExceptionEnum.NAMESPACE_NAME_DUPLICATE);
        }
    }

    private void setBizName(List<ONamespaceBO> namespaces) {
        var moduleIds = namespaces.stream()
                .filter(n -> NamespaceBizType.MODULE.equals(n.getBizType()))
                .map(i -> Long.parseLong(i.getBizId()))
                .distinct()
                .toList();

        var moduleId2Module = mtlService.getModulesByModuleIds(moduleIds);

        namespaces.stream()
                .filter(n -> NamespaceBizType.MODULE.equals(n.getBizType()))
                .forEach(n -> {
                    Optional.ofNullable(moduleId2Module.get(Long.parseLong(n.getBizId())))
                            .ifPresent(m -> n.setBizName(m.getName()));
                });
    }

    private void setParameterCount(List<ONamespaceBO> namespaces) {
        List<String> parameterIds = namespaces.stream()
                .map(ONamespaceBO::getNamespaceId)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Long> namespaceId2Count = parameterDAO.getCountGroupByNamespaceId(parameterIds);
        namespaces.forEach(n -> {
            n.setParameterCount(namespaceId2Count.getOrDefault(n.getNamespaceId(), 0L));
        });
    }
}
