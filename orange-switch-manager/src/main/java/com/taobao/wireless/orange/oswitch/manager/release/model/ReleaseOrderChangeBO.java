package com.taobao.wireless.orange.oswitch.manager.release.model;

import com.taobao.wireless.orange.oswitch.manager.namespace.model.NamespaceSnapshot;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionVersionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterVersionBO;
import lombok.Data;

import java.util.List;

@Data
public class ReleaseOrderChangeBO {
    private String namespaceId;

    /**
     * 参数变化
     */
    private List<ParameterVersionBO> parameterChanges;

    /**
     * 条件变化
     */
    private List<ConditionVersionBO> conditionChanges;

    /**
     * 上一个 namespace 版本内容
     */
    private NamespaceSnapshot previousNamespace;
}
