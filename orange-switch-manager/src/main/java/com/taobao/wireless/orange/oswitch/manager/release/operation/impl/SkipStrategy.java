package com.taobao.wireless.orange.oswitch.manager.release.operation.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.SkipType;
import com.taobao.wireless.orange.common.constant.enums.StageStatus;
import com.taobao.wireless.orange.common.constant.enums.StageType;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.common.PermissionManager;
import com.taobao.wireless.orange.oswitch.manager.release.TigaGrayManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import com.taobao.wireless.orange.oswitch.manager.release.model.SkipCommandBO;
import com.taobao.wireless.orange.oswitch.manager.release.operation.AbstractOperationTemplate;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import com.taobao.wireless.tiga.release.common.task.TaskCmd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 跳过操作策略实现
 */
@Component
@Slf4j
public class SkipStrategy extends AbstractOperationTemplate<SkipCommandBO, OperationResult> {

    @Autowired
    private PermissionManager permissionManager;

    @Autowired
    private TigaGrayManager tigaGrayManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.SKIP;
    }

    @Override
    public void validatePermission(OperationContext<SkipCommandBO, OperationResult> context) {
        // 暂时开放所有人跳过权限，等功能稳定后在回收
        // if (!permissionManager.isAdmin()) {
        //     throw new CommonException(ExceptionEnum.NO_PERMISSION);
        // }
    }

    @Override
    public void executeOperation(OperationContext<SkipCommandBO, OperationResult> context) {
        // 如果是跳过小流量灰度，需要同时结束小流浪灰度单
        Long tigaTaskId = context.getReleaseOrder().getTigaTaskId();
        if (SkipType.SMALLFLOW_GRAY.equals(context.getAdditionalData().getSkipType()) && tigaTaskId != null) {
            try {
                tigaGrayManager.doTask(context.getReleaseOrder(), TaskCmd.SKIP_TASK, null);
            } catch (Exception e) {
                log.error("releaseOrder[" + context.getReleaseVersion() + "] skip smallflow gray error", e);
            }
        }
    }

    @Override
    public void validateOrderStage(OperationContext<SkipCommandBO, OperationResult> context) {
        // 跳过操作不校验阶段状态
    }

    @Override
    public OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext<SkipCommandBO, OperationResult> context) {
        SkipCommandBO skipCommandBO = context.getAdditionalData();
        SkipType skipType = skipCommandBO.getSkipType();

        if (SkipType.ALL.equals(skipType)) {
            return OReleaseOrderStageDO.builder()
                    .status(StageStatus.SKIPPED)
                    .build();
        }

        StageType stageType = StageType.getByName(skipType.name());
        if (stageType != null) {
            return OReleaseOrderStageDO.builder()
                    .type(stageType)
                    .status(StageStatus.SKIPPED)
                    .build();
        }
        return null;
    }
}