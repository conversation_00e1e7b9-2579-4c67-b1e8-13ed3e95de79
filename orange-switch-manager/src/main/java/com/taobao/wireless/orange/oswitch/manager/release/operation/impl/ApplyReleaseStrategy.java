package com.taobao.wireless.orange.oswitch.manager.release.operation.impl;

import com.alibaba.goc.changefree.model.ChangeCheckRes;
import com.alibaba.goc.changefree.model.CheckStatusEnum;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.release.ChangefreeManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.ApplyReleaseCommandBO;
import com.taobao.wireless.orange.oswitch.manager.release.model.ChangeFreeResult;
import com.taobao.wireless.orange.oswitch.manager.release.operation.AbstractOperationTemplate;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ApplyReleaseStrategy extends AbstractOperationTemplate<ApplyReleaseCommandBO, ChangeFreeResult> {
    @Autowired
    private ChangefreeManager changefreeManager;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Override
    public OperationType getOperationType() {
        return OperationType.APPLY_RELEASE;
    }

    @Override
    public void executeOperation(OperationContext<ApplyReleaseCommandBO, ChangeFreeResult> context) {
        boolean isEmergent = ReleaseLevel.EMERGENT.equals(context.getAdditionalData().getReleaseLevel());
        Emergent emergent = isEmergent ? Emergent.y : Emergent.n;
        ChangeCheckRes result = changefreeManager.check(context.getReleaseOrder(), true, emergent);
        context.setOperationResult(BeanUtil.createFromProperties(result, ChangeFreeResult.class));

        // 如果是紧急发布且准入校验通过，则更新发布单紧急发布标记
        if (CheckStatusEnum.CHECK_PASS.equals(result.getCheckStatusEnum()) && isEmergent) {
            releaseOrderDAO.lambdaUpdate()
                    .eq(OReleaseOrderDO::getReleaseVersion, context.getReleaseVersion())
                    .set(OReleaseOrderDO::getIsEmergent, Emergent.y)
                    .update();
        }
    }

    @Override
    public OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext<ApplyReleaseCommandBO, ChangeFreeResult> context) {
        ChangeFreeResult result = context.getOperationResult();

        return OReleaseOrderStageDO.builder()
                .type(StageType.APPLY_RELEASE)
                .status(result != null && CheckStatusEnum.CHECK_PASS.equals(result.getCheckStatusEnum()) ? StageStatus.SUCCESS : StageStatus.IN_PROGRESS)
                .build();
    }
}
