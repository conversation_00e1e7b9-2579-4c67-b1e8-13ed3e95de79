package com.taobao.wireless.orange.oswitch.manager.release.operation.impl;

import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.StageStatus;
import com.taobao.wireless.orange.common.constant.enums.StageType;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.external.changefree.ChangefreeService;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.namespace.version.NamespaceVersionManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import com.taobao.wireless.orange.oswitch.manager.release.model.RatioGrayCommandBO;
import com.taobao.wireless.orange.oswitch.manager.release.operation.AbstractOperationTemplate;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 灰度操作策略实现
 */
@Slf4j
@Component
public class RatioGrayStrategy extends AbstractOperationTemplate<RatioGrayCommandBO, OperationResult> {

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    private static final int MAX_GRAY_RATIO = 50000;
    @Autowired
    private ChangefreeService changefreeService;

    @Override
    public OperationType getOperationType() {
        return OperationType.RATIO_GRAY;
    }

    @Override
    public boolean needLock() {
        return true;
    }

    @Override
    public void validateParameters(OperationContext<RatioGrayCommandBO, OperationResult> context) {
        // 灰度操作需要校验灰度百分比参数
        RatioGrayCommandBO ratioGrayCommandBO = context.getAdditionalData();
        Integer grayRatio = ratioGrayCommandBO.getGrayRatio();
        if (grayRatio == null || grayRatio <= 0 || grayRatio > MAX_GRAY_RATIO) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "灰度百分比必须在(0," + MAX_GRAY_RATIO + "]区间内");
        }

        var releaseOrder = context.getReleaseOrder();
        if (releaseOrder.getGrayRatio() != null && grayRatio <= releaseOrder.getGrayRatio()) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "灰度百分比必须大于当前值");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void executeOperation(OperationContext<RatioGrayCommandBO, OperationResult> context) {
        var releaseOrder = context.getReleaseOrder();

        // 更新发布百分比
        RatioGrayCommandBO ratioGrayCommandBO = context.getAdditionalData();
        int grayRatio = ratioGrayCommandBO.getGrayRatio();
        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseOrder.getReleaseVersion())
                .set(OReleaseOrderDO::getGrayRatio, grayRatio)
                .update();

        namespaceVersionManager.createNamespaceVersion(releaseOrder, NamespaceVersionChangeType.RATIO_GRAY);

        // 如果是第一次灰度，需要开启 changefree 执行单
        if (releaseOrder.getGrayRatio() == null || releaseOrder.getGrayRatio() == 0) {
            changefreeManager.start(releaseOrder);
        }
    }

    @Override
    public boolean needCheckChangefree() {
        return true;
    }

    @Override
    public OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext<RatioGrayCommandBO, OperationResult> context) {
        RatioGrayCommandBO additionalData = context.getAdditionalData();

        return OReleaseOrderStageDO.builder()
                .type(StageType.RATIO_GRAY)
                .status(MAX_GRAY_RATIO == additionalData.getGrayRatio() ? StageStatus.SUCCESS : StageStatus.IN_PROGRESS)
                .build();
    }
}