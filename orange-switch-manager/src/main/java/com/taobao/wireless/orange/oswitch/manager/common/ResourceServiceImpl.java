package com.taobao.wireless.orange.oswitch.manager.common;

import com.alibaba.fastjson2.JSON;
import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.SerializableObject;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.external.oss.OssService;
import com.taobao.wireless.orange.oswitch.dal.dao.OResourceDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OResourceDO;
import com.taobao.wireless.orange.oswitch.manager.common.model.ResourceBO;
import com.taobao.wireless.orange.publish.resource.ResourceService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;

@Service("textResourceService")
public class ResourceServiceImpl implements ResourceService {
    @Autowired
    private OssService ossService;

    @Autowired
    private OResourceDAO resourceDAO;

    /**
     * Create a resource from a serializable object
     *
     * @param content The object to serialize
     * @param type    The resource type
     * @return The created resource
     */
    @Override
    public ResourceBO create(SerializableObject content, ResourceType type) {
        return create(SerializeUtil.UUID(), content, type);
    }

    @Override
    public ResourceBO create(String resourceId, SerializableObject content, ResourceType type) {
        var resource = resourceDAO.getByResourceId(resourceId);
        if (resource != null) {
            throw new CommonException(ExceptionEnum.RESOURCE_NOT_EXIST);
        }

        byte[] data = content.serialize();
        ossService.uploadData(data, resourceId);

        OResourceDO newResource = new OResourceDO();
        newResource.setResourceId(resourceId);
        newResource.setSrcContent(JSON.toJSONString(content));
        newResource.setData(Base64.getEncoder().encodeToString(data));
        newResource.setMd5(DigestUtils.md5Hex(data));
        newResource.setType(type);

        resourceDAO.save(newResource);
        ResourceBO resourceBO = BeanUtil.createFromProperties(newResource, ResourceBO.class);
        // fixme: 确认资源大小计算是否准确
        resourceBO.setResourceSize(data.length);
        return resourceBO;
    }
}
