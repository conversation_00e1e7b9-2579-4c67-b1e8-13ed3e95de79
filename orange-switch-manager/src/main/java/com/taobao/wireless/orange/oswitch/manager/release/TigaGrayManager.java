package com.taobao.wireless.orange.oswitch.manager.release;

import com.alibaba.fastjson2.JSON;
import com.taobao.wireless.orange.common.constant.enums.NamespaceBizType;
import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import com.taobao.wireless.orange.common.constant.enums.TigaActionType;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.util.UserUtil;
import com.taobao.wireless.orange.external.tiga.TigaService;
import com.taobao.wireless.orange.oswitch.dal.dao.*;
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.manager.namespace.mapper.ExpressionMapper;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionExpressionBO;
import com.taobao.wireless.orange.oswitch.manager.release.model.GrayPublishContent;
import com.taobao.wireless.tiga.release.common.task.TaskCmd;
import com.taobao.wireless.tiga.release.console.api.task.model.dto.TaskStageListDTO;
import com.taobao.wireless.tiga.release.console.api.task.model.param.TaskCreateParam;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateInstanceDTO;
import com.taobao.wireless.tiga.release.expression.ExpressionParser;
import com.taobao.wireless.tiga.release.expression.LogicExpression;
import com.taobao.wireless.tiga.release.expression.LogicalExpressionNode;
import com.taobao.wireless.tiga.release.expression.LogicalOperator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * changefree 服务
 *
 * <AUTHOR>
 */
@Service
public class TigaGrayManager {
    @Autowired
    private TigaService tigaService;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private ChangefreeManager changefreeManager;

    @Value("${orange.console.pro.domain}")
    private String consoleDomain;

    @Value("${orange.switch.cdn.domain}")
    private String cdnDomain;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    private ExpressionMapper expressionMapper;

    public Long startTigaTask(OReleaseOrderDO releaseOrder, Long templateId) {
        TaskCreateParam taskCreateParam = new TaskCreateParam();
        taskCreateParam.setTemplateId(templateId);
        taskCreateParam.setSourceOrderId(releaseOrder.getReleaseVersion());
        taskCreateParam.setAppKeyList(Collections.singletonList(releaseOrder.getAppKey()));
        taskCreateParam.setCfApplyId(getCFApplyId(releaseOrder.getReleaseVersion()));
        taskCreateParam.setActionUrl("https://%s/#/workspace/switch/release-orders/%s".formatted(consoleDomain,
                releaseOrder.getReleaseVersion()));

        var namespace = namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId());
        taskCreateParam.setName("【%s】%s".formatted(namespace.getName(), releaseOrder.getDescription()));

        List<String> developers = namespace.getOwners().stream().map(UserUtil::formatWorkerIdWithoutZero)
                .collect(Collectors.toList());
        taskCreateParam.setDeveloperList(developers);

        List<String> testers = namespace.getTesters().stream().map(UserUtil::formatWorkerIdWithoutZero)
                .collect(Collectors.toList());
        taskCreateParam.setTesterList(testers);

        taskCreateParam.setPublishContent(generatePublishContent(releaseOrder, namespace.getName()));

        TaskCreateParam.OrangeActionEntity orangeActionEntity = new TaskCreateParam.OrangeActionEntity();
        orangeActionEntity.setNamespace(namespace.getName());
        if (NamespaceBizType.MODULE.equals(namespace.getBizType())) {
            orangeActionEntity.setModuleIdList(Arrays.asList(namespace.getBizId()));
        }
        taskCreateParam.setActionEntity(orangeActionEntity);
        taskCreateParam.setGrayCondition(generateGrayCondition(releaseOrder));
        taskCreateParam.setActionType(TigaActionType.SWITCH.getCode());

        Long taskId = tigaService.createTask(taskCreateParam);

        String tigaMetadata = JSON.toJSONString(OReleaseOrderDO.TigaMetadata.builder()
                .taskId(taskId)
                .templateId(templateId)
                .build());

        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, releaseOrder.getReleaseVersion())
                .set(OReleaseOrderDO::getTigaTaskId, taskId)
                .set(OReleaseOrderDO::getTigaMetadata, tigaMetadata)
                .update();

        return taskId;
    }

    private String generateGrayCondition(OReleaseOrderDO releaseOrderDO) {
        // 参数条件值变更涉及的条件
        List<String> conditionIds = parameterConditionVersionDAO.lambdaQuery()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseOrderDO.getReleaseVersion())
                .list()
                .stream()
                .map(OParameterConditionVersionDO::getConditionId)
                .distinct()
                .toList();

        // 条件自身变更以及参数条件变更涉及的条件
        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery()
                .eq(OConditionVersionDO::getReleaseVersion, releaseOrderDO.getReleaseVersion())
                .or(!conditionIds.isEmpty(), wrapper -> wrapper
                        .in(OConditionVersionDO::getConditionId, conditionIds)
                        .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED))
                .list();

        if (CollectionUtils.isEmpty(conditions)) {
            return null;
        }

        List<LogicExpression> expressions = conditions.stream()
                .map(c -> JSON.parseObject(c.getExpression(), ConditionExpressionBO.class))
                .map(expressionMapper::toLogicExpression)
                .toList();

        // 用 OR 逻辑进行拼接
        LogicalExpressionNode logicalExpressionNode = new LogicalExpressionNode(LogicalOperator.OR, expressions);

        return ExpressionParser.toStandardJson(logicalExpressionNode);
    }

    private String generatePublishContent(OReleaseOrderDO releaseOrder, String namespaceName) {
        String releaseVersion = releaseOrder.getReleaseVersion();
        Long namespaceChangeVersion = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .eq(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.NEW_IMPACT_RELEASE)
                .oneOpt()
                .map(n -> Long.parseLong(n.getNamespaceChangeVersion()))
                .orElse(null);

        GrayPublishContent publishContent = GrayPublishContent.builder()
                .version(Long.parseLong(releaseVersion))
                .changeVersion(namespaceChangeVersion)
                .name(namespaceName)
                .resourceId("BETA/RO_" + releaseVersion)
                .cdn(cdnDomain)
                .build();
        return JSON.toJSONString(publishContent);
    }

    public void doTask(OReleaseOrderDO releaseOrderDO, TaskCmd cmd, Long skipStageId) {
        tigaService.doTask(releaseOrderDO.getTigaTaskId(), cmd, skipStageId);
    }

    public TaskStageListDTO getTigaTaskStageList(OReleaseOrderDO releaseOrder) {
        if (releaseOrder == null || releaseOrder.getTigaTaskId() == null) {
            return null;
        }
        return tigaService.getTaskStageList(releaseOrder.getTigaTaskId());
    }

    public TemplateInstanceDTO getDefaultTemplate(String appKey) {
        return tigaService.getDefaultTemplateInstance(appKey, TigaActionType.SWITCH.getCode());
    }

    private String getCFApplyId(String releaseVersion) {
        var changeFreeResult = changefreeManager.getLatestPassedChangefreeResult(releaseVersion);
        if (changeFreeResult == null) {
            return null;
        }
        if (StringUtils.isNotBlank(changeFreeResult.getOrderDetailUrl())) {
            return changeFreeResult.getOrderDetailUrl();
        } else if (StringUtils.isNotBlank(changeFreeResult.getApplyOrderUrl())) {
            return changeFreeResult.getApplyOrderUrl();
        }
        throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
    }
}
