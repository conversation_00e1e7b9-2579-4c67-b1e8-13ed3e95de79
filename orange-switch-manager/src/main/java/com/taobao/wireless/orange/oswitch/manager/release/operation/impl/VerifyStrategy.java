package com.taobao.wireless.orange.oswitch.manager.release.operation.impl;

import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.common.PermissionManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import com.taobao.wireless.orange.oswitch.manager.release.model.VerifyCommandBO;
import com.taobao.wireless.orange.oswitch.manager.release.operation.AbstractOperationTemplate;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import com.taobao.wireless.orange.oswitch.manager.task.TaskManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 验证操作策略实现
 */
@Component
public class VerifyStrategy extends AbstractOperationTemplate<VerifyCommandBO, OperationResult> {

    @Autowired
    private TaskManager taskManager;

    @Autowired
    private PermissionManager permissionManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.VERIFY_REPLY;
    }

    @Override
    public void validatePermission(OperationContext<VerifyCommandBO, OperationResult> context) {
        if (permissionManager.isAdmin()) {
            return;
        }

        var namespace = context.getNamespace();
        String workerId = ThreadContextUtil.getWorkerId();

        if (StringUtils.isBlank(workerId) || !namespace.getTesters().contains(workerId)) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION, "仅测试负责人可以进行验证");
        }
    }

    @Override
    public void executeOperation(OperationContext<VerifyCommandBO, OperationResult> context) {
    }

    @Override
    public OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext<VerifyCommandBO, OperationResult> context) {
        VerifyCommandBO verifyCommandBO = context.getAdditionalData();
        return OReleaseOrderStageDO.builder()
                .type(StageType.VERIFY)
                .status(VerifyStatus.PASS.equals(verifyCommandBO.getVerifyStatus()) ? StageStatus.SUCCESS : StageStatus.FAILED)
                .build();
    }

    @Override
    public void handleTask(OperationContext<VerifyCommandBO, OperationResult> context) {
        taskManager.completeReleaseOrderTask(context.getReleaseVersion(), TaskType.VERIFY);

        VerifyCommandBO verifyCommandBO = context.getAdditionalData();
        if (VerifyStatus.PASS.equals(verifyCommandBO.getVerifyStatus())) {
            ONamespaceDO namespace = context.getNamespace();
            if (namespace == null || CollectionUtils.isEmpty(namespace.getOwners())) {
                return;
            }

            taskManager.createReleaseTask(context.getReleaseVersion(), namespace);
        }
    }
}