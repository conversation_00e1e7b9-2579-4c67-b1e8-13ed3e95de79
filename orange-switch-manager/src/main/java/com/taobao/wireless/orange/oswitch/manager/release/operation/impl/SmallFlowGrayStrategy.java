package com.taobao.wireless.orange.oswitch.manager.release.operation.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.StageStatus;
import com.taobao.wireless.orange.common.constant.enums.StageType;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.release.TigaGrayManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import com.taobao.wireless.orange.oswitch.manager.release.model.SmallFlowGrayCommandBO;
import com.taobao.wireless.orange.oswitch.manager.release.operation.AbstractOperationTemplate;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 灰度操作策略实现
 */
@Component
public class SmallFlowGrayStrategy extends AbstractOperationTemplate<SmallFlowGrayCommandBO, OperationResult> {

    @Autowired
    private TigaGrayManager tigaGrayManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.SMALLFLOW_GRAY;
    }

    @Override
    public boolean needCheckChangefree() {
        return true;
    }

    @Override
    public void executeOperation(OperationContext<SmallFlowGrayCommandBO, OperationResult> context) {
        OReleaseOrderDO releaseOrder = context.getReleaseOrder();
        SmallFlowGrayCommandBO smallFlowGrayCommandBO = context.getAdditionalData();

        // 如果是第一次，需要先创建任务
        if (releaseOrder.getTigaTaskId() == null) {
            Long taskId = tigaGrayManager.startTigaTask(releaseOrder, smallFlowGrayCommandBO.getTemplateId());
            releaseOrder.setTigaTaskId(taskId);
        }

        tigaGrayManager.doTask(releaseOrder, smallFlowGrayCommandBO.getTaskCmd(), smallFlowGrayCommandBO.getSkipStageId());
    }

    @Override
    public OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext<SmallFlowGrayCommandBO, OperationResult> context) {
        return OReleaseOrderStageDO.builder()
                .type(StageType.SMALLFLOW_GRAY)
                .status(StageStatus.IN_PROGRESS)
                .build();
    }
}