package com.taobao.wireless.orange.oswitch.manager.release.operation.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.StageStatus;
import com.taobao.wireless.orange.common.constant.enums.StageType;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationAdditionalData;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import com.taobao.wireless.orange.oswitch.manager.release.operation.AbstractOperationTemplate;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import com.taobao.wireless.orange.oswitch.manager.task.TaskManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 发起验证操作策略实现
 */
@Component
public class StartVerifyStrategy extends AbstractOperationTemplate<OperationAdditionalData, OperationResult> {

    @Autowired
    private TaskManager taskManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.START_VERIFY;
    }

    @Override
    public void executeOperation(OperationContext context) {
    }

    @Override
    public OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext context) {
        return OReleaseOrderStageDO.builder()
                .type(StageType.VERIFY)
                .status(StageStatus.IN_PROGRESS)
                .build();
    }

    @Override
    public void handleTask(OperationContext context) {
        ONamespaceDO namespace = context.getNamespace();
        String releaseVersion = context.getReleaseVersion();
        taskManager.createVerifyTask(releaseVersion, namespace);
    }
}