package com.taobao.wireless.orange.oswitch.manager.release;

import com.alibaba.fastjson2.JSON;
import com.alibaba.goc.changefree.model.ChangeQueryRes;
import com.alibaba.goc.changefree.model.CheckStatusEnum;
import com.alibaba.goc.changefree.model.OrderStatusEnum;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.external.changefree.ChangefreeService;
import com.taobao.wireless.orange.external.oss.OssService;
import com.taobao.wireless.orange.external.wmcc.WmccPublishService;
import com.taobao.wireless.orange.oswitch.dal.dao.*;
import com.taobao.wireless.orange.oswitch.dal.entity.*;
import com.taobao.wireless.orange.oswitch.manager.common.PageUtil;
import com.taobao.wireless.orange.oswitch.manager.common.TairManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.ConditionManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.NamespaceManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.ParameterManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionVersionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.NamespaceSnapshot;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterConditionVersionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterVersionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.version.NamespaceVersionManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.*;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationFactory;
import com.taobao.wireless.orange.oswitch.manager.release.operation.impl.*;
import com.taobao.wireless.orange.publish.config.BetaConfigGenerator;
import com.taobao.wireless.orange.publish.config.model.GrayConfig;
import com.taobao.wireless.orange.publish.config.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.publish.resource.ResourceService;
import com.taobao.wireless.tiga.release.console.api.task.model.dto.TaskStageListDTO;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateInstanceDTO;
import com.taobao.wmcc.client.publish.PublishTaskInfo;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType.FINISH_IMPACT_RELEASE;

@Slf4j
@Service
public class ReleaseOrderManager {
    @Autowired
    private ParameterManager parameterManager;

    @Autowired
    private BetaConfigGenerator betaConfigGenerator;

    @Autowired
    private TairManager tairManager;

    @Autowired
    private NamespaceManager namespaceManager;

    @Autowired
    private ConditionManager conditionManager;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    private OperationFactory operationFactory;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private WmccPublishService wmccPublishService;

    @Autowired
    private OIndexDAO indexDAO;

    @Autowired
    private OProbeDAO probeDAO;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private TigaGrayManager tigaGrayManager;

    @Autowired
    private OssService ossService;

    @Autowired
    private ChangefreeService changefreeService;

    @Autowired
    @Qualifier("textResourceService")
    private ResourceService resourceService;

    @Autowired
    private Client slsClient;

    @Autowired
    private OReleaseOrderStageDAO releaseOrderStageDAO;

    /**
     * 查询发布单列表
     *
     * @param query
     * @param pagination
     * @return
     */
    public Page<OReleaseOrderDO> query(ReleaseOrderBO query, Pagination pagination) {
        return releaseOrderDAO.lambdaQuery()
                .eq(query.getAppKey() != null, OReleaseOrderDO::getAppKey, query.getAppKey())
                .eq(query.getNamespaceId() != null, OReleaseOrderDO::getNamespaceId, query.getNamespaceId())
                .eq(query.getReleaseVersion() != null, OReleaseOrderDO::getReleaseVersion, query.getReleaseVersion())
                .in(CollectionUtils.isNotEmpty(query.getStatuses()), OReleaseOrderDO::getStatus, query.getStatuses())
                .orderByDesc(OReleaseOrderDO::getId)
                .page(PageUtil.build(pagination));
    }

    /**
     * 获取发布单操作记录
     *
     * @param releaseVersion 发布版本
     * @return 操作记录列表
     */
    public List<OReleaseOrderOperationDO> getOperations(String releaseVersion, @Nullable List<OperationType> operationTypes) {
        return releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .in(CollectionUtils.isNotEmpty(operationTypes), OReleaseOrderOperationDO::getType, operationTypes)
                .orderByDesc(OReleaseOrderOperationDO::getId)
                .list();
    }

    /**
     * 获取发布单详情
     *
     * @param releaseVersion 发布版本号
     * @return 发布单详情
     */
    public ReleaseOrderBO getDetail(String releaseVersion) {
        OReleaseOrderDO releaseOrderDO = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        if (releaseOrderDO == null) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST);
        }
        ReleaseOrderBO releaseOrder = BeanUtil.createFromProperties(releaseOrderDO, ReleaseOrderBO.class);

        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .list();
        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        // 填充参数和条件信息
        releaseOrder.setParameterVersions(BeanUtil.createListFromProperties(parameters, ParameterVersionBO.class));
        releaseOrder.setConditionVersions(BeanUtil.createListFromProperties(conditions, ConditionVersionBO.class));

        // 填充命名空间名
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(releaseOrder.getNamespaceId());
        releaseOrder.setNamespaceName(namespace.getName());

        // 填充发布阶段详情
        List<OReleaseOrderStageDO> stages = releaseOrderStageDAO.lambdaQuery()
                .eq(OReleaseOrderStageDO::getReleaseVersion, releaseVersion)
                .list()
                .stream()
                // 按照顺序排序返回
                .sorted(Comparator.comparing(OReleaseOrderStageDO::getStageOrder))
                .collect(Collectors.toList());
        releaseOrder.setReleaseOrderStages(stages);

        // 填充 Agateware 任务信息
        fillAgatewareTaskInfo(releaseOrder);

        return releaseOrder;
    }

    /**
     * 新建发布
     *
     * @param releaseOrderBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String create(ReleaseOrderBO releaseOrderBO) {
        // 基础入参校验
        checkReleaseOrderParams(releaseOrderBO);

        ONamespaceDO namespace = namespaceManager.getByNamespaceId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setAppKey(namespace.getAppKey());

        // 获取 namespace 锁
        if (!tairManager.lockNamespace(releaseOrderBO.getNamespaceId())) {
            throw new CommonException(ExceptionEnum.NAMESPACE_LOCKED);
        }

        try {
            // 检测涉及发布的参数和条件实体是否有在变更中
            checkReleaseObjectIsPublishing(releaseOrderBO);

            // 生成发布单
            String releaseVersion = createReleaseOrder(releaseOrderBO);

            // 新增条件版本记录
            conditionManager.createConditionVersions(namespace, releaseVersion, releaseOrderBO.getConditionVersions());

            // 新增参数版本记录
            parameterManager.createParameterVersions(namespace, releaseVersion, releaseOrderBO.getParameterVersions());

            // 新增参数条件版本记录
            parameterManager.createParameterConditionVersions(namespace, releaseVersion, releaseOrderBO.getParameterVersions(), releaseOrderBO.getConditionVersions());

            // 如果对线上有影响，则新增命名空间版本记录
            if (releaseOrderBO.getReleaseType().isImpact()) {
                namespaceVersionManager.createNamespaceVersion(releaseOrderBO, NamespaceVersionChangeType.NEW_IMPACT_RELEASE);
            }

            // 为该发布单生成 Beta 配置文件，用于快速扫码 BETA 使用
            generateBetaConfig(namespace.getNamespaceId(), releaseVersion);

            return releaseVersion;
        } finally {
            tairManager.unlockNamespace(releaseOrderBO.getNamespaceId());
        }
    }

    /**
     * 获取发布单发布内容调试信息
     *
     * @param releaseVersion 发布版本
     * @return 发布单发布内容调试信息
     */
    public DebugInfoBO getDebugInfo(String releaseVersion) {
        DebugInfoBO debugInfo = new DebugInfoBO();

        var releaseOrder = releaseOrderDAO.getByReleaseVersion(releaseVersion);

        // 获取当前应用最新的索引版本
        indexDAO.lambdaQuery().eq(OIndexDO::getAppKey, releaseOrder.getAppKey())
                .eq(OIndexDO::getIsAvailable, Available.y)
                .eq(OIndexDO::getBaseIndexVersion, "0")
                .oneOpt()
                .ifPresent(index -> debugInfo.setIndexVersion(index.getIndexVersion()));

        // 获取当前发布单新建时对应的 changeVersion
        namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .eq(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.NEW_IMPACT_RELEASE)
                .oneOpt()
                .ifPresent(namespaceVersion -> {
                    debugInfo.setChangeVersion(namespaceVersion.getNamespaceChangeVersion());
                });

        var namespace = namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId());
        debugInfo.setNamespaceName(namespace.getName());

        return debugInfo;
    }

    public void applyRelease(String releaseVersion, ApplyReleaseCommandBO applyRelease) {
        ApplyReleaseStrategy strategy = (ApplyReleaseStrategy) operationFactory.getStrategy(OperationType.APPLY_RELEASE);

        strategy.execute(new OperationContext<>(releaseVersion, applyRelease));
    }

    public void publish(String releaseVersion) {
        ReleaseStrategy strategy = (ReleaseStrategy) operationFactory.getStrategy(OperationType.RELEASE);

        strategy.execute(new OperationContext<>(releaseVersion));
    }

    public void cancel(String releaseVersion) {
        CancelStrategy strategy = (CancelStrategy) operationFactory.getStrategy(OperationType.CANCEL);

        strategy.execute(new OperationContext<>(releaseVersion));
    }

    public void skip(String releaseVersion, SkipCommandBO skipCommandBO) {
        SkipStrategy strategy = (SkipStrategy) operationFactory.getStrategy(OperationType.SKIP);

        strategy.execute(new OperationContext<>(releaseVersion, skipCommandBO));
    }

    public void verifyReply(String releaseVersion, VerifyCommandBO verifyReply) {
        VerifyStrategy strategy = (VerifyStrategy) operationFactory.getStrategy(OperationType.VERIFY_REPLY);

        strategy.execute(new OperationContext<>(releaseVersion, verifyReply));
    }

    public void startVerify(String releaseVersion) {
        StartVerifyStrategy strategy = (StartVerifyStrategy) operationFactory.getStrategy(OperationType.START_VERIFY);

        strategy.execute(new OperationContext<>(releaseVersion));
    }

    public void ratioGray(String releaseVersion, RatioGrayCommandBO ratioGrayCommandBO) {
        RatioGrayStrategy strategy = (RatioGrayStrategy) operationFactory.getStrategy(OperationType.RATIO_GRAY);

        strategy.execute(new OperationContext<>(releaseVersion, ratioGrayCommandBO));
    }

    public void tigaGray(String releaseVersion, SmallFlowGrayCommandBO smallFlowGrayCommandBO) {
        SmallFlowGrayStrategy strategy = (SmallFlowGrayStrategy) operationFactory.getStrategy(OperationType.SMALLFLOW_GRAY);

        strategy.execute(new OperationContext<>(releaseVersion, smallFlowGrayCommandBO));
    }

    public List<Map<String, String>> getBetaScanLogs(ScanBetaLogQueryBO query, Pagination pagination) {
        String releaseVersion = query.getReleaseVersion();
        OReleaseOrderDO releaseOrder = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        try {
            String sql = "version: %s and eventType: %s not utdid: -".formatted(releaseVersion, query.getType());
            int from = (int) (releaseOrder.getGmtCreate().getTime() / 1000);
            int to = (int) (new Date().getTime() / 1000);
            int offset = (pagination.getPageNum() - 1) * pagination.getPageSize();

            GetLogsResponse logsResponse = slsClient.GetLogs("orange", "client-beta", from, to, "switch", sql, pagination.getPageSize(), offset, true);

            if (!logsResponse.IsCompleted()) {
                return Collections.emptyList();
            }

            return logsResponse.getLogs().stream().map(log -> {
                LogItem item = log.GetLogItem();
                return item.mContents.stream().collect(Collectors.toMap(LogContent::getKey, LogContent::getValue));
            }).toList();
        } catch (Throwable e) {
            throw new CommonException(ExceptionEnum.SLS_QUERY_ERROR);
        }
    }

    /**
     * 刷新申请发布阶段状态
     *
     * @param releaseVersion 发布版本号
     */
    public void refreshApplyStageStatus(String releaseVersion) {
        List<OReleaseOrderOperationDO> operations = getApplyReleaseOperations(releaseVersion);
        if (CollectionUtils.isEmpty(operations)) {
            return;
        }

        // 更新操作结果并收集状态信息
        ApplyStageStatusSummary statusSummary = updateOperationResults(operations);

        // 更新阶段状态
        updateApplyStageStatus(releaseVersion, statusSummary);

        // 更新紧急发布标记
        updateEmergentReleaseFlag(releaseVersion, statusSummary);
    }

    /**
     * 获取申请发布操作记录
     */
    private List<OReleaseOrderOperationDO> getApplyReleaseOperations(String releaseVersion) {
        return releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .eq(OReleaseOrderOperationDO::getStatus, OperationStatus.SUCCESS)
                .eq(OReleaseOrderOperationDO::getType, OperationType.APPLY_RELEASE)
                .list();
    }

    /**
     * 更新操作结果并收集状态摘要
     */
    private ApplyStageStatusSummary updateOperationResults(List<OReleaseOrderOperationDO> operations) {
        boolean hasSuccess = false;
        boolean hasEmergentSuccess = false;
        boolean hasInProgress = false;

        for (OReleaseOrderOperationDO operation : operations) {
            ChangeFreeResult result = JSON.parseObject(operation.getResult(), ChangeFreeResult.class);
            ChangeQueryRes latestStatus = changefreeService.query(result.getSourceOrderId());

            // 更新操作结果（如果有变化）
            if (updateOperationResultIfChanged(operation, result, latestStatus)) {
                releaseOrderOperationDAO.updateById(operation);
            }

            // 收集状态信息
            if (isSuccessStatus(result)) {
                hasSuccess = true;

                // 检查是否为紧急发布
                if (isEmergentRelease(operation)) {
                    hasEmergentSuccess = true;
                }
            } else if (isInProgressStatus(result)) {
                hasInProgress = true;
            }
        }

        return new ApplyStageStatusSummary(hasSuccess, hasEmergentSuccess, hasInProgress);
    }

    /**
     * 检查并更新操作结果
     *
     * @return true 如果有更新，false 如果无变化
     */
    private boolean updateOperationResultIfChanged(OReleaseOrderOperationDO operation,
                                                   ChangeFreeResult result,
                                                   ChangeQueryRes latestStatus) {
        boolean hasChanged = false;

        if (hasStatusChanged(result.getCheckStatusEnum(), latestStatus.getCheckStatusEnum())) {
            result.setCheckStatusEnum(latestStatus.getCheckStatusEnum());
            hasChanged = true;
        }

        if (hasStatusChanged(result.getOrderStatusEnum(), latestStatus.getOrderStatusEnum())) {
            result.setOrderStatusEnum(latestStatus.getOrderStatusEnum());
            hasChanged = true;
        }

        if (hasUrlChanged(result.getOrderDetailUrl(), latestStatus.getOrderDetailUrl())) {
            result.setOrderDetailUrl(latestStatus.getOrderDetailUrl());
            hasChanged = true;
        }

        if (hasChanged) {
            operation.setResult(JSON.toJSONString(result));
        }

        return hasChanged;
    }

    /**
     * 检查状态是否发生变化
     */
    private boolean hasStatusChanged(Object currentStatus, Object newStatus) {
        if (currentStatus == null) {
            return newStatus != null;
        }
        return !currentStatus.equals(newStatus);
    }

    /**
     * 检查URL是否发生变化
     */
    private boolean hasUrlChanged(String currentUrl, String newUrl) {
        if (StringUtils.isBlank(currentUrl)) {
            return StringUtils.isNotBlank(newUrl);
        }
        return !currentUrl.equals(newUrl);
    }

    /**
     * 判断是否为成功状态
     */
    private boolean isSuccessStatus(ChangeFreeResult result) {
        return CheckStatusEnum.CHECK_PASS.equals(result.getCheckStatusEnum()) ||
                OrderStatusEnum.ORDER_PASS.equals(result.getOrderStatusEnum());
    }

    /**
     * 判断是否为进行中状态
     */
    private boolean isInProgressStatus(ChangeFreeResult result) {
        return CheckStatusEnum.CHECK_HOLD.equals(result.getCheckStatusEnum()) ||
                CheckStatusEnum.CHECK_WAIT.equals(result.getCheckStatusEnum()) ||
                CheckStatusEnum.CHECK_UNKNOWN.equals(result.getCheckStatusEnum()) ||
                OrderStatusEnum.ORDER_APPROVING.equals(result.getOrderStatusEnum());
    }

    /**
     * 判断是否为紧急发布
     */
    private boolean isEmergentRelease(OReleaseOrderOperationDO operation) {
        ApplyReleaseCommandBO applyReleaseCommand = JSON.parseObject(operation.getParams(), ApplyReleaseCommandBO.class);
        return ReleaseLevel.EMERGENT.equals(applyReleaseCommand.getReleaseLevel());
    }

    /**
     * 更新申请发布阶段状态
     */
    private void updateApplyStageStatus(String releaseVersion, ApplyStageStatusSummary statusSummary) {
        OReleaseOrderStageDO stage = releaseOrderStageDAO.getByReleaseVersionAndType(releaseVersion, StageType.APPLY_RELEASE);

        if (stage != null && !stage.getStatus().isCompleted()) {
            StageStatus stageStatus = determineStageStatus(statusSummary);
            releaseOrderStageDAO.updateById(OReleaseOrderStageDO.builder()
                    .id(stage.getId())
                    .status(stageStatus)
                    .build());
        }
    }

    /**
     * 根据状态摘要确定阶段状态
     */
    private StageStatus determineStageStatus(ApplyStageStatusSummary statusSummary) {
        if (statusSummary.hasSuccess()) {
            return StageStatus.SUCCESS;
        } else if (statusSummary.hasInProgress()) {
            return StageStatus.IN_PROGRESS;
        } else {
            return StageStatus.FAILED;
        }
    }

    /**
     * 更新紧急发布标记
     */
    private void updateEmergentReleaseFlag(String releaseVersion, ApplyStageStatusSummary statusSummary) {
        if (statusSummary.hasEmergentSuccess()) {
            releaseOrderDAO.lambdaUpdate()
                    .eq(OReleaseOrderDO::getReleaseVersion, releaseVersion)
                    .eq(OReleaseOrderDO::getIsEmergent, Emergent.n)
                    .set(OReleaseOrderDO::getIsEmergent, Emergent.y)
                    .update();
        }
    }

    /**
     * 申请发布阶段状态摘要
     */
    private static class ApplyStageStatusSummary {
        private final boolean hasSuccess;
        private final boolean hasEmergentSuccess;
        private final boolean hasInProgress;

        public ApplyStageStatusSummary(boolean hasSuccess, boolean hasEmergentSuccess, boolean hasInProgress) {
            this.hasSuccess = hasSuccess;
            this.hasEmergentSuccess = hasEmergentSuccess;
            this.hasInProgress = hasInProgress;
        }

        public boolean hasSuccess() {
            return hasSuccess;
        }

        public boolean hasEmergentSuccess() {
            return hasEmergentSuccess;
        }

        public boolean hasInProgress() {
            return hasInProgress;
        }
    }

    /**
     * 查询发布单变更内容
     *
     * @param releaseVersion 发布版本号
     * @return
     */
    public ReleaseOrderChangeBO getChanges(String releaseVersion) {
        ReleaseOrderChangeBO releaseOrderChange = new ReleaseOrderChangeBO();
        String namespaceId = releaseOrderDAO.getByReleaseVersion(releaseVersion).getNamespaceId();
        releaseOrderChange.setNamespaceId(namespaceId);

        Map<String, List<OParameterConditionVersionDO>> parameterConditionsMap = parameterConditionVersionDAO.lambdaQuery()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion).list().stream()
                .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId));
        List<OParameterVersionDO> parameterVersions = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion).list();
        List<ParameterVersionBO> parameterChanges = BeanUtil.createListFromProperties(parameterVersions, ParameterVersionBO.class);
        parameterChanges.forEach(p -> {
            p.setParameterConditionVersions(BeanUtil.createListFromProperties(parameterConditionsMap.get(p.getParameterId()), ParameterConditionVersionBO.class));
        });
        releaseOrderChange.setParameterChanges(parameterChanges);

        List<OConditionVersionDO> conditionVersions = conditionVersionDAO.lambdaQuery()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();
        List<ConditionVersionBO> conditionChanges = BeanUtil.createListFromProperties(conditionVersions, ConditionVersionBO.class);
        List<String> conditionIds = conditionVersions.stream().map(OConditionVersionDO::getConditionId).collect(Collectors.toList());
        Map<String, OConditionDO> conditionMap = conditionDAO.getConditionMap(conditionIds);
        conditionChanges.forEach(c -> c.setCondition(conditionMap.get(c.getConditionId())));
        releaseOrderChange.setConditionChanges(conditionChanges);

        fillPreviousParametersAndConditions(releaseOrderChange, namespaceId, releaseVersion);

        return releaseOrderChange;
    }

    /**
     * 获取改动前的参数版本
     *
     * @param releaseVersion 发布版本
     */
    private void fillPreviousParametersAndConditions(ReleaseOrderChangeBO releaseOrderChange, String namespaceId, String releaseVersion) {
        ONamespaceVersionDO namespaceVersionDO = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .in(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.getFinishReleaseTypes())
                .oneOpt()
                .orElse(null);

        String namespaceVersion = namespaceVersionDO != null ? namespaceVersionDO.getNamespaceVersion() : null;

        ONamespaceVersionDO previousNamespaceVersion = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .in(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.getFinishReleaseTypes())
                .lt(namespaceVersion != null, ONamespaceVersionDO::getNamespaceVersion, namespaceVersion)
                .orderByDesc(ONamespaceVersionDO::getNamespaceVersion)
                .last("limit 1")
                .oneOpt()
                .orElse(null);

        if (previousNamespaceVersion == null) {
            return;
        }

        byte[] bytes = ossService.readData(String.format("SNAPSHOT/NS_%s.json", previousNamespaceVersion.getNamespaceVersion()));

        NamespaceSnapshot namespaceSnapshot = JSON.parseObject(new String(bytes), NamespaceSnapshot.class);
        releaseOrderChange.setPreviousNamespace(namespaceSnapshot);
    }

    /**
     * 基础入参校验
     *
     * @param releaseOrderBO 发布单入参
     */
    private void checkReleaseOrderParams(ReleaseOrderBO releaseOrderBO) {
        // 校验参数版本
        releaseOrderBO.getParameterVersions().forEach(parameterVersionBO -> {
            validateVersionBO(parameterVersionBO.getChangeType(),
                    parameterVersionBO.getParameterId(),
                    parameterVersionBO.getParameterKey(),
                    parameterVersionBO.getPreviousReleaseVersion(),
                    "参数");
        });

        // 校验条件版本
        releaseOrderBO.getConditionVersions().forEach(conditionVersionBO -> {
            validateVersionBO(conditionVersionBO.getChangeType(),
                    conditionVersionBO.getConditionId(),
                    conditionVersionBO.getCondition().getName(),
                    conditionVersionBO.getPreviousReleaseVersion(),
                    "条件");
        });

        // 校验参数条件是否重复
        parameterManager.checkParameterConditionDuplicate(releaseOrderBO.getParameterVersions());

        // 默认值合法性校验
        parameterManager.checkParameterDefaultCondition(releaseOrderBO.getParameterVersions());

        // 参数和条件变更不能同时为空
        if (CollectionUtils.isEmpty(releaseOrderBO.getParameterVersions()) && CollectionUtils.isEmpty(releaseOrderBO.getConditionVersions())) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "参数和条件变更不能同时为空");
        }
    }


    /**
     * 填充 WMCC 信息
     *
     * @param releaseOrder
     */
    private void fillAgatewareTaskInfo(ReleaseOrderBO releaseOrder) {
        if (!ReleaseOrderStatus.RELEASED.equals(releaseOrder.getStatus())) {
            return;
        }

        // 找到本次发布关联的索引
        String indexVersion = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseOrder.getReleaseVersion())
                .eq(ONamespaceVersionDO::getChangeType, FINISH_IMPACT_RELEASE)
                .oneOpt()
                .map(ONamespaceVersionDO::getIndexVersion)
                .orElse(null);

        if (StringUtils.isBlank(indexVersion)) {
            return;
        }

        // 查找索引对应的探针任务
        String agatewareTaskId = probeDAO.lambdaQuery()
                .eq(OProbeDO::getAppKey, releaseOrder.getAppKey())
                .eq(OProbeDO::getIndexVersion, indexVersion)
                .oneOpt()
                .map(OProbeDO::getAgatewareTaskId)
                .orElse(null);

        if (StringUtils.isBlank(agatewareTaskId)) {
            return;
        }

        // 填充探针任务 ID 及任务详情
        releaseOrder.setAgatewareTaskId(agatewareTaskId);
        var wmccPublishTask = wmccPublishService.getPublishTaskStatus(Long.parseLong(agatewareTaskId));
        if (wmccPublishTask != null) {
            releaseOrder.setAgatewareTaskInfo(wmccPublishTask);
        }
    }

    private String createReleaseOrder(ReleaseOrderBO releaseOrderBO) {
        String releaseVersion = String.valueOf(SerializeUtil.version());
        releaseOrderBO.setReleaseVersion(releaseVersion);
        releaseOrderBO.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrderBO.setBizId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setStatus(ReleaseOrderStatus.IN_PROGRESS);
        releaseOrderBO.setIsEmergent(Emergent.n);
        releaseOrderBO.setReleaseType(releaseOrderBO.getReleaseType());
        // 新增发布单
        releaseOrderDAO.save(releaseOrderBO);

        // 新增发布单阶段
        this.createReleaseOrderStages(releaseOrderBO);

        return releaseVersion;
    }

    private void createReleaseOrderStages(ReleaseOrderBO releaseOrder) {
        List<OReleaseOrderStageDO> stages = Arrays.stream(StageType.values()).map(stage -> {
            OReleaseOrderStageDO stageDO = new OReleaseOrderStageDO();
            stageDO.setAppKey(releaseOrder.getAppKey());
            stageDO.setNamespaceId(releaseOrder.getNamespaceId());
            stageDO.setReleaseVersion(releaseOrder.getReleaseVersion());
            stageDO.setType(stage);
            stageDO.setStageOrder(stage.getStageOrder());
            stageDO.setStatus(StageStatus.INIT);
            return stageDO;
        }).toList();

        releaseOrderStageDAO.saveBatch(stages);
    }

    /**
     * 查询发布单分批发布进展（批量去重查询，减少外部调用）
     */
    public RatioGrayProgressBO getRatioGrayProgress(String releaseVersion) {
        OReleaseOrderDO releaseOrder = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        if (releaseOrder == null) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST);
        }

        // 1) 查询操作记录（灰度 + 正式发布）
        GrayAndReleaseOperations operations = fetchGrayAndReleaseOperations(releaseVersion);

        // 2) 查询 namespace_version（灰度 + 完成发布）
        GrayAndReleaseNamespaceVersions namespaceVersions = fetchGrayAndReleaseNamespaceVersions(releaseVersion);

        // 3) 查询 probe 映射
        Map<String, OProbeDO> indexVersion2Probe = loadProbeMap(releaseOrder.getAppKey(), namespaceVersions.allNamespaceVersions());

        // 4) 查询 WMCC 任务状态
        Map<String, PublishTaskInfo> taskId2Info = loadTaskInfoMap(indexVersion2Probe);

        // 5) 组装结果
        RatioGrayProgressBO dto = new RatioGrayProgressBO();

        List<RatioGrayProgressBO.RatioGrayProgressNode> ratioNodes = buildGrayNodes(
                operations.grayOperations(),
                namespaceVersions.grayNamespaceVersions(),
                taskId2Info,
                indexVersion2Probe
        );
        if (CollectionUtils.isNotEmpty(ratioNodes)) {
            dto.setRatioGrayProgressNodes(ratioNodes);
        }

        RatioGrayProgressBO.ReleaseProgressNode releaseNode = assembleReleaseProgressNode(
                operations.releaseOperation(),
                namespaceVersions.releaseNamespaceVersion(),
                taskId2Info,
                indexVersion2Probe
        );
        if (releaseNode != null) {
            dto.setReleaseProgressNode(releaseNode);
        }

        return dto;
    }

    private Integer parseGrayRatio(String params) {
        try {
            var cmd = JSON.parseObject(params, RatioGrayCommandBO.class);
            if (cmd != null) {
                return cmd.getGrayRatio();
            }
        } catch (Throwable ignore) {
            log.error("RatioGrayCommandBO parse error, params={}", params);
        }
        return null;
    }

    private RatioGrayProgressBO.RatioGrayProgressNode assembleReleaseProgressNode(OReleaseOrderOperationDO op, ONamespaceVersionDO ns, Map<String, PublishTaskInfo> taskId2Info, Map<String, OProbeDO> indexVersion2Probe) {
        if (op == null || ns == null) {
            return null;
        }
        RatioGrayProgressBO.RatioGrayProgressNode releaseNode = new RatioGrayProgressBO.RatioGrayProgressNode();
        releaseNode.setStartTime(op.getGmtCreate());

        // 当探针还未生成时，indexVersion 为空
        if (StringUtils.isBlank(ns.getIndexVersion())) {
            return releaseNode;
        }

        var probe = indexVersion2Probe.get(ns.getIndexVersion());
        if (probe != null) {
            releaseNode.setScheduleTime(probe.getGmtCreate());
            if (StringUtils.isNotBlank(probe.getAgatewareTaskId())) {
                releaseNode.setAgatewareTaskId(probe.getAgatewareTaskId());
                releaseNode.setAgatewareTaskInfo(taskId2Info.get(probe.getAgatewareTaskId()));
            }
        }
        return releaseNode;
    }

    /**
     * 批量查询灰度/正式发布的操作记录
     */
    private GrayAndReleaseOperations fetchGrayAndReleaseOperations(String releaseVersion) {
        List<OReleaseOrderOperationDO> operations = releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .in(OReleaseOrderOperationDO::getType, OperationType.RATIO_GRAY, OperationType.RELEASE)
                .orderByAsc(OReleaseOrderOperationDO::getId)
                .list();

        List<OReleaseOrderOperationDO> grayOps = operations.stream()
                .filter(op -> OperationType.RATIO_GRAY.equals(op.getType()))
                .toList();
        OReleaseOrderOperationDO releaseOp = operations.stream()
                .filter(op -> OperationType.RELEASE.equals(op.getType()))
                .findFirst()
                .orElse(null);

        return new GrayAndReleaseOperations(grayOps, releaseOp);
    }

    /**
     * 批量查询灰度/完成发布的 namespace 版本
     */
    private GrayAndReleaseNamespaceVersions fetchGrayAndReleaseNamespaceVersions(String releaseVersion) {
        List<ONamespaceVersionDO> nsVersions = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .in(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.RATIO_GRAY,
                        NamespaceVersionChangeType.FINISH_IMPACT_RELEASE, NamespaceVersionChangeType.FINISH_NO_IMPACT_RELEASE)
                .orderByAsc(ONamespaceVersionDO::getId)
                .list();

        List<ONamespaceVersionDO> grayNsList = nsVersions.stream()
                .filter(v -> NamespaceVersionChangeType.RATIO_GRAY.equals(v.getChangeType()))
                .toList();
        ONamespaceVersionDO releaseNs = nsVersions.stream()
                .filter(v -> NamespaceVersionChangeType.FINISH_IMPACT_RELEASE.equals(v.getChangeType())
                        || NamespaceVersionChangeType.FINISH_NO_IMPACT_RELEASE.equals(v.getChangeType()))
                .findFirst()
                .orElse(null);

        return new GrayAndReleaseNamespaceVersions(grayNsList, releaseNs, nsVersions);
    }

    /**
     * 根据 namespace 版本加载探针映射
     */
    private Map<String, OProbeDO> loadProbeMap(String appKey, Collection<ONamespaceVersionDO> nsVersions) {
        Set<String> indexVersions = nsVersions.stream()
                .map(ONamespaceVersionDO::getIndexVersion)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(indexVersions)) {
            return Collections.emptyMap();
        }

        return probeDAO.lambdaQuery()
                .eq(OProbeDO::getAppKey, appKey)
                .in(OProbeDO::getIndexVersion, indexVersions)
                .list()
                .stream()
                .collect(Collectors.toMap(OProbeDO::getIndexVersion, Function.identity(), (a, b) -> b));
    }

    /**
     * 批量查询 WMCC 任务状态（taskId 去重）
     */
    private Map<String, PublishTaskInfo> loadTaskInfoMap(Map<String, OProbeDO> indexVersion2Probe) {
        return indexVersion2Probe.values().stream()
                .map(OProbeDO::getAgatewareTaskId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .map(taskId -> {
                    try {
                        var status = wmccPublishService.getPublishTaskInfo(Long.parseLong(taskId));
                        return status != null ? Map.entry(taskId, status) : null;
                    } catch (Throwable e) {
                        log.error("wmccPublishService.getPublishTaskStatus error, taskId={}", taskId, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 组装灰度发布节点，保证长度不一致时安全
     */
    private List<RatioGrayProgressBO.RatioGrayProgressNode> buildGrayNodes(List<OReleaseOrderOperationDO> grayOps,
                                                                           List<ONamespaceVersionDO> grayNsList,
                                                                           Map<String, PublishTaskInfo> taskId2Info,
                                                                           Map<String, OProbeDO> indexVersion2Probe) {
        if (CollectionUtils.isEmpty(grayOps) || CollectionUtils.isEmpty(grayNsList)) {
            return Collections.emptyList();
        }

        if (grayOps.size() != grayNsList.size()) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_GRAY_AND_NAMESPACE_VERSION_NOT_MATCH);
        }

        int count = grayOps.size();

        List<RatioGrayProgressBO.RatioGrayProgressNode> nodes = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            OReleaseOrderOperationDO op = grayOps.get(i);
            RatioGrayProgressBO.RatioGrayProgressNode node = assembleReleaseProgressNode(op, grayNsList.get(i), taskId2Info, indexVersion2Probe);
            node.setGrayRatio(parseGrayRatio(op.getParams()));
            nodes.add(node);
        }
        return nodes;
    }

    /**
     * 灰度/正式发布操作记录封装
     */
    private record GrayAndReleaseOperations(List<OReleaseOrderOperationDO> grayOperations,
                                            OReleaseOrderOperationDO releaseOperation) {
    }

    /**
     * 灰度/完成发布 namespace 版本封装
     */
    private record GrayAndReleaseNamespaceVersions(List<ONamespaceVersionDO> grayNamespaceVersions,
                                                   ONamespaceVersionDO releaseNamespaceVersion,
                                                   List<ONamespaceVersionDO> allNamespaceVersions) {
    }

    /**
     * 检测发布实体是否在发布中
     *
     * @param releaseOrder 创建发布单入参
     */
    private void checkReleaseObjectIsPublishing(ReleaseOrderBO releaseOrder) {
        // 检查参数是否在发布中
        if (CollectionUtils.isNotEmpty(releaseOrder.getParameterVersions())) {
            List<String> parameterIds = releaseOrder.getParameterVersions().stream()
                    .map(ParameterVersionBO::getParameterId)
                    .collect(Collectors.toList());

            if (parameterVersionDAO.lambdaQuery()
                    .in(OParameterVersionDO::getParameterId, parameterIds)
                    .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                    .exists()) {
                throw new CommonException(ExceptionEnum.PARAMETER_IS_PUBLISHING);
            }
        }

        // 检查条件是否在发布中
        if (CollectionUtils.isNotEmpty(releaseOrder.getConditionVersions())) {
            List<String> conditionIds = releaseOrder.getConditionVersions().stream()
                    .map(ConditionVersionBO::getConditionId)
                    .collect(Collectors.toList());

            if (conditionVersionDAO.lambdaQuery()
                    .in(OConditionVersionDO::getConditionId, conditionIds)
                    .eq(OConditionVersionDO::getStatus, VersionStatus.INIT)
                    .exists()) {
                throw new CommonException(ExceptionEnum.CONDITION_IS_PUBLISHING);
            }
        }
    }


    // changeType 为 CREATE 时，ID 必须为空，key/name 必填
    // changeType 为 UPDATE/DELETE 时，ID 必须不为空，key/name 可以为空
    private void validateVersionBO(ChangeType changeType,
                                   String id,
                                   String key,
                                   String previousReleaseVersion,
                                   String type
    ) {
        if (ChangeType.CREATE.equals(changeType)) {
            if (StringUtils.isBlank(key) || StringUtils.isNotBlank(id)) {
                throw new CommonException(ExceptionEnum.PARAM_INVALID, "新增" + type + "必须指定名称");
            }
            if (StringUtils.isNotBlank(previousReleaseVersion)) {
                throw new CommonException(ExceptionEnum.PARAM_INVALID, "新增" + type + "不能指定上一个版本");
            }
        } else {
            if (StringUtils.isBlank(id)) {
                throw new CommonException(ExceptionEnum.PARAM_INVALID, "修改" + type + "必须指定 ID");
            }
            if (StringUtils.isBlank(previousReleaseVersion)) {
                throw new CommonException(ExceptionEnum.PARAM_INVALID, "修改" + type + "必须指定上一个版本");
            }
        }
    }

    public TaskStageListDTO getTigaTaskStageList(String releaseVersion) {
        var releaseOrder = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        return tigaGrayManager.getTigaTaskStageList(releaseOrder);
    }

    public List<TemplateInstanceDTO> getGrayTemplates(String releaseVersion) {
        var releaseOrder = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        if (releaseOrder == null) {
            return List.of();
        }

        // todo: 目前均返回默认模板
        TemplateInstanceDTO template = tigaGrayManager.getDefaultTemplate(releaseOrder.getAppKey());
        if (template == null) {
            return List.of();
        }
        return List.of(template);
    }

    /**
     * 生成指定 releaseVersion 发布单的 Beta配置
     *
     * @param namespaceId
     * @param releaseVersion
     */
    private void generateBetaConfig(String namespaceId, String releaseVersion) {
        NamespaceIdNameRecord namespace = getNamespaceRecord(namespaceId);
        GrayConfig config = betaConfigGenerator.generate(namespace, releaseVersion);
        if (config == null) {
            return;
        }
        resourceService.create("BETA/RO_" + releaseVersion, config, ResourceType.BETA_CONFIG);
    }

    private NamespaceIdNameRecord getNamespaceRecord(String namespaceId) {
        return Optional.ofNullable(namespaceDAO.getByNamespaceId(namespaceId))
                .map(n -> new NamespaceIdNameRecord(n.getNamespaceId(), n.getName()))
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST));
    }

}