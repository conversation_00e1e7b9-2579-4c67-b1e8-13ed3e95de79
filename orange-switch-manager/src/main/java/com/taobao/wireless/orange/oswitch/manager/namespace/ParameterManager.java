package com.taobao.wireless.orange.oswitch.manager.namespace;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.ParameterStatus;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.oswitch.dal.dao.*;
import com.taobao.wireless.orange.oswitch.dal.entity.*;
import com.taobao.wireless.orange.oswitch.manager.common.PageUtil;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionVersionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterConditionVersionBO;
import com.taobao.wireless.orange.publish.config.model.ParameterGroup;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterVersionBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.CONDITION_SEPARATOR;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;

@Service
@Validated
public class ParameterManager {

    @Autowired
    private OParameterDAO parameterDAO;
    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;
    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;
    @Autowired
    private OParameterVersionDAO parameterVersionDAO;
    @Autowired
    private OConditionDAO conditionDAO;

    /**
     * 查询参数列表，支持分页和条件查询
     *
     * @param query      查询条件
     * @param pagination 分页信息
     * @return 参数分页列表结果
     */
    public Page<ParameterBO> query(ParameterBO query, Pagination pagination) {
        Page<OParameterDO> pageResult = parameterDAO.lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getNamespaceId()), OParameterDO::getNamespaceId, query.getNamespaceId())
                .ne(OParameterDO::getStatus, ParameterStatus.INVALID)
                .eq(StringUtils.isNotBlank(query.getParameterKey()), OParameterDO::getParameterKey, query.getParameterKey())
                .and(StringUtils.isNotBlank(query.getKeyword()), wrapper -> wrapper
                        .like(OParameterDO::getParameterKey, query.getKeyword())
                        .or()
                        .like(OParameterDO::getDescription, query.getKeyword())
                )
                .orderByDesc(OParameterDO::getId)
                .page(PageUtil.build(pagination));

        Page<ParameterBO> result = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            result.setRecords(new ArrayList<>());
            return result;
        }

        // 转换并填充详情
        List<ParameterBO> parameters = BeanUtil.createListFromProperties(pageResult.getRecords(), ParameterBO.class);
        fillParameterOnlineVersionAndConditions(parameters);
        fillInPublishReleaseOrder(parameters);

        return result.setRecords(parameters);
    }

    private void fillInPublishReleaseOrder(List<ParameterBO> parameters) {
        List<String> parameterIds = parameters.stream()
                .map(ParameterBO::getParameterId)
                .collect(Collectors.toList());

        List<OParameterVersionDO> inPublishParameterVersions = getParameterVersionsByStatuses(parameterIds, List.of(VersionStatus.INIT), false);
        Map<String, OReleaseOrderDO> releaseVersion2Order = getReleaseVersion2Order(inPublishParameterVersions);
        Map<String, OReleaseOrderDO> parameterId2InPublishOrder = inPublishParameterVersions.stream()
                .collect(Collectors.toMap(OParameterVersionDO::getParameterId, (p -> releaseVersion2Order.get(p.getReleaseVersion()))));

        parameters.forEach(parameter -> parameter.setInPublishReleaseOrder(parameterId2InPublishOrder.get(parameter.getParameterId())));
    }

    /**
     * 为参数列表填充当前线上生效配置详情
     */
    private void fillParameterOnlineVersionAndConditions(List<ParameterBO> parameterBOs) {
        List<String> parameterIds = parameterBOs.stream()
                .map(ParameterBO::getParameterId)
                .collect(Collectors.toList());

        // 批量获取版本和条件（线上版本优先）
        var parameterId2Version = getOnlineFirstParameterVersionMap(parameterIds);
        var parameterId2ParamConditions = getOnlineFirstParameterConditionsMap(parameterIds);

        // 为每个参数填充详情
        parameterBOs.forEach(parameter -> fillParameterVersionAndConditions(parameter, parameterId2Version, parameterId2ParamConditions));
    }

    public void fillParameterVersionAndConditions(ParameterBO parameter,
                                                  Map<String, OParameterVersionDO> parameterId2Version,
                                                  Map<String, List<OParameterConditionVersionDO>> parameterId2ParamConditions) {
        // 1. 填充参数版本
        String parameterId = parameter.getParameterId();
        OParameterVersionDO parameterVersion = parameterId2Version.get(parameterId);
        parameter.setParameterVersion(parameterVersion);

        List<OParameterConditionVersionDO> parameterConditions = parameterId2ParamConditions.get(parameterId);
        if (CollectionUtils.isEmpty(parameterConditions)) {
            return;
        }

        // 2. 填充参数条件
        Map<String, OParameterConditionVersionDO> conditionId2ParameterCondition = parameterConditions.stream()
                .collect(Collectors.toMap(OParameterConditionVersionDO::getConditionId, Function.identity(),
                        (v1, v2) -> v1));
        List<OParameterConditionVersionDO> versions = getConditionsOrder(parameterVersion)
                .stream()
                .map(conditionId2ParameterCondition::get)
                .collect(Collectors.toList());
        // 尾部追加默认条件版本记录
        versions.add(conditionId2ParameterCondition.get(DEFAULT_CONDITION_ID));
        parameter.setParameterConditionVersions(versions);
    }

    /**
     * 获取发布版本和发布单的映射
     *
     * @param inPublishParameterVersions 发布中的参数版本
     * @return 发布版本到发布单的映射
     */
    private Map<String, OReleaseOrderDO> getReleaseVersion2Order(List<OParameterVersionDO> inPublishParameterVersions) {
        if (CollectionUtils.isEmpty(inPublishParameterVersions)) {
            return new HashMap<>(0);
        }
        List<String> releaseVersions = inPublishParameterVersions
                .stream()
                .map(OParameterVersionDO::getReleaseVersion)
                .distinct()
                .collect(Collectors.toList());

        return releaseOrderDAO.getReleaseOrderMapByReleaseVersions(releaseVersions);
    }

    public List<String> getConditionsOrder(OParameterVersionDO parameterVersion) {
        return Optional.ofNullable(parameterVersion.getConditionsOrder())
                .filter(StringUtils::isNotBlank)
                .map(conditionsOrder -> Arrays.asList(conditionsOrder.split(CONDITION_SEPARATOR)))
                .orElse(new ArrayList<>());
    }

    public List<OParameterVersionDO> getParameterVersionsByStatuses(List<String> parameterIds, List<VersionStatus> statuses, boolean isExist) {
        if (CollectionUtils.isEmpty(parameterIds)) {
            return List.of();
        }

        return parameterVersionDAO.lambdaQuery()
                .in(OParameterVersionDO::getParameterId, parameterIds)
                .ne(isExist, OParameterVersionDO::getChangeType, ChangeType.DELETE)
                .in(CollectionUtils.isNotEmpty(statuses), OParameterVersionDO::getStatus, statuses)
                .list();
    }

    public Map<String, OParameterVersionDO> getOnlineFirstParameterVersionMap(List<String> parameterIds) {
        return getParameterVersionsByStatuses(parameterIds, Arrays.asList(VersionStatus.RELEASED, VersionStatus.INIT), true)
                .stream()
                .collect(Collectors.toMap(
                        OParameterVersionDO::getParameterId,
                        Function.identity(),
                        (v1, v2) -> {
                            return v1.getStatus().equals(VersionStatus.RELEASED) ? v1 : v2;
                        }
                ));
    }

    public Map<String, OParameterVersionDO> getOnlineParameterVersionMap(List<String> parameterIds) {
        return getParameterVersionsByStatuses(parameterIds, List.of(VersionStatus.RELEASED), true)
                .stream()
                .collect(Collectors.toMap(
                        OParameterVersionDO::getParameterId,
                        Function.identity()
                ));
    }

    /**
     * 获取参数条件
     */
    private List<OParameterConditionVersionDO> getParameterConditionVersionsByStatuses(List<String> parameterIds, List<VersionStatus> statuses, boolean isExist) {
        if (CollectionUtils.isEmpty(parameterIds)) {
            return List.of();
        }

        return parameterConditionVersionDAO.lambdaQuery()
                .in(OParameterConditionVersionDO::getParameterId, parameterIds)
                .ne(isExist, OParameterConditionVersionDO::getChangeType, ChangeType.DELETE)
                .in(CollectionUtils.isNotEmpty(statuses), OParameterConditionVersionDO::getStatus, statuses)
                .list();
    }

    /**
     * 获取线上的参数条件
     */
    public Map<String, List<OParameterConditionVersionDO>> getOnlineParameterConditionsMap(List<String> parameterIds) {
        return getParameterConditionVersionsByStatuses(parameterIds, List.of(VersionStatus.RELEASED), true)
                .stream()
                .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId,
                        Collectors.toList())
                );
    }

    /**
     * 获取INIT状态的参数条件
     */
    public Map<String, List<OParameterConditionVersionDO>> getInitParameterConditionsMap(List<String> parameterIds) {
        return getParameterConditionVersionsByStatuses(parameterIds, List.of(VersionStatus.INIT), false)
                .stream()
                .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId,
                        Collectors.toList())
                );
    }

    /**
     * 获取参数条件版本（线上优先）
     *
     * @param parameterIds
     * @return
     */
    public Map<String, List<OParameterConditionVersionDO>> getOnlineFirstParameterConditionsMap(List<String> parameterIds) {
        return getParameterConditionVersionsByStatuses(parameterIds, List.of(VersionStatus.RELEASED, VersionStatus.INIT), true)
                .stream()
                .collect(Collectors.groupingBy(
                        i -> Pair.of(i.getParameterId(), i.getConditionId())
                ))
                .values()
                .stream()
                .map(list -> list.stream()
                        .reduce((v1, v2) -> VersionStatus.RELEASED.equals(v1.getStatus()) ? v1 : v2)
                        .orElse(null)
                )
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId));
    }

    /**
     * 批量创建参数变更
     *
     * @param namespace         命名空间
     * @param releaseVersion    发布版本
     * @param parameterVersions 参数版本列表
     */
    public void createParameterVersions(ONamespaceDO namespace, String releaseVersion, List<ParameterVersionBO> parameterVersions) {
        if (CollectionUtils.isEmpty(parameterVersions)) {
            return;
        }

        List<OParameterDO> newParameters = assembleNewParameters(namespace, parameterVersions);

        // 校验新增参数 KEY 是否已经存在
        checkParameterKeyDuplicate(namespace.getNamespaceId(), newParameters);

        // 校验是否已经有人对该参数提前提交了变更（防止用户多窗口操作未刷新页面）
        checkPreviousReleaseVersion(parameterVersions);

        List<OParameterVersionDO> newParameterVersions = assembleNewParameterVersions(namespace, releaseVersion, parameterVersions);

        // 保存参数实体
        parameterDAO.saveBatch(newParameters);
        // 保存参数版本
        parameterVersionDAO.saveBatch(newParameterVersions);
    }

    /**
     * 校验参数条件是否重复
     *
     * @param parameterVersions 参数版本列表
     */
    public void checkParameterConditionDuplicate(List<ParameterVersionBO> parameterVersions) {
        for (ParameterVersionBO parameterVersion : parameterVersions) {
            if (CollectionUtils.isEmpty(parameterVersion.getConditionNamesOrder())) {
                continue;
            }

            parameterVersion.getConditionNamesOrder().stream()
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                    .entrySet()
                    .stream()
                    .filter(e -> e.getValue() > 1)
                    .findAny()
                    .ifPresent(e -> {
                        throw CommonException.getDynamicException(ExceptionEnum.PARAMETER_CONDITION_DUPLICATE, e.getKey());
                    });
        }
    }

    private void checkParameterKeyDuplicate(String namespaceId, List<OParameterDO> newParameters) {
        if (CollectionUtils.isEmpty(newParameters)) {
            return;
        }

        List<String> parameterKeys = newParameters.stream().map(OParameterDO::getParameterKey).collect(Collectors.toList());
        if (parameterDAO.lambdaQuery()
                .eq(OParameterDO::getNamespaceId, namespaceId)
                .in(OParameterDO::getParameterKey, parameterKeys)
                .ne(OParameterDO::getStatus, ParameterStatus.INVALID)
                .exists()) {
            throw new CommonException(ExceptionEnum.PARAMETER_KEY_DUPLICATE);
        }
    }

    /**
     * 处理参数实体
     */
    private List<OParameterDO> assembleNewParameters(ONamespaceDO namespace,
                                                     List<ParameterVersionBO> parameterVersions) {
        String appKey = namespace.getAppKey();
        String namespaceId = namespace.getNamespaceId();

        return parameterVersions.stream()
                .filter(parameterVersion -> ChangeType.CREATE.equals(parameterVersion.getChangeType()))
                .map(parameterVersion -> {
                    OParameterDO parameter = parameterVersion.getParameterBO();
                    parameter.setParameterId(SerializeUtil.UUID());
                    parameter.setAppKey(appKey);
                    parameter.setNamespaceId(namespaceId);
                    parameter.setStatus(ParameterStatus.INIT);
                    return parameter;
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理参数版本
     */
    private List<OParameterVersionDO> assembleNewParameterVersions(ONamespaceDO namespace,
                                                                   String releaseVersion,
                                                                   List<ParameterVersionBO> parameterVersions) {
        String appKey = namespace.getAppKey();
        String namespaceId = namespace.getNamespaceId();

        // 获取待更新参数的基础数据，用户后续在版本记录中填充
        List<String> parameterIds = parameterVersions.stream().map(ParameterVersionBO::getParameterId).filter(Objects::nonNull).toList();
        Map<String, OParameterDO> parameterId2Parameter = parameterDAO.getParameterMapByParameterIds(parameterIds);

        return parameterVersions.stream()
                .map(parameterVersion -> {
                    String parameterId = parameterVersion.getParameterBO().getParameterId();

                    List<String> conditionIds = conditionDAO.getOrderedConditionIdsByName(namespaceId, parameterVersion.getConditionNamesOrder());
                    parameterVersion.setConditionsOrder(StringUtils.join(conditionIds, CONDITION_SEPARATOR));
                    parameterVersion.setStatus(VersionStatus.INIT);
                    parameterVersion.setAppKey(appKey);
                    parameterVersion.setNamespaceId(namespaceId);
                    parameterVersion.setReleaseVersion(releaseVersion);
                    parameterVersion.setParameterId(parameterId);

                    OParameterDO parameter = parameterId2Parameter.getOrDefault(parameterId, parameterVersion.getParameterBO());
                    parameterVersion.setParameterKey(parameter.getParameterKey());
                    parameterVersion.setValueType(parameter.getValueType());

                    return parameterVersion;
                })
                .collect(Collectors.toList());
    }

    /**
     * 校验是否已经有人对该参数提前提交了变更（防止用户多窗口操作未刷新页面）
     */
    private void checkPreviousReleaseVersion(List<ParameterVersionBO> parameterVersions) {
        List<String> parameterIds = parameterVersions.stream()
                .map(ParameterVersionBO::getParameterId)
                .collect(Collectors.toList());
        var onlineParamId2Version = getOnlineParameterVersionMap(parameterIds);

        parameterVersions.forEach(parameterVersion -> {
            ParameterBO parameterBO = parameterVersion.getParameterBO();
            OParameterVersionDO onlineParameterVersion = onlineParamId2Version.get(parameterBO.getParameterId());
            if (onlineParameterVersion != null &&
                    !onlineParameterVersion.getReleaseVersion().equals(parameterVersion.getPreviousReleaseVersion())) {
                throw CommonException.getDynamicException(
                        ExceptionEnum.PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_MATCH,
                        parameterBO.getParameterKey()
                );
            }
        });
    }

    /**
     * 新增参数条件版本记录
     *
     * @param namespace         命名空间
     * @param releaseVersion    发布版本
     * @param parameterVersions 参数版本列表
     * @param conditionVersions 条件版本列表
     */
    public void createParameterConditionVersions(ONamespaceDO namespace,
                                                 String releaseVersion,
                                                 List<ParameterVersionBO> parameterVersions,
                                                 List<ConditionVersionBO> conditionVersions) {
        if (CollectionUtils.isEmpty(parameterVersions)) {
            return;
        }

        // 校验参数默认条件
        checkParameterDefaultCondition(parameterVersions);

        // 获取新增的参数条件变更记录
        List<OParameterConditionVersionDO> newParameterConditionVersions =
                assembleNewParameterConditionVersions(namespace, releaseVersion, parameterVersions, conditionVersions);

        // 保存参数条件版本
        if (CollectionUtils.isNotEmpty(newParameterConditionVersions)) {
            parameterConditionVersionDAO.saveBatch(newParameterConditionVersions);
        }
    }

    /**
     * 校验参数默认条件
     *
     * @param parameterVersions
     */
    public void checkParameterDefaultCondition(List<ParameterVersionBO> parameterVersions) {
        parameterVersions.forEach(parameterVersion -> {
            List<ParameterConditionVersionBO> parameterConditionVersionBOS = parameterVersion.getParameterConditionVersions();
            if (CollectionUtils.isEmpty(parameterConditionVersionBOS)) {
                return;
            }

            ParameterConditionVersionBO defaultCondition = parameterConditionVersionBOS.stream()
                    .filter(i -> DEFAULT_CONDITION_ID.equals(i.getConditionId()))
                    .findFirst()
                    .orElse(null);

            // 新增参数一定有默认值
            if (ChangeType.CREATE.equals(parameterVersion.getChangeType()) && defaultCondition == null) {
                throw CommonException.getDynamicException(ExceptionEnum.PARAMETER_DEFAULT_CONDITION_NOT_FOUND, parameterVersion.getParameterBO().getParameterKey());
            }

            // 不支持删除默认值
            if (defaultCondition != null && ChangeType.DELETE.equals(defaultCondition.getChangeType())) {
                throw CommonException.getDynamicException(ExceptionEnum.PARAMETER_CONDITION_DELETE_DEFAULT, parameterVersion.getParameterBO().getParameterKey());
            }
        });
    }

    /**
     * 将参数按照变更类型分组
     *
     * @param parameterVersions 参数版本列表
     * @return 参数分组（上线参数和下线参数）
     */
    private <T extends OParameterVersionDO> ParameterGroup groupParameters(List<T> parameterVersions) {
        // 按照参数变更类型分组：true=待下线参数，false=待更新参数
        Map<Boolean, List<T>> parameterMap = parameterVersions
                .stream()
                .collect(Collectors.groupingBy(i -> ChangeType.DELETE.equals(i.getChangeType())));

        // 提取待下线的参数列表
        var offlineParameters = Optional.ofNullable(parameterMap.get(true)).orElse(Collections.emptyList());

        // 提取待更新的参数列表
        var updateParameters = Optional.ofNullable(parameterMap.get(false)).orElse(Collections.emptyList());

        return new ParameterGroup(updateParameters, offlineParameters);
    }

    /**
     * 构建参数条件版本列表
     */
    private List<OParameterConditionVersionDO> assembleNewParameterConditionVersions(ONamespaceDO namespace,
                                                                                     String releaseVersion,
                                                                                     List<ParameterVersionBO> parameterVersions,
                                                                                     List<ConditionVersionBO> conditionVersions) {
        ParameterGroup parameterGroup = groupParameters(parameterVersions);

        List<OParameterConditionVersionDO> newParameterConditionVersions = new ArrayList<>();

        // 处理删除参数，需要将该参数下的条件值均删除
        @SuppressWarnings("unchecked")
        List<ParameterVersionBO> offlineParameters = (List<ParameterVersionBO>) parameterGroup.offlineParameters();
        newParameterConditionVersions.addAll(assembleOfflineParameterConditionVersions(releaseVersion, offlineParameters));

        // 处理更新/新增参数
        @SuppressWarnings("unchecked")
        List<ParameterVersionBO> updateParameters = (List<ParameterVersionBO>) parameterGroup.updateParameters();
        newParameterConditionVersions.addAll(assembleUpdateParameterConditionVersions(namespace,
                releaseVersion,
                updateParameters,
                conditionVersions));

        return newParameterConditionVersions;
    }


    /**
     * 处理下线参数
     *
     * @param releaseVersion    发布版本
     * @param offlineParameters 下线参数
     */
    private List<OParameterConditionVersionDO> assembleOfflineParameterConditionVersions(String releaseVersion,
                                                                                         List<ParameterVersionBO> offlineParameters) {
        if (CollectionUtils.isEmpty(offlineParameters)) {
            return List.of();
        }

        List<OParameterConditionVersionDO> newParameterConditionVersions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(offlineParameters)) {
            List<String> offlineParameterIds = offlineParameters.stream().map(ParameterVersionBO::getParameterId).collect(Collectors.toList());
            List<OParameterConditionVersionDO> deleteParameterConditions = getOnlineParameterConditionsMap(offlineParameterIds).values().stream()
                    .flatMap(Collection::stream)
                    .map(pc -> {
                        OParameterConditionVersionDO deleteParameterCondition = BeanUtil.createFromProperties(pc, OParameterConditionVersionDO.class);
                        deleteParameterCondition.setId(null);
                        deleteParameterCondition.setChangeType(ChangeType.DELETE);
                        deleteParameterCondition.setReleaseVersion(releaseVersion);
                        deleteParameterCondition.setStatus(VersionStatus.INIT);
                        deleteParameterCondition.setValue("");
                        deleteParameterCondition.setPreviousReleaseVersion(pc.getReleaseVersion());
                        return deleteParameterCondition;
                    })
                    .toList();
            newParameterConditionVersions.addAll(deleteParameterConditions);
        }

        return newParameterConditionVersions;
    }

    /**
     * 处理新增/更新参数
     *
     * @param namespace         命名空间
     * @param releaseVersion    发布版本
     * @param onlineParameters  上线参数
     * @param conditionVersions 条件版本
     */
    private List<OParameterConditionVersionDO> assembleUpdateParameterConditionVersions(ONamespaceDO namespace,
                                                                                        String releaseVersion,
                                                                                        List<ParameterVersionBO> onlineParameters,
                                                                                        List<ConditionVersionBO> conditionVersions) {
        String appKey = namespace.getAppKey();
        String namespaceId = namespace.getNamespaceId();

        // 条件名称到条件ID的映射
        Map<String, String> conditionName2Id = conditionVersions.stream()
                .collect(Collectors.toMap(
                        v -> v.getCondition().getName(),
                        ConditionVersionBO::getConditionId
                ));

        List<OParameterConditionVersionDO> newParameterConditionVersions = new ArrayList<>();

        for (ParameterVersionBO parameterVersionBO : onlineParameters) {
            List<ParameterConditionVersionBO> parameterConditionVersionBOS = parameterVersionBO.getParameterConditionVersions();
            // 只是调整条件顺序时该列表为空
            if (CollectionUtils.isEmpty(parameterConditionVersionBOS)) {
                continue;
            }

            for (ParameterConditionVersionBO parameterConditionVersion : parameterConditionVersionBOS) {
                parameterConditionVersion.setAppKey(appKey);
                parameterConditionVersion.setNamespaceId(namespaceId);
                parameterConditionVersion.setReleaseVersion(releaseVersion);
                parameterConditionVersion.setParameterId(parameterVersionBO.getParameterId());
                parameterConditionVersion.setStatus(VersionStatus.INIT);

                // 说明引用了本次新增的条件
                if (parameterConditionVersion.getConditionId() == null) {
                    String conditionName = parameterConditionVersion.getConditionName();
                    parameterConditionVersion.setConditionId(conditionName2Id.get(conditionName));
                }

                // 删除的条件值置为空
                if (ChangeType.DELETE.equals(parameterConditionVersion.getChangeType())) {
                    parameterConditionVersion.setValue("");
                }
            }

            newParameterConditionVersions.addAll(parameterConditionVersionBOS);
        }

        return newParameterConditionVersions;
    }
}
