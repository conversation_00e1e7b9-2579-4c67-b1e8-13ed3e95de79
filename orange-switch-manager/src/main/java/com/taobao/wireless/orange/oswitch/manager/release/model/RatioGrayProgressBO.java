package com.taobao.wireless.orange.oswitch.manager.release.model;

import com.taobao.wmcc.client.publish.PublishTaskInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
public class RatioGrayProgressBO {
    private ReleaseProgressNode releaseProgressNode;
    private List<RatioGrayProgressNode> ratioGrayProgressNodes;

    @Data
    public static class ReleaseProgressNode {
        private Date startTime;
        private Date scheduleTime;
        private String agatewareTaskId;
        private PublishTaskInfo agatewareTaskInfo;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class RatioGrayProgressNode extends ReleaseProgressNode {
        private Integer grayRatio;
    }
}
