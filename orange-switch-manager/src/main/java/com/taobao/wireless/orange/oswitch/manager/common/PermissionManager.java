package com.taobao.wireless.orange.oswitch.manager.common;

import com.taobao.wireless.orange.common.thread.OThreadContextHolder;
import com.taobao.wireless.orange.external.acl.AclService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.taobao.wireless.orange.external.acl.AclService.ADMIN_PERMISSION;

@Service
public class PermissionManager {
    @Autowired
    private AclService aclService;

    // 是否有管理员权限
    public boolean isAdmin() {
        Integer bucId = OThreadContextHolder.get().getBucId();
        return aclService.checkPermission(ADMIN_PERMISSION, bucId);
    }
}
