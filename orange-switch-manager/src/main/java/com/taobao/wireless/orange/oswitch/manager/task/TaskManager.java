package com.taobao.wireless.orange.oswitch.manager.task;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.constant.enums.TaskBizType;
import com.taobao.wireless.orange.common.constant.enums.TaskHandlerStatus;
import com.taobao.wireless.orange.common.constant.enums.TaskStatus;
import com.taobao.wireless.orange.common.constant.enums.TaskType;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.oswitch.dal.dao.OTaskDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OTaskHandlerDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OTaskDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OTaskHandlerDO;
import com.taobao.wireless.orange.oswitch.manager.task.model.TaskBO;
import com.taobao.wireless.orange.oswitch.manager.task.model.TaskHandlerBO;
import com.taobao.wireless.orange.oswitch.manager.common.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务管理器
 */
@Component
public class TaskManager {

    @Autowired
    private OTaskDAO taskDAO;

    @Autowired
    private OTaskHandlerDAO taskHandlerDAO;

    /**
     * 查询我的任务列表
     *
     * @param query      查询条件
     * @param pagination 分页参数
     * @return 任务分页结果
     */
    public Page<TaskBO> queryMyTasks(TaskHandlerBO query, Pagination pagination) {
        // 首先查询用户的任务处理记录
        Page<OTaskHandlerDO> taskHandlers = taskHandlerDAO.lambdaQuery()
                .eq(OTaskHandlerDO::getUserId, ThreadContextUtil.getWorkerId())
                .eq(query.getTaskType() != null, OTaskHandlerDO::getTaskType, query.getTaskType())
                .eq(query.getStatus() != null, OTaskHandlerDO::getStatus, query.getStatus())
                .page(PageUtil.build(pagination));

        Page<TaskBO> result = new Page<>(taskHandlers.getCurrent(), taskHandlers.getSize(), taskHandlers.getTotal());
        if (CollectionUtils.isEmpty(taskHandlers.getRecords())) {
            result.setRecords(List.of());
            return result;
        }

        // 获取任务ID列表
        List<String> taskIds = taskHandlers.getRecords().stream()
                .map(OTaskHandlerDO::getTaskId)
                .collect(Collectors.toList());

        // 构建查询条件
        var pageResult = taskDAO.lambdaQuery()
                .in(OTaskDO::getTaskId, taskIds)
                .orderByDesc(OTaskDO::getTaskId)
                .page(PageUtil.build(pagination));

        var tasks = BeanUtil.createListFromProperties(pageResult.getRecords(), TaskBO.class);
        fillMyHandlerStatus(tasks, taskHandlers.getRecords());
        result.setRecords(tasks);
        return result;
    }

    private void fillMyHandlerStatus(List<TaskBO> tasks, List<OTaskHandlerDO> taskHandlers) {
        var taskId2Status = taskHandlers.stream().collect(Collectors.toMap(OTaskHandlerDO::getTaskId, OTaskHandlerDO::getStatus));
        tasks.forEach(task -> task.setMyHandlerStatus(taskId2Status.get(task.getTaskId())));
    }

    /**
     * 创建验收任务并分派给测试人员
     *
     * @param releaseVersion 发布版本
     * @param namespace      命名空间
     */
    @Transactional(rollbackFor = Exception.class)
    public void createVerifyTask(String releaseVersion, ONamespaceDO namespace) {
        List<String> testers = namespace.getTesters();
        if (CollectionUtils.isEmpty(testers)) {
            return;
        }

        String description = String.format("%s-%s-版本 %s 质量验证",
                namespace.getAppKey(), namespace.getName(), releaseVersion);

        // 生成任务ID
        String taskId = SerializeUtil.UUID();

        // 创建任务
        OTaskDO task = new OTaskDO();
        task.setTaskId(taskId);
        task.setType(TaskType.VERIFY);
        task.setStatus(TaskStatus.PENDING);
        task.setBizId(releaseVersion);
        task.setBizType(TaskBizType.RELEASE_ORDER);
        task.setDescription(description);
        task.setAppKey(namespace.getAppKey());
        task.setNamespaceId(namespace.getNamespaceId());
        taskDAO.save(task);

        // 为每个测试人员创建任务处理记录
        for (String tester : testers) {
            OTaskHandlerDO handler = new OTaskHandlerDO();
            handler.setTaskId(taskId);
            handler.setTaskType(TaskType.VERIFY);
            handler.setUserId(tester);
            handler.setStatus(TaskHandlerStatus.PENDING);
            taskHandlerDAO.save(handler);
        }

    }

    /**
     * 完成任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeReleaseOrderTask(String releaseVersion, TaskType taskType) {
        // 查找对应的验收任务
        OTaskDO task = taskDAO.lambdaQuery()
                .eq(OTaskDO::getBizType, TaskBizType.RELEASE_ORDER)
                .eq(OTaskDO::getBizId, releaseVersion)
                .eq(OTaskDO::getType, taskType)
                .eq(OTaskDO::getStatus, TaskStatus.PENDING)
                .one();

        if (task == null) {
            return;
        }

        String taskId = task.getTaskId();
        String userId = ThreadContextUtil.getWorkerId();

        // 更新当前用户的任务状态为已完成
        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, userId)
                .set(OTaskHandlerDO::getStatus, TaskHandlerStatus.COMPLETED)
                .update();

        // 更新其他用户的任务状态为无需处理
        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .ne(OTaskHandlerDO::getUserId, userId)
                .eq(OTaskHandlerDO::getStatus, TaskHandlerStatus.PENDING)
                .set(OTaskHandlerDO::getStatus, TaskHandlerStatus.NO_NEED)
                .update();

        // 更新任务状态为已完成
        taskDAO.lambdaUpdate()
                .eq(OTaskDO::getTaskId, taskId)
                .set(OTaskDO::getStatus, TaskStatus.COMPLETED)
                .update();
    }

    /**
     * 创建发布任务
     *
     * @param releaseVersion 发布版本
     * @param namespace      命名空间
     */
    @Transactional(rollbackFor = Exception.class)
    public void createReleaseTask(String releaseVersion, ONamespaceDO namespace) {
        List<String> userIds = namespace.getOwners();
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        // todo: 待将 appKey 替换为 appName
        String description = String.format("%s-%s-发布单 %s 验收通过待发布",
                namespace.getAppKey(), namespace.getName(), releaseVersion);

        // 生成任务ID
        String taskId = SerializeUtil.UUID();

        // 创建任务
        OTaskDO task = new OTaskDO();
        task.setTaskId(taskId);
        task.setType(TaskType.RELEASE);
        task.setStatus(TaskStatus.PENDING);
        task.setBizId(releaseVersion);
        task.setBizType(TaskBizType.RELEASE_ORDER);
        task.setDescription(description);
        task.setAppKey(namespace.getAppKey());
        task.setNamespaceId(namespace.getNamespaceId());
        taskDAO.save(task);

        // 为每个审批人员创建任务处理记录
        for (String userId : userIds) {
            OTaskHandlerDO handler = new OTaskHandlerDO();
            handler.setTaskId(taskId);
            handler.setUserId(userId);
            handler.setTaskType(TaskType.RELEASE);
            handler.setStatus(TaskHandlerStatus.PENDING);
            taskHandlerDAO.save(handler);
        }
    }

    /**
     * 取消任务
     *
     * @param releaseVersion 发布版本
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelReleaseOrderTask(String releaseVersion) {
        // 查找对应的任务
        OTaskDO task = taskDAO.lambdaQuery()
                .eq(OTaskDO::getBizId, releaseVersion)
                .eq(OTaskDO::getBizType, TaskBizType.RELEASE_ORDER)
                .eq(OTaskDO::getStatus, TaskStatus.PENDING)
                .one();

        if (task == null) {
            return;
        }

        String taskId = task.getTaskId();

        // 更新处理人状态为无需处理
        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getStatus, TaskHandlerStatus.PENDING)
                .set(OTaskHandlerDO::getStatus, TaskHandlerStatus.NO_NEED)
                .update();

        // 更新任务状态
        taskDAO.lambdaUpdate()
                .eq(OTaskDO::getTaskId, taskId)
                .set(OTaskDO::getStatus, TaskStatus.CANCELED)
                .update();
    }
}