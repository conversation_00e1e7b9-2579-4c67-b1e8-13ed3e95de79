package com.taobao.wireless.orange.oswitch.manager.release.operation;

import com.alibaba.fastjson2.JSON;
import com.alibaba.goc.changefree.model.ChangeCheckRes;
import com.alibaba.goc.changefree.model.CheckStatusEnum;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderOperationDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderStageDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderOperationDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.common.PermissionManager;
import com.taobao.wireless.orange.oswitch.manager.common.TairManager;
import com.taobao.wireless.orange.oswitch.manager.release.ChangefreeManager;
import com.taobao.wireless.orange.oswitch.manager.release.TigaGrayManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationAdditionalData;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import com.taobao.wireless.orange.oswitch.manager.release.model.SkipCommandBO;
import com.taobao.wireless.tiga.release.common.task.TaskCmd;
import com.taobao.wireless.tiga.release.console.api.task.model.dto.TaskStageListDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Optional;

/**
 * 发布操作模板
 */
@Slf4j
public abstract class AbstractOperationTemplate<T extends OperationAdditionalData, R extends OperationResult> implements OperationStrategy<T, R> {

    @Autowired
    protected OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private TairManager tairManager;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    protected OReleaseOrderOperationDAO releaseOrderOperationDAO;

    @Autowired
    private PermissionManager permissionManager;

    @Autowired
    protected ChangefreeManager changefreeManager;

    @Autowired
    private TigaGrayManager tigaGrayManager;

    @Autowired
    protected OReleaseOrderStageDAO releaseOrderStageDAO;

    /**
     * 发布单操作标准流程
     */
    public void execute(OperationContext<T, R> context) {
        // 获取上下文信息，用于后续消费
        loadContextDetail(context);

        // 获取命名空间锁
        acquireNamespaceLock(context);

        try {
            // 前置校验
            validation(context);

            // 创建操作记录 (INIT 状态)
            Long operationId = createOperationRecord(context);

            // 业务逻辑执行
            try {
                executeOperation(context);
            } catch (Throwable e) {
                // 异常处理和失败状态记录
                OperationResult operationResult = new OperationResult();
                operationResult.setMessage(e.getMessage());
                updateOperationStatusAndResult(operationId, OperationStatus.FAILED, operationResult);
                throw e;
            }

            // 更新发布单阶段和状态
            updateReleaseOrderStatusAndStages(context);

            // 更新操作记录
            updateOperationStatusAndResult(operationId, OperationStatus.SUCCESS, context.getOperationResult());

            // 操作完成后的后续处理逻辑（失败不影响操作执行）
            afterOperation(context);
        } finally {
            // 释放锁
            releaseNamespaceLock(context);
        }
    }

    public ReleaseOrderStatus getTargetReleaseOrderStatus(OperationContext<T, R> context) {
        return null;
    }

    /**
     * 操作完成后的后续处理逻辑（失败不影响任务执行）
     */
    @Async("releaseOrderExecutor")
    protected void afterOperation(OperationContext<T, R> context) {
        ReleaseOrderStatus targetReleaseOrderStatus = getTargetReleaseOrderStatus(context);
        OReleaseOrderDO releaseOrder = context.getReleaseOrder();

        try {
            // 1. 任务生成&处理
            handleTask(context);

            // 2. 结束 tiga 灰度单
            if (targetReleaseOrderStatus != null && targetReleaseOrderStatus.isFinished() && releaseOrder.getTigaTaskId() != null) {
                TaskStageListDTO tigaTaskStageList = tigaGrayManager.getTigaTaskStageList(releaseOrder);
                if (!tigaTaskStageList.getTask().getStatus().isFinished()) {
                    tigaGrayManager.doTask(releaseOrder, TaskCmd.CANCEL, null);
                }
            }
        } catch (Throwable e) {
            log.error("releaseOrder[{}] afterOperation error", context.getReleaseVersion(), e);
        }
    }

    /**
     * 处理操作相关的任务逻辑（异步执行）
     * 子类重写此方法实现自己的任务处理逻辑
     *
     * @param context 操作上下文
     */
    protected void handleTask(OperationContext<T, R> context) {
        // 默认不处理任务，子类重写
    }

    /**
     * 前置校验，包含状态校验、权限校验、入参校验
     */
    private void validation(OperationContext<T, R> context) {
        // 订单状态校验
        validateOrderStatus(context);

        // 订单阶段校验
        validateOrderStage(context);

        // 权限校验
        validatePermission(context);

        // 入参校验
        validateParameters(context);

        // CF 变更准入检查
        changeFreeHoldCheck(context);
    }

    private void changeFreeHoldCheck(OperationContext<T, R> context) {
        if (!needCheckChangefree() || isSkipped(context.getReleaseVersion(), SkipType.CF_HOLD_CHECK)) {
            return;
        }

        ChangeCheckRes checkRes = changefreeManager.check(context.getReleaseOrder(), false, context.getReleaseOrder().getIsEmergent());

        if (!CheckStatusEnum.CHECK_PASS.equals(checkRes.getCheckStatusEnum())) {
            throw new CommonException(ExceptionEnum.CHANGEFREE_CHECK_NOT_PASS);
        }
    }

    private boolean isSkipped(String releaseVersion, List<SkipType> skipTypes) {
        List<OReleaseOrderOperationDO> operations = releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .eq(OReleaseOrderOperationDO::getType, OperationType.SKIP)
                .eq(OReleaseOrderOperationDO::getStatus, OperationStatus.SUCCESS)
                .list();

        for (OReleaseOrderOperationDO operation : operations) {
            SkipCommandBO skipCommandBO = JSON.parseObject(operation.getParams(), SkipCommandBO.class);
            if (skipTypes.contains(skipCommandBO.getSkipType()) || SkipType.ALL.equals(skipCommandBO.getSkipType())) {
                return true;
            }
        }
        return false;
    }

    private boolean isSkipped(String releaseVersion, SkipType skipType) {
        return isSkipped(releaseVersion, List.of(skipType));
    }

    @Override
    public boolean needCheckChangefree() {
        return false;
    }

    @Override
    public boolean needLock() {
        return false;
    }

    /**
     * 状态校验
     */
    private void validateOrderStatus(OperationContext<T, R> context) {
        if (context.getReleaseOrder().getStatus().isFinished()) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_IS_FINISHED);
        }
    }

    /**
     * 阶段校验
     */
    public void validateOrderStage(OperationContext<T, R> context) {
        var stage = getTargetReleaseOrderStage(context);
        if (stage == null || stage.getType() == null) {
            return;
        }

        var operationStage = releaseOrderStageDAO.lambdaQuery()
                .eq(OReleaseOrderStageDO::getReleaseVersion, context.getReleaseVersion())
                .eq(OReleaseOrderStageDO::getType, stage.getType())
                .oneOpt()
                .orElse(null);

        if (operationStage == null) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STAGE_NOT_EXIST);
        }

        // 禁止对已完成的阶段进行重复操作
        if (operationStage.getStatus().isCompleted()) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STAGE_ALREADY_COMPLETED);
        }

        // 之前阶段均已完成
        releaseOrderStageDAO.lambdaQuery()
                .eq(OReleaseOrderStageDO::getReleaseVersion, context.getReleaseVersion())
                .lt(OReleaseOrderStageDO::getStageOrder, operationStage.getStageOrder())
                .list()
                .forEach(s -> {
                    if (!s.getStatus().isCompleted()) {
                        throw CommonException.getDynamicException(ExceptionEnum.RELEASE_ORDER_STAGE_NOT_COMPLETED, s.getType().getStageName());
                    }
                });
    }

    /**
     * 权限校验
     */
    public void validatePermission(OperationContext<T, R> context) {
        if (permissionManager.isAdmin()) {
            return;
        }

        var namespace = context.getNamespace();
        String workerId = ThreadContextUtil.getWorkerId();

        if (StringUtils.isBlank(workerId) || !namespace.getOwners().contains(workerId)) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION);
        }
    }

    /**
     * 入参校验
     */
    public void validateParameters(OperationContext<T, R> context) {
    }

    /**
     * 创建操作记录
     */
    private Long createOperationRecord(OperationContext<T, R> context) {
        OReleaseOrderDO releaseOrder = context.getReleaseOrder();

        OReleaseOrderOperationDO operation = new OReleaseOrderOperationDO();
        operation.setReleaseVersion(context.getReleaseVersion());
        operation.setType(getOperationType());
        operation.setAppKey(releaseOrder.getAppKey());
        operation.setNamespaceId(releaseOrder.getNamespaceId());
        operation.setParams(context.getAdditionalData() != null ? JSON.toJSONString(context.getAdditionalData()) : null);
        operation.setStatus(OperationStatus.INIT);

        var stage = getTargetReleaseOrderStage(context);
        if (stage != null && stage.getType() != null) {
            operation.setStageType(stage.getType());
        }

        releaseOrderOperationDAO.save(operation);
        return operation.getId();
    }

    /**
     * 更新操作记录状态
     */
    private void updateOperationStatusAndResult(Long id, OperationStatus status, OperationResult operationResult) {
        var operation = new OReleaseOrderOperationDO();
        operation.setId(id);
        if (status != null) {
            operation.setStatus(status);
        }
        if (operationResult != null) {
            operation.setResult(JSON.toJSONString(operationResult));
        }
        releaseOrderOperationDAO.updateById(operation);
    }

    /**
     * 获取上下文详情
     */
    private void loadContextDetail(OperationContext<T, R> context) {
        OReleaseOrderDO releaseOrder = releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getReleaseVersion, context.getReleaseVersion())
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST));
        context.setReleaseOrder(releaseOrder);

        ONamespaceDO namespace = namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId());
        if (namespace == null) {
            throw new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST);
        }
        context.setNamespace(namespace);
    }

    /**
     * 更新发布单状态的默认实现
     */
    private void updateReleaseOrderStatusAndStages(OperationContext<T, R> context) {
        // 更新发布单阶段状态
        updateReleaseOrderStage(context);

        var stageType = Optional.ofNullable(getTargetReleaseOrderStage(context))
                .map(OReleaseOrderStageDO::getType)
                .orElse(null);

        ReleaseOrderStatus orderStatus = getTargetReleaseOrderStatus(context);

        if (stageType == null && orderStatus == null) {
            return;
        }

        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, context.getReleaseVersion())
                .set(orderStatus != null, OReleaseOrderDO::getStatus, orderStatus)
                .set(stageType != null, OReleaseOrderDO::getCurrentStageType, stageType)
                .update();
    }

    /**
     * 更新发布单阶段状态
     */
    protected void updateReleaseOrderStage(OperationContext<T, R> context) {
        OReleaseOrderStageDO targetStage = getTargetReleaseOrderStage(context);
        if (targetStage != null) {
            // 更新阶段记录状态
            releaseOrderStageDAO.lambdaUpdate()
                    .eq(OReleaseOrderStageDO::getReleaseVersion, context.getReleaseVersion())
                    // fixme: 待优化逻辑
                    // skip all 需要把所有未完成非发布节点置为跳过
                    .eq(targetStage.getType() != null, OReleaseOrderStageDO::getType, targetStage.getType())
                    .ne(targetStage.getType() == null, OReleaseOrderStageDO::getType, StageType.RELEASE)

                    .notIn(OReleaseOrderStageDO::getStatus, StageStatus.getCompletedStatuses())
                    .set(OReleaseOrderStageDO::getStatus, targetStage.getStatus())
                    .update();
        }
    }

    /**
     * 获取 namespace 和应用锁
     */
    private void acquireNamespaceLock(OperationContext<T, R> context) {
        if (needLock() && !tairManager.lockNamespace(context.getNamespace().getNamespaceId())) {
            throw new CommonException(ExceptionEnum.NAMESPACE_LOCKED);
        }
    }

    /**
     * 释放 namespace 和应用锁
     */
    private void releaseNamespaceLock(OperationContext<T, R> context) {
        if (needLock()) {
            tairManager.unlockNamespace(context.getNamespace().getNamespaceId());
        }
    }
}