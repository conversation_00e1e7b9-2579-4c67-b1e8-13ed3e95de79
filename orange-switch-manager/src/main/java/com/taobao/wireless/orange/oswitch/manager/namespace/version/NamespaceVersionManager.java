package com.taobao.wireless.orange.oswitch.manager.namespace.version;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.oswitch.dal.dao.OIndexDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceVersionDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderStageDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OIndexDO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.manager.common.PageUtil;
import com.taobao.wireless.orange.oswitch.manager.namespace.NamespaceManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.version.model.ONamespaceVersionBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class NamespaceVersionManager {

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;
    @Autowired
    private NamespaceManager namespaceManager;
    @Autowired
    private NamespaceVersionResourceManager namespaceVersionResourceManager;
    @Autowired
    private OIndexDAO indexDAO;
    @Autowired
    private OReleaseOrderStageDAO releaseOrderStageDAO;

    /**
     * 查询命名空间版本历史
     *
     * @param namespaceId
     * @param pagination
     * @return
     */
    public Page<ONamespaceVersionBO> histories(String namespaceId, Pagination pagination) {
        Page<ONamespaceVersionDO> oNamespaceVersionDOPage = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.FINISH_IMPACT_RELEASE)
                .orderByDesc(ONamespaceVersionDO::getNamespaceChangeVersion)
                .page(PageUtil.build(pagination));

        Page<ONamespaceVersionBO> result = new Page<>(oNamespaceVersionDOPage.getCurrent(), oNamespaceVersionDOPage.getSize(), oNamespaceVersionDOPage.getTotal());
        if (oNamespaceVersionDOPage.getRecords().isEmpty()) {
            return result;
        }

        return Pipe.of(oNamespaceVersionDOPage.getRecords())
                .map(v -> BeanUtil.createListFromProperties(v, ONamespaceVersionBO.class))
                .apply(this::fillIndex)
                .map(result::setRecords)
                .get();
    }

    /**
     * 填充索引
     *
     * @param namespaceVersions
     */
    private void fillIndex(List<ONamespaceVersionBO> namespaceVersions) {
        List<String> indexVersion = namespaceVersions.stream()
                .map(ONamespaceVersionDO::getIndexVersion)
                .distinct()
                .toList();

        var version2Index = indexDAO.lambdaQuery()
                .in(OIndexDO::getIndexVersion, indexVersion)
                .eq(OIndexDO::getBaseIndexVersion, "0")
                .list()
                .stream()
                .collect(Collectors.toMap(OIndexDO::getIndexVersion, index -> index));

        namespaceVersions.forEach(namespaceVersionBO -> Optional.ofNullable(version2Index.get(namespaceVersionBO.getIndexVersion()))
                .ifPresent(index -> {
                    namespaceVersionBO.setIndexResourceId(index.getIndexResourceId());
                    namespaceVersionBO.setIndexGmtCreate(index.getGmtCreate());
                }));
    }

    @Transactional
    public String createNamespaceVersion(OReleaseOrderDO releaseOrder, NamespaceVersionChangeType changeType) {
        String namespaceId = releaseOrder.getNamespaceId();
        String releaseVersion = releaseOrder.getReleaseVersion();
        ONamespaceVersionDO currentNamespaceVersion = namespaceVersionDAO.getAvailableNamespaceVersionByNamespaceId(namespaceId);

        String nextVersion = String.valueOf(SerializeUtil.version());
        // 当一个 namespace 第一次创建发布单时，currentNamespaceVersion 为空
        String namespaceVersion = generateNamespaceVersion(currentNamespaceVersion, changeType, nextVersion);
        String namespaceChangeVersion = generateNamespaceChangeVersion(currentNamespaceVersion, changeType, nextVersion);

        // 失效当前版本记录
        namespaceVersionDAO.invalidateCurrentVersion(namespaceId);

        ONamespaceVersionDO namespaceVersionDO = insertNamespaceVersionRecord(releaseOrder, changeType, namespaceVersion, namespaceChangeVersion);

        // 根据 changeType 生成不同的变更产物
        if (NamespaceVersionChangeType.RATIO_GRAY.equals(changeType)) {
            // 如果该发布单历史未百分比发布过，第一次百分比发布，需要生成新的灰度配置
            var ratioGrayStage = releaseOrderStageDAO.getByReleaseVersionAndType(releaseVersion, StageType.RATIO_GRAY);
            if (ratioGrayStage == null || StageStatus.INIT.equals(ratioGrayStage.getStatus())) {
                namespaceVersionResourceManager.createNamespaceVersionResource(namespaceVersionDO, ConfigType.GRAY);
            }
        } else if (NamespaceVersionChangeType.FINISH_IMPACT_RELEASE.equals(changeType)) {
            // 生成最新的正式和灰度配置
            if (releaseOrder.getGrayRatio() != null && releaseOrder.getGrayRatio() > 0) {
                namespaceVersionResourceManager.createNamespaceVersionResource(namespaceVersionDO, ConfigType.GRAY);
            }
            namespaceVersionResourceManager.createNamespaceVersionResource(namespaceVersionDO, ConfigType.RELEASE);
        } else if (NamespaceVersionChangeType.CANCEL_RELEASE.equals(changeType)) {
            // 如果该发布单历史百分比发布过，取消，则需要生成新的灰度配置
            if (releaseOrder.getGrayRatio() != null && releaseOrder.getGrayRatio() > 0) {
                namespaceVersionResourceManager.createNamespaceVersionResource(namespaceVersionDO, ConfigType.GRAY);
            }
        }

        return namespaceVersionDO.getNamespaceVersion();
    }

    /**
     * 创建新的命名空间版本记录到数据库
     */
    private ONamespaceVersionDO insertNamespaceVersionRecord(OReleaseOrderDO releaseOrder, NamespaceVersionChangeType changeType, String namespaceVersion, String changeVersion) {
        String namespaceId = releaseOrder.getNamespaceId();
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(namespaceId);

        ONamespaceVersionDO newNamespaceVersion = new ONamespaceVersionDO();
        newNamespaceVersion.setAppKey(namespace.getAppKey());
        newNamespaceVersion.setNamespaceId(namespaceId);
        newNamespaceVersion.setReleaseVersion(releaseOrder.getReleaseVersion());
        newNamespaceVersion.setIsAvailable(Available.y);
        newNamespaceVersion.setChangeType(changeType);
        newNamespaceVersion.setNamespaceVersion(namespaceVersion);
        newNamespaceVersion.setNamespaceChangeVersion(changeVersion);
        newNamespaceVersion.setIsEmergent(releaseOrder.getIsEmergent());

        namespaceVersionDAO.save(newNamespaceVersion);
        return newNamespaceVersion;
    }

    /**
     * 生成命名空间版本号
     *
     * @param namespaceVersion
     * @param changeType
     * @return
     */
    private String generateNamespaceVersion(@Nullable ONamespaceVersionDO namespaceVersion, NamespaceVersionChangeType changeType, String nextVersion) {
        // 只有正式发布才会进行 namespace 版本号递增
        if (NamespaceVersionChangeType.FINISH_IMPACT_RELEASE.equals(changeType) || NamespaceVersionChangeType.FINISH_NO_IMPACT_RELEASE.equals(changeType)) {
            return nextVersion;
        }
        return Optional.ofNullable(namespaceVersion).map(ONamespaceVersionDO::getNamespaceVersion).orElse(null);
    }

    /**
     * 生成命名空间变更版本号
     *
     * @param namespaceVersion
     * @param changeType
     * @return
     */
    private String generateNamespaceChangeVersion(@Nullable ONamespaceVersionDO namespaceVersion, NamespaceVersionChangeType changeType, String nextVersion) {
        // 对线上产物有影响的发布或者是第一次发布（namespaceVersion 为空）递增 namespaceChangeVersion
        if (!NamespaceVersionChangeType.FINISH_NO_IMPACT_RELEASE.equals(changeType) || namespaceVersion == null) {
            return nextVersion;
        }
        return namespaceVersion.getNamespaceChangeVersion();
    }
}