package com.taobao.wireless.orange.oswitch.manager.namespace.model;

import com.alibaba.fastjson2.JSON;
import com.taobao.wireless.orange.common.model.SerializableObject;
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class NamespaceSnapshot implements SerializableObject {
    // 关联发布单号
    private String releaseVersion;

    // 命名空间版本号
    private String namespaceVersion;

    // 生成时间
    private Date gmtCreate;

    // 参数列表
    private List<ParameterSnapshot> parameterSnapshots;

    // 条件列表
    private List<ConditionSnapshot> conditionSnapshots;

    @Override
    public byte[] serialize() {
        return JSON.toJSONString(this).getBytes();
    }

    @Data
    public static class ParameterSnapshot extends OParameterVersionDO {
        private List<ParameterConditionSnapshot> parameterConditions;
    }

    @Data
    public static class ParameterConditionSnapshot extends OParameterConditionVersionDO {
    }

    @Data
    public static class ConditionSnapshot extends OConditionVersionDO {
        private String name;
        private String color;
    }
}
