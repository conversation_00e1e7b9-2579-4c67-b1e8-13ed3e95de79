package com.taobao.wireless.orange.oswitch.manager.release.model;

import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionVersionBO;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.ParameterVersionBO;
import com.taobao.wmcc.client.constants.BriefTaskStatus;
import lombok.Data;

import java.util.List;

@Data
public class ReleaseOrderBO extends OReleaseOrderDO {
    private String namespaceName;
    /**
     * 本次发布单涉及的参数变更
     */
    private List<ParameterVersionBO> parameterVersions;

    /**
     * 本次发布单涉及的条件变更
     */
    private List<ConditionVersionBO> conditionVersions;

    /**
     * 发布单状态列表（查询使用）
     */
    private List<ReleaseOrderStatus> statuses;

    /**
     * WMCC 任务 ID
     */
    private String agatewareTaskId;

    /**
     * WMCC 任务状态
     */
    private BriefTaskStatus agatewareTaskInfo;

    /**
     * 发布单阶段列表
     */
    private List<OReleaseOrderStageDO> releaseOrderStages;
}
