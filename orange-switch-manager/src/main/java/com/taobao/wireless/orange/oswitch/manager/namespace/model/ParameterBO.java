package com.taobao.wireless.orange.oswitch.manager.namespace.model;

import com.taobao.wireless.orange.oswitch.dal.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import lombok.Data;

import java.util.List;

@Data
public class ParameterBO extends OParameterDO {
    /**
     * 当前参数版本信息
     */
    private OParameterVersionDO parameterVersion;
    /**
     * 当前参数条件信息
     */
    private List<OParameterConditionVersionDO> parameterConditionVersions;
    /**
     * 发布中的发布单
     */
    private OReleaseOrderDO inPublishReleaseOrder;

    /**
     * 搜索关键字
     */
    private String keyword;
}
