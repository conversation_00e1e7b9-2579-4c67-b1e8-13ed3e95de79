package com.taobao.wireless.orange.oswitch.manager.release;

import com.alibaba.change.core2.hsf.pojo.ChangeEnv;
import com.alibaba.change.core2.hsf.pojo.ChangeResult;
import com.alibaba.change.core2.hsf.pojo.ChangeStartReqDTO;
import com.alibaba.change.core2.hsf.pojo.GrayStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.goc.changefree.builder.ChangeStartReqBuilder;
import com.alibaba.goc.changefree.model.ChangeCheckRes;
import com.alibaba.goc.changefree.model.GeneralChangeObject;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.common.constant.enums.OperationStatus;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.util.UserUtil;
import com.taobao.wireless.orange.external.changefree.ChangefreeService;
import com.taobao.wireless.orange.oswitch.dal.dao.OConditionDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OReleaseOrderOperationDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderOperationDO;
import com.taobao.wireless.orange.oswitch.manager.namespace.NamespaceAuditManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.ChangeFreeResult;
import com.taobao.wireless.orange.oswitch.manager.release.model.ChangefreeDiffInfoItemDTO;
import com.taobao.wireless.orange.publish.config.BetaConfigGenerator;
import com.taobao.wireless.orange.publish.config.FullReleaseConfigGenerator;
import com.taobao.wireless.orange.publish.config.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * changefree 服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChangefreeManager {

    private static final String RELEASE_ORDER_TITLE_TEMPLATE = "[ORANGE 配置%s发布]命名空间：%s；发布单：RO-%s（%s）";
    private static final String NOTIFY_URL_TEMPLATE = "https://%s/api/release-orders/%s/changefree/callback";
    private static final String DETAIL_URL_TEMPLATE = "https://%s/#/workspace/switch/release-orders/%s";
    private static final String EMPTY_JSON = "{}";
    private static final String PARAMETER_TYPE_DIFF = "DIFF";
    private static final String PARAMETER_NAME_EN = "Orange Parameter";
    private static final String PARAMETER_NAME_CN = "Orange 参数";
    private static final String CONDITION_NAME_EN = "Orange Condition";
    private static final String CONDITION_NAME_CN = "Orange 条件";

    @Autowired
    private ChangefreeService changefreeService;

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;

    @Value("${orange.console.pro.domain}")
    private String consoleDomain;

    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;

    @Autowired
    private BetaConfigGenerator betaConfigGenerator;

    @Autowired
    private NamespaceAuditManager namespaceAuditManager;

    public ChangeCheckRes check(OReleaseOrderDO releaseOrder, boolean forceNewCFCheck, Emergent emergent) {
        // 如果强制创建新 CF 单则重新生成 sourceOrderId, 如果只是准入校验，则从历史发布记录中取 sourceOrderId 重新校验
        String sourceOrderId = generateSourceOrderId(releaseOrder.getReleaseVersion(), forceNewCFCheck);

        GeneralChangeObject changeObject = generateChangeObject(releaseOrder);

        ONamespaceDO namespace = namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId());
        Map<String, Object> extraInfo = generateExtraInfo(releaseOrder, namespace, emergent);

        String title = RELEASE_ORDER_TITLE_TEMPLATE.formatted(Emergent.y.equals(emergent) ? "紧急" : "",
                namespace.getName(),
                releaseOrder.getReleaseVersion(),
                releaseOrder.getDescription());
        String detailUrl = getReleaseOrderUrl(releaseOrder.getReleaseVersion());
        String notifyUrl = NOTIFY_URL_TEMPLATE.formatted(consoleDomain, releaseOrder.getReleaseVersion());

        return changefreeService.check(sourceOrderId, changeObject, extraInfo, title, detailUrl, notifyUrl);
    }

    /**
     * 获取最近一次验证通过的changefree结果
     * 一个发布单可以提交多次发布申请
     *
     * @param releaseVersion
     * @return
     */
    public ChangeFreeResult getLatestPassedChangefreeResult(String releaseVersion) {
        return releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .orderByDesc(OReleaseOrderOperationDO::getId)
                .eq(OReleaseOrderOperationDO::getType, OperationType.APPLY_RELEASE)
                .eq(OReleaseOrderOperationDO::getStatus, OperationStatus.SUCCESS)
                .last("limit 1")
                .oneOpt()
                .map(operation -> JSON.parseObject(operation.getResult(), ChangeFreeResult.class))
                .orElse(null);
    }

    private Map<String, Object> generateExtraInfo(OReleaseOrderDO releaseOrder, ONamespaceDO namespace, Emergent emergent) {
        NamespaceIdNameRecord namespaceIdNameRecord = new NamespaceIdNameRecord(releaseOrder.getNamespaceId(), namespace.getName());

        // grayConfig 中包含了本次发布变更的参数和条件
        GrayConfig grayConfig = betaConfigGenerator.generate(namespaceIdNameRecord, releaseOrder.getReleaseVersion());

        if (grayConfig == null) {
            return null;
        }

        // fullReleaseConfig 中包含了本次发布变更前的参数和条件
        ReleaseConfig releaseConfig = fullReleaseConfigGenerator.generate(namespaceIdNameRecord, releaseOrder.getReleaseVersion());

        List<ChangefreeDiffInfoItemDTO> changeInfo = new ArrayList<>();
        // 添加参数 DIFF
        addParameterDiffInfo(changeInfo, grayConfig, releaseConfig);
        // 添加条件 DIFF
        addConditionDiffInfo(changeInfo, grayConfig, releaseConfig);

        // 审批加签人
        List<String> addSigners = namespaceAuditManager.getApprovers(namespace, emergent).stream().map(UserUtil::formatWorkerIdWithoutZero).toList();

        return Map.of("changeInfo", changeInfo, "addSigners", addSigners);
    }

    private void addParameterDiffInfo(List<ChangefreeDiffInfoItemDTO> changeInfo, GrayConfig grayConfig, ReleaseConfig releaseConfig) {
        ChangefreeDiffInfoItemDTO parameterChangeInfoItem = createDiffInfoItem(PARAMETER_NAME_EN, PARAMETER_NAME_CN);
        List<ChangefreeDiffInfoItemDTO.DiffData> parameterDiffData = new ArrayList<>();
        parameterChangeInfoItem.setData(parameterDiffData);
        changeInfo.add(parameterChangeInfoItem);

        Map<String, Parameter> oldParameterMap = getParameterMap(releaseConfig);
        GrayConfig.ReleaseOrder order = grayConfig.getOrders().getFirst();

        processOfflineParameters(parameterDiffData, order.getOfflineParameters(), oldParameterMap);

        processUpdatedParameters(parameterDiffData, order.getParameters(), oldParameterMap);
    }

    private void addConditionDiffInfo(List<ChangefreeDiffInfoItemDTO> changeInfo, GrayConfig grayConfig, ReleaseConfig releaseConfig) {
        if (CollectionUtils.isEmpty(grayConfig.getConditions())) {
            return;
        }

        ChangefreeDiffInfoItemDTO conditionChangeInfoItem = createDiffInfoItem(CONDITION_NAME_EN, CONDITION_NAME_CN);
        List<ChangefreeDiffInfoItemDTO.DiffData> conditionDiffData = new ArrayList<>();
        conditionChangeInfoItem.setData(conditionDiffData);
        changeInfo.add(conditionChangeInfoItem);

        Map<String, Condition> oldConditionMap = getConditionMap(releaseConfig);
        List<String> conditionIds = grayConfig.getConditions().stream()
                .map(Condition::getId)
                .toList();
        Map<String, String> conditionId2Name = conditionDAO.getConditionId2NameMap(conditionIds);

        grayConfig.getConditions().forEach(condition -> {
            ChangefreeDiffInfoItemDTO.DiffData diffData = createConditionDiffData(condition, oldConditionMap, conditionId2Name);
            conditionDiffData.add(diffData);
        });
    }

    private ChangefreeDiffInfoItemDTO createDiffInfoItem(String nameEn, String nameCn) {
        ChangefreeDiffInfoItemDTO item = new ChangefreeDiffInfoItemDTO();
        item.setType(PARAMETER_TYPE_DIFF);
        item.setNameEn(nameEn);
        item.setNameCn(nameCn);
        return item;
    }

    private Map<String, Parameter> getParameterMap(ReleaseConfig releaseConfig) {
        if (releaseConfig == null || CollectionUtils.isEmpty(releaseConfig.getParameters())) {
            return Collections.emptyMap();
        }
        return releaseConfig.getParameters().stream()
                .collect(Collectors.toMap(Parameter::getKey, Function.identity()));
    }

    private Map<String, Condition> getConditionMap(ReleaseConfig releaseConfig) {
        if (releaseConfig == null || CollectionUtils.isEmpty(releaseConfig.getConditions())) {
            return Collections.emptyMap();
        }
        return releaseConfig.getConditions().stream()
                .collect(Collectors.toMap(Condition::getId, Function.identity()));
    }

    private void processOfflineParameters(List<ChangefreeDiffInfoItemDTO.DiffData> diffDataList,
                                          List<String> offlineParameterKeys,
                                          Map<String, Parameter> oldParameterMap) {
        if (CollectionUtils.isEmpty(offlineParameterKeys)) {
            return;
        }

        offlineParameterKeys.forEach(parameterKey -> {
            ChangefreeDiffInfoItemDTO.DiffData diffData = new ChangefreeDiffInfoItemDTO.DiffData();
            diffData.setChangeTarget(parameterKey);
            diffData.setOldValue(getParameterJsonValue(oldParameterMap.get(parameterKey)));
            diffData.setNewValue(EMPTY_JSON);

            diffDataList.add(diffData);
        });
    }

    private void processUpdatedParameters(List<ChangefreeDiffInfoItemDTO.DiffData> diffDataList,
                                          List<Parameter> newParameters,
                                          Map<String, Parameter> oldParameterMap) {
        if (CollectionUtils.isEmpty(newParameters)) {
            return;
        }

        newParameters.forEach(newParameter -> {
            ChangefreeDiffInfoItemDTO.DiffData diffData = new ChangefreeDiffInfoItemDTO.DiffData();
            diffData.setChangeTarget(newParameter.getKey());
            diffData.setOldValue(getParameterJsonValue(oldParameterMap.get(newParameter.getKey())));
            diffData.setNewValue(JSON.toJSONString(newParameter));

            diffDataList.add(diffData);
        });
    }

    private ChangefreeDiffInfoItemDTO.DiffData createConditionDiffData(Condition condition,
                                                                       Map<String, Condition> oldConditionMap,
                                                                       Map<String, String> conditionId2Name) {
        ChangefreeDiffInfoItemDTO.DiffData diffData = new ChangefreeDiffInfoItemDTO.DiffData();
        String conditionName = conditionId2Name.get(condition.getId());
        diffData.setChangeTarget(conditionName + "-" + condition.getId());
        diffData.setNewValue(JSON.toJSONString(condition));

        Condition oldCondition = oldConditionMap.get(condition.getId());
        diffData.setOldValue(oldCondition == null ? EMPTY_JSON : JSON.toJSONString(oldCondition));

        return diffData;
    }

    private String getParameterJsonValue(Parameter parameter) {
        return parameter == null ? EMPTY_JSON : JSON.toJSONString(parameter);
    }

    private String generateSourceOrderId(String releaseVersion) {
        return releaseVersion + "_" + System.currentTimeMillis();
    }

    private String generateSourceOrderId(String releaseVersion, boolean forceNewCFCheck) {
        if (!forceNewCFCheck) {
            ChangeFreeResult result = getLatestPassedChangefreeResult(releaseVersion);
            if (result != null) {
                return result.getSourceOrderId();
            }
        }

        return generateSourceOrderId(releaseVersion);
    }

    private GeneralChangeObject generateChangeObject(OReleaseOrderDO releaseOrder) {
        GeneralChangeObject changeObject = new GeneralChangeObject();
        changeObject.setAppKey(releaseOrder.getAppKey());
        return changeObject;
    }

    private String getReleaseOrderUrl(String releaseVersion) {
        return DETAIL_URL_TEMPLATE.formatted(consoleDomain, releaseVersion);
    }

    /**
     * 开始变更
     *
     * @param releaseOrder
     * @return
     */
    public void start(OReleaseOrderDO releaseOrder) {
        try {
            changefreeService.start(assembleChangeStartReq(releaseOrder));
        } catch (Exception e) {
            // 执行单报错不阻断发布
            log.error("Start changefree failed, releaseVersion: %s".formatted(releaseOrder.getReleaseVersion()), e);
        }
    }

    /**
     * 开始并结束变更，对于无过程的变更
     *
     * @return
     */
    public void startAndEnd(OReleaseOrderDO releaseOrder) {
        try {
            changefreeService.start(assembleChangeStartReq(releaseOrder));
        } catch (Exception e) {
            // 执行单报错不阻断发布
            log.error("Start changefree failed, releaseVersion: %s".formatted(releaseOrder.getReleaseVersion()), e);
        }
    }

    /**
     * 结束变更
     *
     * @param releaseVersion
     * @param changeResult
     * @return
     */
    public void end(String releaseVersion, ChangeResult changeResult) {
        try {
            changefreeService.end(releaseVersion, changeResult);
        } catch (Exception e) {
            // 执行单报错不阻断发布
            log.error("End changefree failed, releaseVersion: %s, changeResult: %s".formatted(releaseVersion, changeResult.name()), e);
        }
    }

    private String getReleaseOrderTitle(OReleaseOrderDO releaseOrder, String namespaceName) {
        return RELEASE_ORDER_TITLE_TEMPLATE.formatted(Emergent.y.equals(releaseOrder.getIsEmergent()) ? "紧急" : "",
                namespaceName,
                releaseOrder.getReleaseVersion(),
                releaseOrder.getDescription());
    }

    private ChangeStartReqDTO assembleChangeStartReq(OReleaseOrderDO releaseOrder) {
        ONamespaceDO namespace = namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId());

        // 针对暂不具备灰度观测能力的变更系统，gray_strategy统一填为： {"batchSize": 1}
        GrayStrategy grayStrategy = new GrayStrategy();
        grayStrategy.setBatchSize(1.0);

        final ChangeStartReqDTO changeStartReq = ChangeStartReqBuilder.aChangeCheckReq()
                .sourceOrderId(releaseOrder.getReleaseVersion())
                .changeTitle(getReleaseOrderTitle(releaseOrder, namespace.getName()))
                .grayStrategy(grayStrategy)
                .actionType("end")
                .changeEnv(ChangeEnv.PUBLISH)
                .changeEndTime(System.currentTimeMillis())
                .build();

        // 为了展示 appKey 影响面，需要指定 primary，只能通过这种方式赋值
        Map<String, String> changeObject = new HashMap<>() {{
            put("appKey", releaseOrder.getAppKey());
            put("primary", "appKey");
        }};
        changeStartReq.setChangeObject(JSON.toJSONString(changeObject));
        changeStartReq.setChangeObjectType("GENERAL");

        return changeStartReq;
    }
}