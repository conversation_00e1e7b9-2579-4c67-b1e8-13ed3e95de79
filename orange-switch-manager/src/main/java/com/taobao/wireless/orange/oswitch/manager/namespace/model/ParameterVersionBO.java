package com.taobao.wireless.orange.oswitch.manager.namespace.model;

import com.taobao.wireless.orange.oswitch.dal.entity.OParameterVersionDO;
import lombok.Data;

import java.util.List;

@Data
public class ParameterVersionBO extends OParameterVersionDO {
    /**
     * 参数条件名顺序（由于新增条件提交时还没有条件ID）
     */
    private List<String> conditionNamesOrder;

    /**
     * 关联的参数基本(非版本化)信息
     */
    private ParameterBO parameterBO;

    /**
     * 涉及的参数条件值变更
     */
    private List<ParameterConditionVersionBO> parameterConditionVersions;
}
