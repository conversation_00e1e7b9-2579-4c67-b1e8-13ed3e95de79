package com.taobao.wireless.orange.oswitch.manager.namespace;

import com.taobao.wireless.orange.common.constant.enums.NamespaceType;
import com.taobao.wireless.orange.external.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户namespace访问记录管理器
 * 统一管理用户的常用namespace（基于访问频次）和最近访问namespace（基于访问时间）
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserNamespaceManager {

    @Autowired
    private RedisService redisService;

    /**
     * 用户常用namespace的Redis key前缀（基于访问频次的有序集合）
     */
    private static final String USER_FREQUENT_NAMESPACES_KEY_PREFIX = "user_frequent_ns";

    /**
     * 用户最近访问namespace的Redis key前缀（基于访问时间的列表）
     */
    private static final String USER_RECENT_NAMESPACES_KEY_PREFIX = "user_recent_ns";

    /**
     * 常用namespace默认过期时间：60天
     */
    private static final int FREQUENT_EXPIRE_SECONDS = 2 * 30 * 24 * 60 * 60;

    /**
     * 最近访问namespace默认过期时间：30天
     */
    private static final int RECENT_EXPIRE_SECONDS = 30 * 24 * 60 * 60;

    /**
     * 最大保存的最近访问记录数量
     */
    private static final int MAX_RECENT_COUNT = 50;

    /**
     * 异步记录用户namespace访问情况
     * 同时更新常用记录（频次）和最近访问记录（时间）
     *
     * @param type        namespace类型
     * @param workerId    用户工号
     * @param namespaceId namespace ID
     */
    @Async
    public void recordUserNamespaceAccess(NamespaceType type, String workerId, String namespaceId) {
        if (StringUtils.isBlank(workerId) || StringUtils.isBlank(namespaceId)) {
            return;
        }

        // 并行记录常用和最近访问
        recordFrequentAccess(type, workerId, namespaceId);
        recordRecentAccess(type, workerId, namespaceId);
    }

    /**
     * 记录常用namespace访问（基于频次）
     */
    private void recordFrequentAccess(NamespaceType type, String workerId, String namespaceId) {
        try {
            String key = getFrequentNamespacesKey(type, workerId);
            redisService.zincrby(key, 1.0, namespaceId);
            redisService.expire(key, FREQUENT_EXPIRE_SECONDS);
        } catch (Exception e) {
            log.error("Failed to record frequent namespace access: type={}, workerId={}, namespaceId={}",
                    type.name(), workerId, namespaceId, e);
        }
    }

    /**
     * 记录最近访问namespace（基于时间）
     */
    private void recordRecentAccess(NamespaceType type, String workerId, String namespaceId) {
        try {
            String key = getRecentNamespacesKey(type, workerId);

            // 先移除已存在的相同namespaceId（避免重复）
            redisService.lrem(key, 0, namespaceId);

            // 将新的namespaceId推入列表头部
            redisService.lpush(key, namespaceId);

            // 保持列表长度不超过最大限制
            redisService.ltrim(key, 0, MAX_RECENT_COUNT - 1);

            // 设置过期时间
            redisService.expire(key, RECENT_EXPIRE_SECONDS);
        } catch (Exception e) {
            log.error("Failed to record recent namespace access: type={}, workerId={}, namespaceId={}",
                    type.name(), workerId, namespaceId, e);
        }
    }

    /**
     * 获取用户常用的namespace列表（按使用频次降序）
     *
     * @param type     namespace类型
     * @param workerId 用户工号
     * @param limit    返回数量限制
     * @return namespace ID列表
     */
    public List<String> getUserFrequentNamespaces(NamespaceType type, String workerId, int limit) {
        if (StringUtils.isBlank(workerId) || limit <= 0) {
            return new ArrayList<>();
        }

        try {
            String key = getFrequentNamespacesKey(type, workerId);
            return redisService.zrevrange(key, 0, limit - 1).stream().toList();
        } catch (Exception e) {
            log.error("Failed to get user frequent namespaces: type={}, workerId={}, limit={}",
                    type.name(), workerId, limit, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户最近访问的namespace列表（按访问时间倒序）
     *
     * @param type     namespace类型
     * @param workerId 用户工号
     * @param limit    返回数量限制
     * @return namespace ID列表
     */
    public List<String> getUserRecentNamespaces(NamespaceType type, String workerId, int limit) {
        if (StringUtils.isBlank(workerId) || limit <= 0) {
            return new ArrayList<>();
        }

        try {
            String key = getRecentNamespacesKey(type, workerId);
            return redisService.lrange(key, 0, limit - 1);
        } catch (Exception e) {
            log.error("Failed to get user recent namespaces: type={}, workerId={}, limit={}",
                    type.name(), workerId, limit, e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建用户常用namespace的Redis key
     *
     * @param type     namespace类型
     * @param workerId 用户工号
     * @return Redis key
     */
    private String getFrequentNamespacesKey(NamespaceType type, String workerId) {
        return String.format("%s:%s:%s", USER_FREQUENT_NAMESPACES_KEY_PREFIX, type.name().toLowerCase(), workerId);
    }

    /**
     * 构建用户最近访问namespace的Redis key
     *
     * @param type     namespace类型
     * @param workerId 用户工号
     * @return Redis key
     */
    private String getRecentNamespacesKey(NamespaceType type, String workerId) {
        return String.format("%s:%s:%s", USER_RECENT_NAMESPACES_KEY_PREFIX, type.name().toLowerCase(), workerId);
    }
}
