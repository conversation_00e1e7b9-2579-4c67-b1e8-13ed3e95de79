package com.taobao.wireless.orange.oswitch.manager.release.operation;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.oswitch.dal.entity.OReleaseOrderStageDO;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationAdditionalData;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;

/**
 * 发布操作策略接口
 */
public interface OperationStrategy<T extends OperationAdditionalData, R extends OperationResult> {

    /**
     * 获取操作类型
     */
    OperationType getOperationType();

    /**
     * 是否需要锁
     *
     * @return
     */
    boolean needLock();

    /**
     * 是否需要检查 ChangeFree
     *
     * @return
     */
    boolean needCheckChangefree();

    /**
     * 权限校验
     *
     * @param context 操作上下文
     */
    void validatePermission(OperationContext<T, R> context);

    /**
     * 入参校验
     *
     * @param context 操作上下文
     */
    void validateParameters(OperationContext<T, R> context);

    /**
     * 执行具体的业务逻辑
     *
     * @param context 操作上下文
     */
    void executeOperation(OperationContext<T, R> context);

    /**
     * 获取目标状态（用于状态更新）
     */
    ReleaseOrderStatus getTargetReleaseOrderStatus(OperationContext<T, R> context);

    /**
     * 获取目标阶段状态（用于阶段状态更新）
     *
     * @param context
     * @return
     */
    OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext<T, R> context);
}