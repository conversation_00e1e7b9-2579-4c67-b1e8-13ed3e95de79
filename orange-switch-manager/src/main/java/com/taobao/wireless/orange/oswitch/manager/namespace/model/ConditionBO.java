package com.taobao.wireless.orange.oswitch.manager.namespace.model;

import com.taobao.wireless.orange.oswitch.dal.entity.OConditionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OConditionVersionDO;
import lombok.Data;

import java.util.List;

@Data
public class ConditionBO extends OConditionDO {
    /**
     * 条件版本信息(优先使用线上版本，没有则使用草稿版本)
     */
    private OConditionVersionDO conditionVersion;
    /**
     * 引用了该条件的参数列表
     */
    private List<ParameterConditionVersionBO> relatedParameters;
}
