package com.taobao.wireless.orange.oswitch.manager.release.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GrayPublishContent {
    /**
     * 发布单版本号
     */
    private Long version;
    /**
     * 创建对应发布单产生的变更版本（changeVersion)
     */
    private Long changeVersion;
    private String resourceId;
    private String name;
    private String cdn;
}
