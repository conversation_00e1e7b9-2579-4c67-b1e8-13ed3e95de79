package com.taobao.wireless.orange.oswitch.manager.namespace.version;

import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.ConfigType;
import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceVersionResourceDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionResourceDO;
import com.taobao.wireless.orange.publish.config.ConfigGenerateService;
import com.taobao.wireless.orange.publish.resource.model.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NamespaceVersionResourceManager {

    @Autowired
    private ONamespaceVersionResourceDAO namespaceVersionResourceDAO;

    @Autowired
    private ConfigGenerateService configGenerateService;

    /**
     * 生成当前 namespaceVersionDO 记录对应 configType 的配置文件
     *
     * @param namespaceVersionDO
     * @param configType
     */
    public void createNamespaceVersionResource(ONamespaceVersionDO namespaceVersionDO, ConfigType configType) {
        ONamespaceVersionResourceDO versionContentDO = switch (configType) {
            case RELEASE -> generateReleaseResource(namespaceVersionDO);
            case GRAY -> generateGrayResource(namespaceVersionDO);
            case EXPERIMENT, BETA -> null;
        };

        // 将历史生效配置失效
        namespaceVersionResourceDAO.lambdaUpdate()
                .eq(ONamespaceVersionResourceDO::getNamespaceId, namespaceVersionDO.getNamespaceId())
                .eq(ONamespaceVersionResourceDO::getType, configType)
                .eq(ONamespaceVersionResourceDO::getIsAvailable, Available.y)
                .set(ONamespaceVersionResourceDO::getIsAvailable, Available.n)
                .update();

        // 当没有进行中灰度单或者配置还没有正式发布过，此时 versionContentDO 会为空
        if (versionContentDO != null) {
            namespaceVersionResourceDAO.save(versionContentDO);
        }
    }

    /**
     * 生成当前命名空间 changeVersion 版本对应产生的正式配置
     *
     * @param namespaceVersion
     * @return
     */
    private ONamespaceVersionResourceDO generateReleaseResource(ONamespaceVersionDO namespaceVersion) {
        Resource resource = configGenerateService.generate(namespaceVersion.getNamespaceId(), ResourceType.FULL_RELEASE_CONFIG, null);
        if (resource == null) {
            return null;
        }

        var versionContentDO = createBaseVersionResource(namespaceVersion);
        versionContentDO.setType(ConfigType.RELEASE);
        versionContentDO.setResourceId(resource.getResourceId());
        return versionContentDO;
    }

    /**
     * 生成当前命名空间 changeVersion 版本对应产生的灰度配置
     *
     * @param namespaceVersion
     * @return
     */
    private ONamespaceVersionResourceDO generateGrayResource(ONamespaceVersionDO namespaceVersion) {
        Resource resource = configGenerateService.generate(namespaceVersion.getNamespaceId(), ResourceType.FULL_GRAY_CONFIG, null);
        if (resource == null) {
            return null;
        }

        var versionContentDO = createBaseVersionResource(namespaceVersion);
        versionContentDO.setType(ConfigType.GRAY);
        versionContentDO.setResourceId(resource.getResourceId());
        return versionContentDO;
    }

    private ONamespaceVersionResourceDO createBaseVersionResource(ONamespaceVersionDO namespaceVersion) {
        ONamespaceVersionResourceDO versionContentDO = new ONamespaceVersionResourceDO();
        versionContentDO.setNamespaceId(namespaceVersion.getNamespaceId());
        versionContentDO.setNamespaceChangeVersion(namespaceVersion.getNamespaceChangeVersion());
        versionContentDO.setAppKey(namespaceVersion.getAppKey());
        versionContentDO.setIsAvailable(Available.y);
        return versionContentDO;
    }
}
