package com.taobao.wireless.orange.oswitch.manager.release.operation.impl;

import com.alibaba.change.core2.hsf.pojo.ChangeResult;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.external.oss.OssService;
import com.taobao.wireless.orange.oswitch.dal.dao.*;
import com.taobao.wireless.orange.oswitch.dal.entity.*;
import com.taobao.wireless.orange.oswitch.manager.namespace.NamespaceManager;
import com.taobao.wireless.orange.oswitch.manager.namespace.model.NamespaceSnapshot;
import com.taobao.wireless.orange.oswitch.manager.namespace.version.NamespaceVersionManager;
import com.taobao.wireless.orange.oswitch.manager.release.ChangefreeManager;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationAdditionalData;
import com.taobao.wireless.orange.oswitch.manager.release.model.OperationResult;
import com.taobao.wireless.orange.oswitch.manager.release.operation.AbstractOperationTemplate;
import com.taobao.wireless.orange.oswitch.manager.release.operation.OperationContext;
import com.taobao.wireless.orange.oswitch.manager.task.TaskManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType.FINISH_IMPACT_RELEASE;
import static com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType.FINISH_NO_IMPACT_RELEASE;

/**
 * 发布操作策略实现
 */
@Component
public class ReleaseStrategy extends AbstractOperationTemplate<OperationAdditionalData, OperationResult> {

    @Autowired
    private TaskManager taskManager;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private NamespaceManager namespaceManager;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    private OConditionDAO conditionDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private OssService ossService;
    @Autowired
    private ChangefreeManager changefreeManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.RELEASE;
    }

    @Override
    public boolean needLock() {
        return true;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void executeOperation(OperationContext context) {
        String releaseVersion = context.getReleaseVersion();

        // 上线参数版本
        onlineParameterVersion(releaseVersion);

        // 上线条件版本
        onlineConditionVersion(releaseVersion);

        // 上线参数条件版本
        onlineParameterConditionVersion(releaseVersion);

        // namespace changeVersion 递增
        OReleaseOrderDO releaseOrder = context.getReleaseOrder();
        NamespaceVersionChangeType changeType = releaseOrder.getReleaseType().isImpact() ? FINISH_IMPACT_RELEASE : FINISH_NO_IMPACT_RELEASE;
        String namespaceVersion = namespaceVersionManager.createNamespaceVersion(releaseOrder, changeType);

        // 生成发布后的 namespace 快照(用于发布单详情页追溯历史变更)
        createAndUploadOnlineSnapshot(releaseOrder.getNamespaceId(), namespaceVersion, releaseVersion);

        // 如果没有百分比发布过
        if (releaseOrder.getGrayRatio() == null || releaseOrder.getGrayRatio() == 0) {
            changefreeManager.startAndEnd(releaseOrder);
        } else {
            // 如果百分比发布过，说明有 cf 执行单，需要关闭
            changefreeManager.end(releaseOrder.getReleaseVersion(), ChangeResult.SUCCESS);
        }
    }

    @Override
    public ReleaseOrderStatus getTargetReleaseOrderStatus(OperationContext context) {
        return ReleaseOrderStatus.RELEASED;
    }

    @Override
    public OReleaseOrderStageDO getTargetReleaseOrderStage(OperationContext context) {
        return OReleaseOrderStageDO.builder()
                .type(StageType.RELEASE)
                .status(StageStatus.SUCCESS)
                .build();
    }

    @Override
    public void handleTask(OperationContext context) {
        taskManager.completeReleaseOrderTask(context.getReleaseVersion(), TaskType.RELEASE);
    }

    @Override
    public boolean needCheckChangefree() {
        return true;
    }

    private void onlineParameterVersion(String releaseVersion) {
        List<OParameterVersionDO> parameters = parameterVersionDAO
                .lambdaQuery()
                .select(OParameterVersionDO::getParameterId, OParameterVersionDO::getChangeType)
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(parameters)) {
            return;
        }

        // 本次发布单涉及的参数
        List<String> parameterIds = parameters.stream()
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());

        // 将历史发布版本标记为过期
        parameterVersionDAO.lambdaUpdate()
                .in(OParameterVersionDO::getParameterId, parameterIds)
                .eq(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OParameterVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        // 将本次发布版本标记为发布
        parameterVersionDAO.lambdaUpdate()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterVersionDO::getStatus, VersionStatus.RELEASED)
                .update();

        // 将删除的参数对象状态置为删除
        List<String> deleteParameterIds = parameters.stream()
                .filter(p -> ChangeType.DELETE.equals(p.getChangeType()))
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteParameterIds)) {
            parameterDAO.lambdaUpdate()
                    .in(OParameterDO::getParameterId, deleteParameterIds)
                    .set(OParameterDO::getStatus, ParameterStatus.INVALID)
                    .update();
        }

        // 将新增的参数对象状态置从初始改为已发布
        parameterDAO.lambdaUpdate()
                .in(OParameterDO::getParameterId, parameterIds)
                .eq(OParameterDO::getStatus, ParameterStatus.INIT)
                .set(OParameterDO::getStatus, ParameterStatus.ONLINE)
                .update();
    }

    private void onlineConditionVersion(String releaseVersion) {
        List<OConditionVersionDO> conditions = conditionVersionDAO
                .lambdaQuery()
                .select(OConditionVersionDO::getConditionId, OConditionVersionDO::getChangeType)
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }

        List<String> conditionIds = conditions.stream()
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());

        // 将历史发布版本标记为过期
        conditionVersionDAO.lambdaUpdate()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .eq(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .set(OConditionVersionDO::getStatus, VersionStatus.OUTDATED)
                .update();

        // 将本次发布版本标记为发布
        conditionVersionDAO.lambdaUpdate()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .update();

        // 将删除的条件对象状态置为删除
        List<String> deleteConditionIds = conditions.stream()
                .filter(c -> ChangeType.DELETE.equals(c.getChangeType()))
                .map(OConditionVersionDO::getConditionId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteConditionIds)) {
            conditionDAO.lambdaUpdate()
                    .in(OConditionDO::getConditionId, deleteConditionIds)
                    .set(OConditionDO::getStatus, ConditionStatus.INVALID)
                    .update();
        }

        // 将新增的条件对象状态置为已发布
        conditionDAO.lambdaUpdate()
                .in(OConditionDO::getConditionId, conditionIds)
                .eq(OConditionDO::getStatus, ConditionStatus.INIT)
                .set(OConditionDO::getStatus, ConditionStatus.ONLINE)
                .update();
    }

    private void onlineParameterConditionVersion(String releaseVersion) {
        var parameterConditionVersions = parameterConditionVersionDAO.lambdaQuery()
                .select(OParameterConditionVersionDO::getParameterId, OParameterConditionVersionDO::getConditionId)
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        if (CollectionUtils.isEmpty(parameterConditionVersions)) {
            return;
        }

        // 将历史发布版本标记为过期
        var updateChainWrapper = parameterConditionVersionDAO.lambdaUpdate();
        for (var parameterCondition : parameterConditionVersions) {
            updateChainWrapper.or(i -> i
                    .eq(OParameterConditionVersionDO::getParameterId, parameterCondition.getParameterId())
                    .eq(OParameterConditionVersionDO::getConditionId, parameterCondition.getConditionId())
                    .eq(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED));
        }
        updateChainWrapper.set(OParameterConditionVersionDO::getStatus, VersionStatus.OUTDATED).update();

        // 将本次发布版本标记为发布
        parameterConditionVersionDAO.lambdaUpdate()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion)
                .set(OParameterConditionVersionDO::getStatus, VersionStatus.RELEASED)
                .update();
    }

    /**
     * 生成发布后的 namespace 快照(用于发布单详情页追溯历史变更)
     *
     * @param namespaceId
     * @param namespaceVersion
     * @param releaseVersion
     */
    private void createAndUploadOnlineSnapshot(String namespaceId, String namespaceVersion, String releaseVersion) {
        NamespaceSnapshot namespaceSnapshot = namespaceManager.createOnlineSnapshot(namespaceId);
        namespaceSnapshot.setReleaseVersion(releaseVersion);
        namespaceSnapshot.setGmtCreate(new Date());
        namespaceSnapshot.setNamespaceVersion(namespaceVersion);
        ossService.uploadData(namespaceSnapshot.serialize(), String.format("SNAPSHOT/NS_%s.json", namespaceVersion));
    }
}