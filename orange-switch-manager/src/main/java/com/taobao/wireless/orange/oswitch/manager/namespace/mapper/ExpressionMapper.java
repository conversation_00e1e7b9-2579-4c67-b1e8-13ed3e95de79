package com.taobao.wireless.orange.oswitch.manager.namespace.mapper;

import com.taobao.wireless.orange.oswitch.manager.namespace.model.ConditionExpressionBO;
import com.taobao.wireless.orange.common.constant.OperatorConstants;
import com.taobao.wireless.tiga.release.expression.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MapStruct映射器，用于表达式相关的对象转换
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Mapper(componentModel = "spring")
public interface ExpressionMapper {

    /**
     * 将ConditionExpressionBO转换为LogicExpression
     * 递归处理不同类型的表达式
     *
     * @param conditionExpressionBO 条件表达式业务对象
     * @return LogicExpression对象
     */
    default LogicExpression toLogicExpression(ConditionExpressionBO conditionExpressionBO) {
        if (conditionExpressionBO == null) {
            return null;
        }

        String operator = conditionExpressionBO.getOperator();
        if (StringUtils.isBlank(operator)) {
            throw new IllegalArgumentException("Operator cannot be null or empty");
        }

        // 处理逻辑操作符 (AND, OR)
        if (OperatorConstants.AND.equalsIgnoreCase(operator) || OperatorConstants.OR.equalsIgnoreCase(operator)) {
            return createLogicalExpression(conditionExpressionBO);
        }

        // 处理 NOT 操作符
        if (OperatorConstants.NOT.equalsIgnoreCase(operator)) {
            return createNotExpression(conditionExpressionBO);
        }

        // 处理叶子节点表达式（需要key和value）
        String key = conditionExpressionBO.getKey();
        String value = conditionExpressionBO.getValue();

        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("Key cannot be null or empty for leaf expression");
        }

        // 处理比较操作符 (>, <, >=, <=)
        if (isComparisonOperator(operator)) {
            return createComparisonExpression(key, operator, value);
        }

        // 处理前缀匹配操作符 (~=, !~)
        if (isPrefixMatchOperator(operator)) {
            return createPrefixMatchExpression(key, operator, value);
        }

        // 处理等式操作符 (==, !=, =)
        if (isEqualityOperator(operator)) {
            return createEqualityExpression(key, operator, value);
        }

        throw new UnsupportedOperationException("Unsupported operator: " + operator);
    }

    /**
     * 将ConditionExpressionBO列表转换为LogicExpression列表
     *
     * @param conditionExpressionBOList 条件表达式业务对象列表
     * @return LogicExpression对象列表
     */
    default List<LogicExpression> toLogicExpressionList(List<ConditionExpressionBO> conditionExpressionBOList) {
        if (conditionExpressionBOList == null) {
            return null;
        }

        return conditionExpressionBOList.stream()
                .map(this::toLogicExpression)
                .collect(Collectors.toList());
    }

    /**
     * 判断是否为比较操作符
     */
    default boolean isComparisonOperator(String operator) {
        return Arrays.stream(ComparisonOperator.values()).anyMatch(o -> o.getValue().equals(operator));
    }

    /**
     * 判断是否为前缀匹配操作符
     */
    default boolean isPrefixMatchOperator(String operator) {
        return Arrays.stream(PrefixMatchOperator.values()).anyMatch(o -> o.getValue().equals(operator));
    }

    /**
     * 判断是否为等式操作符
     */
    default boolean isEqualityOperator(String operator) {
        return Arrays.stream(EqualityOperator.values()).anyMatch(o -> o.getValue().equals(operator));
    }

    /**
     * 创建逻辑表达式
     */
    default LogicExpression createLogicalExpression(ConditionExpressionBO bo) {
        if (CollectionUtils.isEmpty(bo.getChildren())) {
            throw new IllegalArgumentException("Logical expression must have children");
        }

        List<LogicExpression> children = toLogicExpressionList(bo.getChildren());
        LogicalOperator logicalOperator = getLogicalOperator(bo.getOperator());

        return new LogicalExpressionNode(logicalOperator, children);
    }

    /**
     * 创建NOT表达式
     */
    default LogicExpression createNotExpression(ConditionExpressionBO bo) {
        if (CollectionUtils.isEmpty(bo.getChildren()) || bo.getChildren().size() != 1) {
            throw new IllegalArgumentException("NOT expression must have exactly one child");
        }

        LogicExpression child = toLogicExpression(bo.getChildren().getFirst());
        return new NotExpressionNode(child);
    }

    /**
     * 创建比较表达式
     */
    default LogicExpression createComparisonExpression(String key, String operator, String value) {
        Field field = getField(key);
        ComparisonOperator comparisonOperator = getComparisonOperator(operator);

        return new ComparisonExpressionNode(field, comparisonOperator, value);
    }

    /**
     * 创建前缀匹配表达式
     */
    default LogicExpression createPrefixMatchExpression(String key, String operator, String value) {
        Field field = getField(key);
        PrefixMatchOperator prefixMatchOperator = getPrefixMatchOperator(operator);

        return new PrefixMatchExpressionNode(field, prefixMatchOperator, value);
    }

    /**
     * 创建等式表达式
     */
    default LogicExpression createEqualityExpression(String key, String operator, String value) {
        Field field = getField(key);
        EqualityOperator equalityOperator = getEqualityOperator(operator);

        return new EqualityExpressionNode(field, equalityOperator, value);
    }

    /**
     * 根据字符串获取LogicalOperator枚举
     */
    default LogicalOperator getLogicalOperator(String operator) {
        return switch (operator.toUpperCase()) {
            case OperatorConstants.AND -> LogicalOperator.AND;
            case OperatorConstants.OR -> LogicalOperator.OR;
            default -> throw new IllegalArgumentException("Unknown logical operator: " + operator);
        };
    }

    /**
     * 根据字符串获取ComparisonOperator枚举
     */
    default ComparisonOperator getComparisonOperator(String operator) {
        return ComparisonOperator.fromString(operator);
    }

    /**
     * 根据字符串获取PrefixMatchOperator枚举
     */
    default PrefixMatchOperator getPrefixMatchOperator(String operator) {
        return PrefixMatchOperator.fromString(operator);
    }

    /**
     * 根据字符串获取EqualityOperator枚举
     */
    default EqualityOperator getEqualityOperator(String operator) {
        return EqualityOperator.fromString(operator);
    }

    /**
     * 根据字符串获取Field枚举
     */
    default Field getField(String key) {
        if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("Field key cannot be null or empty");
        }

        // 根据key字符串映射到相应的Field枚举
        // 如果没有匹配的预定义字段，可以考虑使用一个通用字段或抛出异常
        return switch (key.toLowerCase()) {
            case "app_ver" -> Field.APP_VERSION;
            case "os_ver" -> Field.OS_VERSION;
            case "m_brand" -> Field.BRAND;
            case "m_model" -> Field.MODEL;
            default -> throw new IllegalArgumentException("Unknown field: " + key);
        };
    }
}
