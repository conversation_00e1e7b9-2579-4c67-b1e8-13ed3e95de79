package com.taobao.wireless.orange.common.model;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Pagination {
    @Min(value = 1, message = "页码最小值为1")
    private Integer pageNum;
    @Min(value = 1, message = "每页最小值为1")
    @Max(value = 100, message = "每页最大值为100")
    private Integer pageSize;
}
