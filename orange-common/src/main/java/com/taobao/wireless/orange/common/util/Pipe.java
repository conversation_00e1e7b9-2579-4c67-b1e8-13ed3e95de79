package com.taobao.wireless.orange.common.util;

import java.util.function.Function;
import java.util.function.Consumer;

public class Pipe<T> {
    private final T value;

    private Pipe(T value) {
        this.value = value;
    }

    public static <T> Pipe<T> of(T value) {
        return new Pipe<>(value);
    }

    public <R> Pipe<R> map(Function<? super T, ? extends R> mapper) {
        return new Pipe<>(mapper.apply(value));
    }

    public Pipe<T> apply(Consumer<? super T> action) {
        action.accept(value);
        return this;
    }

    public T get() {
        return value;
    }
}
