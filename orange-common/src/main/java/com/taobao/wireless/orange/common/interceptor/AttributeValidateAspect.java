package com.taobao.wireless.orange.common.interceptor;


import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import jakarta.validation.executable.ExecutableValidator;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.hibernate.validator.messageinterpolation.ParameterMessageInterpolator;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;

/**
 * 入参对象校验过滤器
 *
 * <AUTHOR>
 */
@Aspect
@Order(15)
@Component
public class AttributeValidateAspect {
    /**
     * 应用单例校验器-由工厂获取
     */
    private static final Validator VALIDATOR;
    /**
     * 校验器工厂实例
     */
    private static final ValidatorFactory VALIDATOR_FACTORY;

    static {
        VALIDATOR_FACTORY = Validation.byDefaultProvider().configure().messageInterpolator(new ParameterMessageInterpolator()).buildValidatorFactory();
        VALIDATOR = VALIDATOR_FACTORY.getValidator();

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (VALIDATOR_FACTORY != null) {
                VALIDATOR_FACTORY.close();
            }
        }));
    }

    @Before("@annotation(com.taobao.wireless.orange.common.annotation.AttributeValidate))")
    public void before(JoinPoint point) throws SecurityException {
        Object target = point.getThis();
        Object[] args = point.getArgs();
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        Set<ConstraintViolation<Object>> violationSet = validateAttribute(target, method, args);
        checkConstraintViolation(violationSet);
    }

    /**
     * 检查违反约束对象Set,并将校验信息封装为异常抛出
     *
     * @param violationSet 违反约束对象Set
     * @throws CommonException 参数无效异常
     */
    private void checkConstraintViolation(Set<ConstraintViolation<Object>> violationSet) {
        if (CollectionUtils.isNotEmpty(violationSet)) {
            StringBuilder sb = new StringBuilder();
            for (ConstraintViolation<Object> violation : violationSet) {
                sb.append('[').append(violation.getMessage()).append(']');
            }
            throw new CommonException(ExceptionEnum.PARAM_INVALID, sb.toString());
        }
    }

    /**
     * 校验所有参数对象的属性
     * 1.处理所有参数对象,如果参数为空则执行检查方法参数对象
     * 2.如果参数是list，则判断List是否有元素，无元素执行检查方法参数对象，有元素则循环获取每一个对象再校验其属性值
     * 3.其他对象将参数转换为对应的对象后校验对象的属性
     *
     * @param target 调用目标对象
     * @param method 调用方法
     * @param args   参数对象列表
     * @return 违反约束对象Set
     */
    private Set<ConstraintViolation<Object>> validateAttribute(Object target, Method method, Object[] args) {
        Set<ConstraintViolation<Object>> resultSet = new HashSet<>();

        if (args == null || args.length == 0) {
            return validatorMethodParam(target, method, args);
        }

        for (Object arg : args) {
            if (arg == null) {
                resultSet.addAll(validatorMethodParam(target, method, args));
            } else if (arg instanceof Collection) {
                if (CollectionUtils.isEmpty((Collection<?>) arg)) {
                    resultSet.addAll(validatorMethodParam(target, method, args));
                } else {
                    for (Object obj : (Collection<?>) arg) {
                        if (obj != null) {
                            resultSet.addAll(VALIDATOR.validate(obj));
                        }
                    }
                }
            } else {
                resultSet.addAll(VALIDATOR.validate(arg));
            }
        }
        return resultSet;
    }

    /**
     * 检查方法参数对象，如果参数上有校验注解则检查
     *
     * @param target 调用目标对象
     * @param method 调用方法
     * @param args   参数对象列表
     * @return 违反约束对象Set
     */
    private Set<ConstraintViolation<Object>> validatorMethodParam(Object target, Method method, Object[] args) {
        ExecutableValidator validatorParam = VALIDATOR.forExecutables();
        return validatorParam.validateParameters(target, method, args);
    }
}
