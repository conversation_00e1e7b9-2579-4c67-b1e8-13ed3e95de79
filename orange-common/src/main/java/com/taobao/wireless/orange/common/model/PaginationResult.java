package com.taobao.wireless.orange.common.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PaginationResult<T> extends BaseResult implements Serializable {
    @Serial
    private static final long serialVersionUID = -7984935710908202735L;

    /**
     * 结果数据列表
     */
    private List<T> data;

    /**
     * 总个数
     */
    private Long total;

    /**
     * 每页个数
     */
    private Long size;

    /**
     * 当前页
     */
    private Long current;

    /**
     * 总页数
     */
    public Long getPageCount() {
        if (this.size == null || this.total == null || this.size == 0 || this.total == 0) {
            return 0L;
        }
        long mod = this.total % this.size;
        if (mod == 0) {
            return this.total / this.size;
        } else {
            return this.total / this.size + 1;
        }
    }
}
