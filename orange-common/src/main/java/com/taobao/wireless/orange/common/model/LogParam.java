package com.taobao.wireless.orange.common.model;

import com.taobao.wireless.orange.common.constant.enums.LogType;
import com.taobao.wireless.orange.common.constant.enums.ProtocolType;
import lombok.Builder;
import lombok.Data;

/**
 * 统一日志信息类
 * 用于记录各种调用的日志信息
 * <p>
 * 必选参数包括日志类型和协议类型，其他参数则根据具体业务需求进行设置
 *
 * <AUTHOR>
 */
@Data
@Builder
public class LogParam {

    /**
     * 日志类型(HTTP/HSFServer/HSFClient/CustomLog)
     */
    private LogType type;

    /**
     * 请求协议(HSF/HTTP)
     */
    private ProtocolType protocolType;

    // 系统信息
    /**
     * 系统标志编码
     */
    private String system;
    /**
     * 被调用系统编码
     */
    private String calledSystem;

    // 用户信息
    /**
     * 工号或用户ID
     */
    private String workerId;
    /**
     * 花名或用户名
     */
    private String workerName;

    // 网络信息
    /**
     * 调用方IP
     */
    private String localIp;
    /**
     * 客户端IP
     */
    private String remoteIp;

    // 请求信息
    /**
     * 鹰眼ID
     */
    private String traceId;
    /**
     * HSF服务RPC ID
     */
    private String rpcId;
    /**
     * 入参HTTP-{query, body, params} HSF-[]
     */
    private String input;
    /**
     * 出参
     */
    private String output;
    /**
     * 请求方法名
     */
    private String method;
    /**
     * 请求路径
     */
    private String path;
    /**
     * 请求是否成功
     */
    private Boolean success;
    /**
     * 错误码
     */
    private String code;
    /**
     * 调用耗时
     */
    private Long costTime;

    // 时间信息
    /**
     * 调用开始时间
     */
    private String startTime;
    /**
     * 调用结束时间
     */
    private String endTime;

    // 环境信息
    /**
     * 服务器环境
     */
    private String env;

    // 业务信息
    /**
     * 业务信息(可选)
     */
    private String customMessage;
}

