package com.taobao.wireless.orange.common.util;

import org.apache.commons.lang3.StringUtils;

public class UserUtil {
    public static String formatWorkerId(String workerId) {
        return StringUtils.isNumeric(workerId) ? StringUtils.leftPad(workerId, 6, '0') : workerId;
    }

    public static String formatWorkerIdWithoutZero(String workerId) {
        return StringUtils.isNumeric(workerId) ? workerId.replaceAll("^0+", "") : workerId;
    }
}
