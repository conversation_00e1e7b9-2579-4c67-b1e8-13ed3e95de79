package com.taobao.wireless.orange.common.util;

import com.taobao.wireless.orange.common.constant.enums.Env;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class EnvUtil {

    private static Env env;

    public EnvUtil(@Value("${orange.env}") String env) {
        EnvUtil.env = Env.valueOf(env);
    }

    public Env currEnv() {
        return env;
    }

    public static boolean isDaily() {
        return Objects.equals(Env.DAILY, env);
    }

    public static boolean isPre() {
        return Objects.equals(Env.PRE, env);
    }

    public static boolean isProd() {
        return Objects.equals(Env.PROD, env);
    }
}
