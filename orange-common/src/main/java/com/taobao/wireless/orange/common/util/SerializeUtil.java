package com.taobao.wireless.orange.common.util;

import java.util.Date;
import java.util.UUID;

public class SerializeUtil {
    private static final Snowflake snowflake = new Snowflake();

    /**
     * 生成 ID
     *
     * @return
     */
    public static String UUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成递增的版本号
     */
    public static long version() {
        return snowflake.nextId();
    }

    /**
     * 根据版本号解析生成时间
     *
     * @param version
     * @return
     */
    public static Date parseGenerationTime(long version) {
        return snowflake.parseGenerationTime(version);
    }
}
