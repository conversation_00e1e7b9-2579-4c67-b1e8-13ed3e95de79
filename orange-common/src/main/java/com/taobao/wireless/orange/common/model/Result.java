package com.taobao.wireless.orange.common.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class Result<T> extends BaseResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 6972442177157015848L;

    /**
     * 返回值
     */
    private T data;

    public Result() {
        super();
    }

    public Result(T value) {
        super();
        this.data = value;
    }

    public static <T> Result<T> success(T value) {
        Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setData(value);
        return result;
    }

    public static Result<Void> success() {
        Result<Void> result = new Result<>();
        result.setSuccess(true);
        return result;
    }
}
