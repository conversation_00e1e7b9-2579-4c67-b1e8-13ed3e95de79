package com.taobao.wireless.orange.common.util;

import lombok.extern.slf4j.Slf4j;

import java.net.NetworkInterface;
import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Enumeration;

@Slf4j
public class Snowflake {
    private static final int NODE_ID_BITS = 3;
    private static final int SEQUENCE_BITS = 3;

    private static final long maxNodeId = (1L << NODE_ID_BITS) - 1;
    private static final long maxSequence = (1L << SEQUENCE_BITS) - 1;

    private final long nodeId;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS").withZone(ZoneId.systemDefault());

    private volatile long lastTimestamp = -1L;
    private volatile long sequence = 0L;

    public Snowflake() {
        this.nodeId = createNodeId();
        log.info("Snowflake nodeId: {}", nodeId);
    }

    public synchronized long nextId() {
        long currentTimestamp = timestamp();

        if (currentTimestamp < lastTimestamp) {
            throw new IllegalStateException("Invalid System Clock!");
        }

        if (currentTimestamp == lastTimestamp) {
            sequence = (sequence + 1) & maxSequence;
            if (sequence == 0) {
                currentTimestamp = waitNextMillis(currentTimestamp);
            }
        } else {
            sequence = 0;
        }

        lastTimestamp = currentTimestamp;

        String datetimePrefix = formatter.format(Instant.ofEpochMilli(currentTimestamp));
        return Long.parseLong(datetimePrefix + String.format("%02d", (nodeId << SEQUENCE_BITS) | sequence));
    }

    private long timestamp() {
        return Instant.now().toEpochMilli();
    }

    private long waitNextMillis(long currentTimestamp) {
        while (currentTimestamp == lastTimestamp) {
            currentTimestamp = timestamp();
        }
        return currentTimestamp;
    }

    private long createNodeId() {
        long nodeId;
        try {
            StringBuilder sb = new StringBuilder();
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                byte[] mac = networkInterface.getHardwareAddress();
                if (mac != null) {
                    for (byte macPort : mac) {
                        sb.append(String.format("%02X", macPort));
                    }
                }
            }
            nodeId = sb.toString().hashCode();
        } catch (Exception ex) {
            nodeId = (new SecureRandom().nextInt());
        }
        nodeId = nodeId & maxNodeId;
        return nodeId;
    }

    /**
     * 从 Snowflake ID 中解析出生成时间
     *
     * @param snowflakeId Snowflake ID
     * @return 生成时间的 Date 对象
     * @throws IllegalArgumentException 如果 ID 格式不正确
     */
    public Date parseGenerationTime(long snowflakeId) {
        try {
            // 将 ID 转换为字符串
            String idStr = String.valueOf(snowflakeId);

            // 检查 ID 长度是否正确 (yyyyMMddHHmmssSSS + 2位后缀 = 19位)
            if (idStr.length() != 19) {
                throw new IllegalArgumentException("Invalid Snowflake ID length: " + idStr.length());
            }

            // 提取时间戳部分 (前17位: yyyyMMddHHmmssSSS)
            String timestampStr = idStr.substring(0, 17);

            // 解析时间戳
            LocalDateTime dateTime = LocalDateTime.parse(timestampStr, formatter);

            // 转换为 Date 对象
            return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());

        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to parse Snowflake ID: " + snowflakeId, e);
        }
    }
}