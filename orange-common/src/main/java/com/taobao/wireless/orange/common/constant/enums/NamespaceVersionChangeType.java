package com.taobao.wireless.orange.common.constant.enums;

import java.util.List;

public enum NamespaceVersionChangeType {
    /**
     * 新建发布单
     */
    NEW_IMPACT_RELEASE,
    /**
     * 百分比发布
     */
    RATIO_GRAY,
    /**
     * 对线上产物有影响的发布
     */
    FINISH_IMPACT_RELEASE,
    /**
     * 取消发布
     */
    CANCEL_RELEASE,

    /**
     * 对线上产物无影响的发布（线上构建发布产物的时候忽略这类型发布单）
     */
    FINISH_NO_IMPACT_RELEASE,

    /**
     * 删除命名空间
     */
    DELETE_NAMESPACE,

    /**
     * 新建实验发布单
     */
    NEW_EXPERIMENT,
    /**
     * 取消实验发布
     */
    CANCEL_EXPERIMENT,
    ;

    public static List<NamespaceVersionChangeType> getFinishReleaseTypes() {
        return List.of(FINISH_IMPACT_RELEASE, FINISH_NO_IMPACT_RELEASE);
    }
}
