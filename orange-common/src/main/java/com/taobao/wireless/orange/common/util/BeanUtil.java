package com.taobao.wireless.orange.common.util;

import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Bean 工具类
 *
 * <AUTHOR>
 */
public class BeanUtil {

    private static final Map<Class<?>, Class<?>> PRIMITIVE_TO_WRAPPER_MAP = Map.of(
            int.class, Integer.class,
            boolean.class, Boolean.class,
            char.class, Character.class,
            byte.class, Byte.class,
            long.class, Long.class,
            float.class, Float.class,
            double.class, Double.class,
            short.class, Short.class
    );

    private static final Map<ClassPair, List<PropertyAccessor>> ACCESSOR_CACHE = new ConcurrentHashMap<>(256);

    private record ClassPair(Class<?> source, Class<?> target) {
    }

    private record PropertyAccessor(
            Method getter,
            Method setter,
            Function<Object, Object> converter,
            Function<Object, Object> deepCopier
    ) {
        void copy(Object source, Object target) {
            try {
                if (!getter.canAccess(source) || !setter.canAccess(target)) {
                    getter.setAccessible(true);
                    setter.setAccessible(true);
                }

                Object value = getter.invoke(source);

                if (deepCopier != null) {
                    value = deepCopier.apply(value);
                }

                if (converter != null) {
                    value = converter.apply(value);
                }

                if (setter.getParameterTypes()[0].isPrimitive() && value == null) {
                    return;
                }

                setter.invoke(target, value);
            } catch (Exception e) {
                throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION,
                        String.format("Failed to copy property from %s.%s to %s.%s",
                                source.getClass().getSimpleName(), getter.getName(),
                                target.getClass().getSimpleName(), setter.getName()), e);
            }
        }
    }

    public static void copyProperties(Object source, Object target) {
        copyProperties(source, target, true, null);
    }

    public static void copyProperties(Object source, Object target, Map<String, Class<?>> deepCopyMap) {
        copyProperties(source, target, false, deepCopyMap);
    }

    public static <T> T createFromProperties(Object source, Class<T> targetType) {
        return createFromProperties(source, targetType, null);
    }

    public static <S, T> T createFromProperties(S source, Class<T> targetType, Map<String, Class<?>> deepCopyMap) {
        if (source == null) return null;
        try {
            Constructor<T> constructor = targetType.getDeclaredConstructor();
            constructor.setAccessible(true);
            T target = constructor.newInstance();
            copyProperties(source, target, false, deepCopyMap);
            return target;
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION,
                    "Failed to create and copy properties for target class: " + targetType.getName(), e);
        }
    }

    public static <S, T> List<T> createListFromProperties(List<S> sourceList, Class<T> targetType) {
        return createListFromProperties(sourceList, targetType, null);
    }

    public static <S, T> List<T> createListFromProperties(List<S> sourceList, Class<T> targetType, Map<String, Class<?>> deepCopyMap) {
        if (sourceList == null) return null;
        return sourceList.stream()
                .map(source -> createFromProperties(source, targetType, deepCopyMap))
                .collect(Collectors.toList());
    }

    /**
     * 科里化版本的 createFromProperties 方法
     *
     * @param targetType 目标类型
     * @param <S>        源类型
     * @param <T>        目标类型
     * @return 接受 source 的函数
     */
    public static <S, T> Function<S, T> createFromProperties(Class<T> targetType) {
        return source -> createFromProperties(source, targetType);
    }

    /**
     * 科里化版本的 createListFromProperties 方法
     *
     * @param targetType 目标类型
     * @param <S>        源类型
     * @param <T>        目标类型
     * @return 接受 sourceList 的函数
     */
    public static <S, T> Function<List<S>, List<T>> createListFromProperties(Class<T> targetType) {
        return sourceList -> createListFromProperties(sourceList, targetType);
    }

    public static void copyProperties(Object source, Object target, boolean skipNulls, Map<String, Class<?>> deepCopyMap, String... ignoreProperties) {
        if (source == null || target == null) return;
        var accessors = getPropertyAccessors(source.getClass(), target.getClass(), deepCopyMap, ignoreProperties);
        for (var accessor : accessors) {
            if (skipNulls) {
                try {
                    Object sourceValue = accessor.getter().invoke(source);
                    if (sourceValue == null) continue;
                } catch (Exception e) {
                    throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION, e);
                }
            }
            accessor.copy(source, target);
        }
    }

    private static List<PropertyAccessor> getPropertyAccessors(Class<?> sourceClass, Class<?> targetClass, Map<String, Class<?>> deepCopyMap, String... ignoreProperties) {
        var key = new ClassPair(sourceClass, targetClass);
        return ACCESSOR_CACHE.computeIfAbsent(key, BeanUtil::resolvePropertyAccessors)
                .stream()
                .filter(accessor -> {
                    Set<String> ignoreSet = (ignoreProperties != null) ? Set.of(ignoreProperties) : Collections.emptySet();
                    String propName = getPropertyName(accessor.getter());
                    return !ignoreSet.contains(propName);
                })
                .map(accessor -> {
                    String propName = getPropertyName(accessor.getter());
                    if (deepCopyMap != null && deepCopyMap.containsKey(propName)) {
                        Class<?> deepCopyTargetClass = deepCopyMap.get(propName);
                        Function<Object, Object> deepCopier = (value) -> {
                            if (value instanceof Collection) {
                                return createListFromProperties((List<?>) value, deepCopyTargetClass, deepCopyMap);
                            }
                            return value;
                        };
                        return new PropertyAccessor(accessor.getter(), accessor.setter(), accessor.converter(), deepCopier);
                    }
                    return accessor;
                })
                .toList();
    }

    private static List<PropertyAccessor> resolvePropertyAccessors(ClassPair classPair) {
        try {
            BeanInfo sourceBeanInfo = Introspector.getBeanInfo(classPair.source());
            BeanInfo targetBeanInfo = Introspector.getBeanInfo(classPair.target());

            Map<String, PropertyDescriptor> sourcePds = Arrays.stream(sourceBeanInfo.getPropertyDescriptors())
                    .collect(Collectors.toMap(PropertyDescriptor::getName, Function.identity()));

            return Arrays.stream(targetBeanInfo.getPropertyDescriptors())
                    .map(targetPd -> {
                        PropertyDescriptor sourcePd = sourcePds.get(targetPd.getName());
                        if (sourcePd == null) return null;

                        Method getter = sourcePd.getReadMethod();
                        Method setter = targetPd.getWriteMethod();
                        if (getter == null || setter == null) return null;

                        Class<?> sourceType = getter.getReturnType();
                        Class<?> targetType = setter.getParameterTypes()[0];
                        var converter = createConverter(sourceType, targetType);

                        // --- FIX: 使用更完善的兼容性检查 ---
                        if (isBasicallyAssignable(targetType, sourceType) || converter != null) {
                            return new PropertyAccessor(getter, setter, converter != null ? converter : Function.identity(), null);
                        }

                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.BEAN_COPY_EXCEPTION, "Failed to resolve property accessors for " + classPair, e);
        }
    }

    private static boolean isBasicallyAssignable(Class<?> targetType, Class<?> sourceType) {
        // 1. 标准的 isAssignableFrom 检查
        if (targetType.isAssignableFrom(sourceType)) {
            return true;
        }
        // 2. 检查 包装类 -> 基础类型 (e.g., Long -> long)
        if (targetType.isPrimitive() && PRIMITIVE_TO_WRAPPER_MAP.get(targetType) == sourceType) {
            return true;
        }
        // 3. 检查 基础类型 -> 包装类 (e.g., long -> Long)
        return sourceType.isPrimitive() && PRIMITIVE_TO_WRAPPER_MAP.get(sourceType) == targetType;
    }

    private static Function<Object, Object> createConverter(Class<?> sourceType, Class<?> targetType) {
        return switch (targetType) {
            case Class<?> t when t == Boolean.class || t == boolean.class -> switch (sourceType) {
                case Class<?> s when s == Integer.class || s == int.class -> v -> v != null && ((Integer) v) == 1;
                case Class<?> s when s == String.class -> v -> v != null && "true".equalsIgnoreCase(v.toString());
                case Class<?> s when s == Byte.class || s == byte.class -> v -> v != null && ((Byte) v) == 1;
                default -> null;
            };
            case Class<?> t when t == Integer.class || t == int.class -> switch (sourceType) {
                case Class<?> s when s == Boolean.class || s == boolean.class -> v -> v != null && (Boolean) v ? 1 : 0;
                case Class<?> s when s == Byte.class || s == byte.class ->
                        v -> v != null ? Byte.toUnsignedInt((Byte) v) : null;
                default -> null;
            };
            case Class<?> t when t == String.class ->
                    (sourceType == String.class) ? null : v -> v != null ? v.toString() : null;
            default -> null;
        };
    }

    private static String getPropertyName(Method getter) {
        String name = getter.getName();
        if (name.startsWith("get") && name.length() > 3) {
            return Character.toLowerCase(name.charAt(3)) + name.substring(4);
        }
        if (name.startsWith("is") && name.length() > 2) {
            return Character.toLowerCase(name.charAt(2)) + name.substring(3);
        }
        return name;
    }
}