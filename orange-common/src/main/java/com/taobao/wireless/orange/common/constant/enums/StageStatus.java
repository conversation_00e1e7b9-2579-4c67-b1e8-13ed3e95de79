package com.taobao.wireless.orange.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum StageStatus {
    /**
     * 初始
     */
    INIT(false),
    /**
     * 正在执行
     */
    IN_PROGRESS(false),
    /**
     * 被跳过
     */
    SKIPPED(true),
    /**
     * 成功
     */
    SUCCESS(true),
    /**
     * 失败
     */
    FAILED(false),
    /**
     * 被取消（由于取消发布单引起）
     */
    CANCELED(true),
    ;

    /**
     * 是否成功完成，完成代表可以进行下一个阶段
     */
    private final boolean isCompleted;

    public static List<StageStatus> getCompletedStatuses() {
        return Arrays.stream(StageStatus.values())
                .filter(StageStatus::isCompleted)
                .collect(Collectors.toList());
    }
}
