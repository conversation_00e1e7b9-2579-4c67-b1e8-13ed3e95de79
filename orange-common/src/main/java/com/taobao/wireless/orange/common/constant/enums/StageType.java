package com.taobao.wireless.orange.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum StageType {
    /**
     * 灰度
     */
//    BETA(0),
    /**
     * 申请发布
     */
    APPLY_RELEASE(1, "申请发布"),
    /**
     * 小流量灰度
     */
    SMALLFLOW_GRAY(2, "小流量灰度"),
    /**
     * 人工验证
     */
    VERIFY(3, "人工验证"),
    /**
     * 百分比发布
     */
    RATIO_GRAY(4, "百分比发布"),
    /**
     * 正式发布
     */
    RELEASE(5, "正式发布"),
    ;

    private final Integer stageOrder;

    private final String stageName;

    public static StageType getByName(String typeName) {
        for (StageType stageType : StageType.values()) {
            if (stageType.name().equals(typeName)) {
                return stageType;
            }
        }
        return null;
    }
}
