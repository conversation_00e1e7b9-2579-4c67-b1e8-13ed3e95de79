package com.taobao.wireless.orange.common.thread;

/**
 * 线程上下文持有者
 *
 * <AUTHOR>
 */
public class OThreadContextHolder {

    private static final InheritableThreadLocal<OThreadContext> threadLocal = new InheritableThreadLocal<>();

    public static OThreadContext get() {
        return threadLocal.get();
    }

    public static void clear() {
        threadLocal.remove();
    }

    public static void set(OThreadContext threadContext) {
        threadLocal.set(threadContext);
    }
}
