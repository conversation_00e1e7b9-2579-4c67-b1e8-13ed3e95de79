package com.taobao.wireless.orange.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Generic batch processor utility for handling batch requests
 */
@Component
@Slf4j
public class BatchProcessor {

    private static final int DEFAULT_BATCH_SIZE = 1000;

    /**
     * Configuration for batch processing
     */
    public static class BatchConfig<T> {
        private int batchSize = DEFAULT_BATCH_SIZE;
        private Predicate<T> validator = Objects::nonNull;
        private Function<T, T> preprocessor = Function.identity();
        private boolean removeDuplicates = true;
        private boolean continueOnError = true;
        private Executor executor = ForkJoinPool.commonPool();

        public BatchConfig<T> batchSize(int batchSize) {
            this.batchSize = batchSize;
            return this;
        }

        public BatchConfig<T> validator(Predicate<T> validator) {
            this.validator = validator;
            return this;
        }

        public BatchConfig<T> preprocessor(Function<T, T> preprocessor) {
            this.preprocessor = preprocessor;
            return this;
        }

        public BatchConfig<T> removeDuplicates(boolean removeDuplicates) {
            this.removeDuplicates = removeDuplicates;
            return this;
        }

        public BatchConfig<T> continueOnError(boolean continueOnError) {
            this.continueOnError = continueOnError;
            return this;
        }

        public BatchConfig<T> executor(Executor executor) {
            this.executor = executor;
            return this;
        }

        // Getters
        public int getBatchSize() {
            return batchSize;
        }

        public Predicate<T> getValidator() {
            return validator;
        }

        public Function<T, T> getPreprocessor() {
            return preprocessor;
        }

        public boolean isRemoveDuplicates() {
            return removeDuplicates;
        }

        public boolean isContinueOnError() {
            return continueOnError;
        }

        public Executor getExecutor() {
            return executor;
        }
    }

    /**
     * Process batch requests synchronously
     *
     * @param inputList      List of input items
     * @param processor      Function that processes a batch and returns results
     * @param resultCombiner Function that combines individual results into final result
     * @param config         Batch configuration
     * @return Final combined result
     * @throws IllegalArgumentException if input is invalid
     * @throws RuntimeException         if processing fails and continueOnError is false
     */
    public <T, R, F> F processBatch(
            List<T> inputList,
            Function<List<T>, R> processor,
            Function<List<R>, F> resultCombiner,
            BatchConfig<T> config) {

        // Validate and preprocess input
        List<T> processedList = validateAndPreprocessInput(inputList, config);
        if (processedList.isEmpty()) {
            throw new IllegalArgumentException("No valid items to process");
        }

        if (processedList.size() <= config.getBatchSize()) {
            // Single batch
            R result = processor.apply(processedList);
            return resultCombiner.apply(Collections.singletonList(result));
        } else {
            // Multiple batches
            return processBatchesSynchronously(processedList, processor, resultCombiner, config);
        }
    }

    /**
     * Process batch requests asynchronously
     */
    public <T, R, F> F processBatchAsync(
            List<T> inputList,
            Function<List<T>, R> processor,
            Function<List<R>, F> resultCombiner,
            BatchConfig<T> config) {

        List<T> processedList = validateAndPreprocessInput(inputList, config);
        if (processedList.isEmpty()) {
            throw new IllegalArgumentException("No valid items to process");
        }

        if (processedList.size() <= config.getBatchSize()) {
            R result = processor.apply(processedList);
            return resultCombiner.apply(Collections.singletonList(result));
        } else {
            return processBatchesAsynchronously(processedList, processor, resultCombiner, config);
        }
    }

    /**
     * Simplified version for Map results (most common use case)
     */
    public <T, K, V> Map<K, V> processBatchToMap(
            List<T> inputList,
            Function<List<T>, Map<K, V>> processor,
            BatchConfig<T> config) {

        return processBatch(
                inputList,
                processor,
                this::combineMaps,
                config
        );
    }

    /**
     * Simplified version for List results
     */
    public <T, R> List<R> processBatchToList(
            List<T> inputList,
            Function<List<T>, List<R>> processor,
            BatchConfig<T> config) {

        return processBatch(
                inputList,
                processor,
                this::combineLists,
                config
        );
    }

    /**
     * Process batch to Map asynchronously
     */
    public <T, K, V> Map<K, V> processBatchToMapAsync(
            List<T> inputList,
            Function<List<T>, Map<K, V>> processor,
            BatchConfig<T> config) {

        return processBatchAsync(
                inputList,
                processor,
                this::combineMaps,
                config
        );
    }

    /**
     * Process batch to List asynchronously
     */
    public <T, R> List<R> processBatchToListAsync(
            List<T> inputList,
            Function<List<T>, List<R>> processor,
            BatchConfig<T> config) {

        return processBatchAsync(
                inputList,
                processor,
                this::combineLists,
                config
        );
    }

    private <T> List<T> validateAndPreprocessInput(List<T> inputList, BatchConfig<T> config) {
        if (inputList == null || inputList.isEmpty()) {
            return Collections.emptyList();
        }

        var stream = inputList.stream()
                .filter(config.getValidator())
                .map(config.getPreprocessor());

        if (config.isRemoveDuplicates()) {
            stream = stream.distinct();
        }

        return stream.collect(Collectors.toList());
    }

    private <T, R, F> F processBatchesSynchronously(
            List<T> processedList,
            Function<List<T>, R> processor,
            Function<List<R>, F> resultCombiner,
            BatchConfig<T> config) {

        List<R> allResults = new ArrayList<>();
        int batchSize = config.getBatchSize();

        for (int i = 0; i < processedList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, processedList.size());
            List<T> batch = processedList.subList(i, endIndex);

            try {
                R batchResult = processor.apply(batch);
                if (batchResult != null) {
                    allResults.add(batchResult);
                }
            } catch (Exception e) {
                log.warn("Failed to process batch {}-{}: {}", i, endIndex - 1, e.getMessage());
                if (!config.isContinueOnError()) {
                    throw new RuntimeException("Batch processing failed at batch " + i, e);
                }
            }
        }

        return resultCombiner.apply(allResults);
    }

    private <T, R, F> F processBatchesAsynchronously(
            List<T> processedList,
            Function<List<T>, R> processor,
            Function<List<R>, F> resultCombiner,
            BatchConfig<T> config) {

        List<CompletableFuture<R>> futures = new ArrayList<>();
        int batchSize = config.getBatchSize();

        for (int i = 0; i < processedList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, processedList.size());
            List<T> batch = new ArrayList<>(processedList.subList(i, endIndex));

            CompletableFuture<R> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return processor.apply(batch);
                } catch (Exception e) {
                    log.warn("Failed to process async batch: {}", e.getMessage());
                    if (!config.isContinueOnError()) {
                        throw new RuntimeException("Batch processing failed", e);
                    }
                    return null;
                }
            }, config.getExecutor());

            futures.add(future);
        }

        List<R> allResults = futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return resultCombiner.apply(allResults);
    }

    private <K, V> Map<K, V> combineMaps(List<Map<K, V>> maps) {
        return maps.stream()
                .filter(Objects::nonNull)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing
                ));
    }

    private <T> List<T> combineLists(List<List<T>> lists) {
        return lists.stream()
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }
}