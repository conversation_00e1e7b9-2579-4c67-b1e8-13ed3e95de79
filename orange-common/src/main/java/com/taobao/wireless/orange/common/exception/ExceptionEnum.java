package com.taobao.wireless.orange.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 异常枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ExceptionEnum {

    // A类 - 入参错误
    // 通用入参错误 (68)
    PARAM_INVALID("A68001", "入参不合法"),
    VIEW_TYPE_NOT_SUPPORT("A68002", "展示类型不支持"),
    NO_PERMISSION("A68003", "无权限"),

    // 命名空间入参错误 (01)
    NAMESPACE_NOT_EXIST("A01001", "命名空间不存在"),
    NAMESPACE_NAME_DUPLICATE("A01002", "该命名空间名字已存在"),
    NAMESPACE_LOCKED("A01003", "命名空间正在发布中"),

    // 参数入参错误 (02)
    PARAMETER_NOT_FOUND("A02001", "参数不存在"),
    PARAMETER_NOT_ONLINE("A02002", "参数未上线"),
    PARAMETER_KEY_DUPLICATE("A02003", "存在重复的参数 KEY"),
    PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A02004", "参数[key={}]已经有新的版本"),
    PARAMETER_CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A02005", "参数[key={}]条件[name={}]已经有新的版本"),
    PARAMETER_IS_PUBLISHING("A02006", "参数正在发布中"),
    PARAMETER_CONDITION_DUPLICATE("A02007", "存在重复的参数条件"),
    PARAMETER_CONDITION_DELETE_DEFAULT("A02008", "参数[key={}]不能删除默认条件"),
    PARAMETER_DEFAULT_CONDITION_NOT_FOUND("A02009", "参数[key={}]没有默认条件"),
    PARAMETER_PREVIOUS_RELEASE_VERSION_NOT_FOUND("A02010", "参数[key={}]没有上一次发布版本"),

    // 条件入参错误 (03)
    CONDITION_IS_PUBLISHING("A03001", "条件正在发布中"),
    CONDITION_NOT_FOUND("A03002", "条件不存在"),
    CONDITION_PREVIOUS_RELEASE_VERSION_NOT_MATCH("A03003", "条件[id={}]已经有新的版本"),
    CONDITION_NAME_DUPLICATE("A03004", "条件名字[{}]已存在"),
    CONDITION_PREVIOUS_RELEASE_VERSION_NOT_FOUND("A03005", "条件[id={}]没有上一次发布版本"),

    // 发布单入参错误 (04)
    RELEASE_ORDER_NOT_EXIST("A04001", "发布单不存在"),
    RELEASE_ORDER_STATUS_NOT_VERIFY_PASS("A04002", "发布单未验证通过"),
    RELEASE_ORDER_STATUS_INVALID("A04003", "发布单状态不正确"),
    RELEASE_ORDER_IS_FINISHED("A04004", "发布单已结束，禁止操作"),
    RELEASE_ORDER_STAGE_NOT_EXIST("A04005", "发布单阶段不存在"),
    RELEASE_ORDER_STAGE_ALREADY_COMPLETED("A04006", "该发布阶段已完成，禁止重复操作"),
    RELEASE_ORDER_STAGE_NOT_COMPLETED("A04007", "发布阶段[{}]尚未完成"),

    RESOURCE_NOT_EXIST("A05001", "资源不存在"),

    CHANGEFREE_CHECK_NOT_PASS("A06001", "发布单 Changefree 准入检测未通过，请进行发布申请"),

    // B类 - 本系统错误
    // 命名空间系统错误 (01)
    CREATE_NAMESPACE_FAIL("B01001", "创建命名空间失败"),
    NAMESPACE_NOT_FOUND("B01002", "命名空间不存在"),
    NAMESPACE_VERSION_CONTENT_NOT_EXIST("B01003", "命名空间版本快照不存在"),
    UPDATE_NAMESPACE_FAIL("B01004", "更新命名空间失败"),

    // 参数系统错误 (02)
    CREATE_PARAMETER_FAIL("B02001", "创建参数失败"),

    // 条件系统错误 (03)
    CREATE_CONDITION_FAIL("B03001", "创建条件失败"),

    // 发布单系统错误 (04)
    CREATE_RELEASE_ORDER_FAIL("B04001", "创建发布单失败"),
    RELEASE_ORDER_GRAY_AND_NAMESPACE_VERSION_NOT_MATCH("B04002", "发布单灰度阶段和命名空间版本不匹配"),

    // 通用系统错误 (68)
    RESOURCE_DESERIALIZE_ERROR("B68002", "反序列化失败"),
    DESERIALIZE_EXCEPTION("B68003", "反序列化异常"),
    SYSTEM_EXCEPTION("B68004", "Orange 服务系统异常"),
    BEAN_COPY_EXCEPTION("B68005", "BEAN 拷贝异常"),

    // C类 - 外部服务错误
    // AMDP 服务错误 (81)
    AMDP_ERROR("C81001", "调用 Amdp 服务异常"),

    // MTL 服务错误 (82)
    MTL_ERROR("C82001", "调用 MTL 服务异常"),
    MTL_INIT_ERROR("C82002", "MTL 客户端初始化失败"),
    MTL_ENDPOINT_INVALID("C82003", "MTL endpoint 配置无效"),
    MTL_ACCESS_KEY_BLANK("C82004", "MTL accessKey 不能为空"),
    MTL_ACCESS_SECRET_BLANK("C82005", "MTL accessSecret 不能为空"),
    MTL_SEARCH_PARAMS_INVALID("C82006", "MTL 搜索参数无效，appKey 和 keyword 均不能为空"),
    MTL_HTTP_ERROR("C82007", "调用 MTL 接口失败，状态码：{}"),
    MTL_BUSINESS_ERROR("C82008", "调用 MTL 接口失败：{}"),
    MTL_PARSE_ERROR("C82009", "解析 MTL 返回结果失败"),

    // WMCC 服务错误 (83)
    WMCC_SERVICE_NOT_AVAILABLE("C83001", "WMCC 配置发布服务不可用"),
    WMCC_PUBLISH_ERROR("C83002", "WMCC 配置发布任务提交失败: {}"),
    WMCC_ROLLBACK_ERROR("C83003", "WMCC 配置回滚任务提交失败"),
    WMCC_QUERY_RUNNING_TASK_ERROR("C83004", "WMCC 查询正在发布的任务信息失败"),
    WMCC_QUERY_TASK_STATUS_ERROR("C83005", "WMCC 查询任务发布状态失败"),
    WMCC_GET_ROLLBACK_CONFIG_ERROR("C83006", "WMCC 获取回滚配置内容失败"),
    WMCC_PAUSE_TASK_ERROR("C83007", "WMCC 暂停任务失败"),
    WMCC_CANCEL_TASK_ERROR("C83008", "WMCC 取消任务失败"),
    WMCC_CANCEL_TASK_FAIL("C83009", "WMCC 取消任务[taskId={}]失败"),
    WMCC_QUERY_TASK_INFO_ERROR("C83010", "WMCC 获取任务信息失败"),

    // CHANGEFREE 服务错误 (84)
    CHANGEFREE_CHECK_ERROR("C84001", "调用 Changefree check 服务异常"),
    CHANGEFREE_QUERY_ERROR("C84002", "调用 Changefree query 服务异常"),
    CHANGEFREE_START_ERROR("C84003", "调用 Changefree 执行单 start 服务异常"),
    CHANGEFREE_END_ERROR("C84004", "调用 Changefree 执行单 end 服务异常"),

    // OSS 服务错误 (85)
    OSS_READ_ERROR("C85001", "读取 OSS 数据失败"),
    OSS_UPLOAD_ERROR("C85002", "上传 OSS 数据失败"),

    // Tiga
    TIGA_CREATE_TASK_EXCEPTION("C86001", "调用 Tiga 创建任务服务异常"),
    TIGA_CREATE_TASK_ERROR("C86002", "调用 Tiga 创建任务服务失败：{}"),
    TIGA_DO_TASK_ERROR("C86003", "调用 Tiga 执行任务服务失败：{}"),
    TIGA_DO_TASK_EXCEPTION("C86004", "调用 Tiga 执行任务服务异常"),
    TIGA_GET_TASK_STAGE_LIST_ERROR("C86005", "调用 Tiga 获取任务阶段列表服务失败：{}"),
    TIGA_GET_TASK_STAGE_LIST_EXCEPTION("C86006", "调用 Tiga 获取任务阶段列表服务异常"),
    TIGA_GET_TEMPLATE_LIST_EXCEPTION("C86007", "调用 Tiga 获取灰度模板列表服务异常"),
    TIGA_GET_TEMPLATE_LIST_ERROR("C86008", "调用 Tiga 获取灰度模板列表服务失败：{}"),
    TIGA_GET_TEMPLATE_INSTANCE_ERROR("C86009", "调用 Tiga 获取灰度模板实例详细信息服务失败：{}"),
    TIGA_GET_TEMPLATE_INSTANCE_EXCEPTION("C86010", "调用 Tiga 获取灰度模板实例详细信息服务异常"),
    TIGA_GET_TASK_LIST_ERROR("C86011", "调用 Tiga 获取任务列表服务失败：{}"),
    TIGA_GET_TASK_LIST_EXCEPTION("C86012", "调用 Tiga 获取任务列表服务异常"),
    TIGA_GET_TASK_LIST_EMPTY("C86013", "调用 Tiga 获取任务为空"),
    TIGA_GET_TASK_RESULT_ERROR("C86014", "调用 Tiga 获取任务结果服务失败：{}"),
    TIGA_GET_TASK_RESULT_EXCEPTION("C86015", "调用 Tiga 获取任务结果服务异常"),
    TIGA_GET_GRAYSCALE_DEVICE_COUNT_ERROR("C86016", "调用 Tiga 获取灰度设备数服务失败：{}"),
    TIGA_GET_GRAYSCALE_DEVICE_COUNT_EXCEPTION("C86017", "调用 Tiga 获取灰度设备数服务异常"),

    // ACL
    ACL_CHECK_PERMISSION_ERROR("C87001", "调用 ACL 权限校验服务失败：{}"),

    // SLS
    SLS_QUERY_ERROR("C88001", "调用 SLS 查询服务失败"),

    // Idealab 服务错误 (89)
    IDEALAB_ERROR("C89001", "调用 Idealab 服务异常"),
    IDEALAB_HTTP_ERROR("C89002", "调用 Idealab 接口失败，状态码：{}"),
    IDEALAB_BUSINESS_ERROR("C89003", "调用 Idealab 接口失败：{}"),
    IDEALAB_PARSE_ERROR("C89004", "解析 Idealab 返回结果失败"),
    IDEALAB_PARAMS_INVALID("C89005", "Idealab 请求参数无效"),
    ;

    private final String code;
    private final String message;
}
