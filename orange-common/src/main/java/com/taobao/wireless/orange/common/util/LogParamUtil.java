package com.taobao.wireless.orange.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.hsf.domain.HSFResponse;
import com.taobao.hsf.invocation.Invocation;
import com.taobao.hsf.invocation.RPCResult;
import com.taobao.hsf.plugins.eagleeye.EagleEyeConstants;
import com.taobao.hsf.util.RequestCtxUtil;
import com.taobao.wireless.orange.common.constant.enums.LogType;
import com.taobao.wireless.orange.common.constant.enums.ProtocolType;
import com.taobao.wireless.orange.common.model.LogParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 日志对象组装工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class LogParamUtil {

    // 常量定义
    private static final int MAX_RESULT_SIZE = 4000;
    private static final String DEFAULT_SUCCESS_CODE = "200";
    private static final String DEFAULT_ERROR_CODE = "500";
    private static final String HSF_ERROR_CODE = "500.hsf.error";
    private static final String HSF_TIMEOUT_CODE = "500.hsf.timeout";
    private static final String HSF_FAIL_CODE = "500.hsf.fail";
    private static final String ABBREVIATION_CONNECTOR = "....";
    private static final String EMPTY_STRING = "";

    private static final DateTimeFormatter DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    // 成功字段名称
    private static final List<String> SUCCESS_FIELD_NAMES = List.of("success", "isSuccess");
    private static final List<String> SUCCESS_METHOD_NAMES = List.of("isSuccess", "getSuccess");

    // 错误码字段名称
    private static final List<String> CODE_FIELD_NAMES = List.of("code", "errorCode", "returnCode");
    private static final List<String> CODE_METHOD_NAMES = List.of("getCode", "getErrorCode");

    private LogParamUtil() {
    }

    public static LogParam generateLog(LogType type, Invocation invocation, Exception e) {
        try {
            LogParam logParam = generateBaseLogParam(type, invocation);

            // 出参
            logParam.setOutput(e.getMessage());
            logParam.setSuccess(false);
            logParam.setCode(DEFAULT_ERROR_CODE);

            return logParam;
        } catch (Exception exception) {
            log.error("Failed to generate log for exception", exception);
            return null;
        }
    }

    public static LogParam generateLog(LogType type, Invocation invocation, RPCResult rpcResult) {
        try {
            LogParam logParam = generateBaseLogParam(type, invocation);

            // 出参补充
            logParam.setOutput(generateOutput(rpcResult));

            Boolean success = generateSuccess(rpcResult);
            logParam.setSuccess(success);
            // 正常情况下不关注 code, 直接赋值，减少反射开销
            logParam.setCode(BooleanUtils.isTrue(success) ? DEFAULT_SUCCESS_CODE : generateCode(rpcResult));

            return logParam;
        } catch (Exception exception) {
            log.error("Failed to generate log for RPC result", exception);
            return null;
        }
    }


    private static LogParam generateBaseLogParam(LogType type, Invocation invocation) {
        long currentTime = System.currentTimeMillis();
        long startTime = invocation.getStartTime();
        Object[] args = invocation.getMethodArgs();

        return LogParam.builder()
                .type(type)
                .protocolType(ProtocolType.HSF)
                .env(System.getenv("envSign"))
                .system(System.getProperty("project.name", "orange"))
                .calledSystem(RequestCtxUtil.getAppNameOfClient())
                .remoteIp(LogType.HSF_SERVER.equals(type) ? invocation.getPeerIP() : invocation.getInvokerContext().getRemoteIp())
                .localIp(RequestCtxUtil.getLocalIp())
                .path(invocation.getTargetServiceUniqueName())
                .method(invocation.getMethodName())
                .startTime(formatTimestamp(startTime))
                .endTime(formatTimestamp(currentTime))
                .costTime(currentTime - startTime)
                .traceId((String) invocation.get(EagleEyeConstants.EAGLEEYE_TRACE_ID_KEY))
                .rpcId((String) invocation.get(EagleEyeConstants.EAGLEEYE_RPC_ID_KEY))
                .input(abbreviationStr(generateInput(args), MAX_RESULT_SIZE))
                .build();
    }

    private static String generateInput(Object[] args) {
        if (args == null || args.length == 0) {
            return EMPTY_STRING;
        }
        try {
            return JSON.toJSONString(args);
        } catch (Exception e) {
            log.warn("Failed to serialize input arguments", e);
            return "Failed to serialize input";
        }
    }

    private static String generateOutput(RPCResult rpcResult) {
        HSFResponse hsfResponse = rpcResult.getHsfResponse();
        if (StringUtils.isNotEmpty(hsfResponse.getErrorMsg())) {
            return hsfResponse.getErrorMsg();
        }

        Object result = rpcResult.getAppResponse();
        if (result == null) {
            return null;
        }
        if (result instanceof Throwable) {
            return ((Throwable) result).getMessage();
        }

        try {
            String resultStr = JSON.toJSONString(result);
            // 对于大返回值的请求进行结果删减
            return abbreviationStr(resultStr, MAX_RESULT_SIZE);
        } catch (Exception e) {
            log.warn("Failed to serialize output result", e);
            return "Failed to serialize output";
        }
    }

    /**
     * 格式化时间戳为字符串
     */
    private static String formatTimestamp(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
                .format(DATE_TIME_FORMATTER);
    }

    private static String abbreviationStr(String content, Integer size) {
        if (StringUtils.isEmpty(content) || content.length() <= size) {
            return content;
        }
        int halfWordLength = (size - ABBREVIATION_CONNECTOR.length()) / 2;
        return content.substring(0, halfWordLength) + ABBREVIATION_CONNECTOR + content.substring(
                content.length() - halfWordLength);
    }

    private static Boolean generateSuccess(RPCResult rpcResult) {
        HSFResponse hsfResponse = rpcResult.getHsfResponse();
        if (hsfResponse.getErrorType() != null) {
            return false;
        }

        Object result = rpcResult.getAppResponse();
        return switch (result) {
            case null -> null;
            case Throwable throwable -> false;
            case HashMap<?, ?> hashMap -> {
                @SuppressWarnings("unchecked")
                Map<String, Object> map = (HashMap<String, Object>) hashMap;
                yield extractBooleanFromMap(map, SUCCESS_FIELD_NAMES);
            }
            case JSONObject jsonObject -> extractBooleanFromJsonObject(jsonObject, SUCCESS_FIELD_NAMES);
            case ArrayList<?> arrayList -> true;
            default -> extractBooleanFromObject(result, SUCCESS_METHOD_NAMES);
        };
    }

    private static String generateCode(RPCResult rpcResult) {
        HSFResponse hsfResponse = rpcResult.getHsfResponse();
        if (hsfResponse.isError()) {
            return HSF_ERROR_CODE;
        }
        if (hsfResponse.isTimeout()) {
            return HSF_TIMEOUT_CODE;
        }

        Object result = rpcResult.getAppResponse();
        return switch (result) {
            case null -> null;
            case Throwable ignored -> HSF_FAIL_CODE;
            case HashMap<?, ?> hashMap -> {
                @SuppressWarnings("unchecked")
                var map = (HashMap<String, Object>) hashMap;
                yield extractStringFromMap(map, CODE_FIELD_NAMES);
            }
            case JSONObject jsonObject -> extractStringFromJsonObject(jsonObject, CODE_FIELD_NAMES);
            default -> extractStringFromObject(result, CODE_METHOD_NAMES);
        };
    }

    /**
     * 从Map中提取布尔值
     */
    private static Boolean extractBooleanFromMap(Map<String, Object> map, List<String> fieldNames) {
        return fieldNames.stream()
                .map(map::get)
                .filter(Objects::nonNull)
                .findFirst()
                .map(value -> BooleanUtils.toBoolean(String.valueOf(value)))
                .orElse(null);
    }

    /**
     * 从JSONObject中提取布尔值
     */
    private static Boolean extractBooleanFromJsonObject(JSONObject jsonObject, List<String> fieldNames) {
        return fieldNames.stream()
                .map(jsonObject::get)
                .filter(Objects::nonNull)
                .findFirst()
                .map(value -> BooleanUtils.toBoolean(String.valueOf(value)))
                .orElse(null);
    }

    /**
     * 从对象中通过反射提取布尔值
     */
    private static Boolean extractBooleanFromObject(Object obj, List<String> methodNames) {
        return methodNames.stream()
                .map(methodName -> getValueFromObject(obj, methodName))
                .filter(StringUtils::isNotEmpty)
                .findFirst()
                .map(BooleanUtils::toBoolean)
                .orElse(null);
    }

    /**
     * 从Map中提取字符串值
     */
    private static String extractStringFromMap(Map<String, Object> map, List<String> fieldNames) {
        return fieldNames.stream()
                .map(map::get)
                .filter(Objects::nonNull)
                .findFirst()
                .map(Object::toString)
                .orElse(null);
    }

    /**
     * 从JSONObject中提取字符串值
     */
    private static String extractStringFromJsonObject(JSONObject jsonObject, List<String> fieldNames) {
        return fieldNames.stream()
                .map(jsonObject::get)
                .filter(Objects::nonNull)
                .findFirst()
                .map(Object::toString)
                .orElse(null);
    }

    /**
     * 从对象中通过反射提取字符串值
     */
    private static String extractStringFromObject(Object obj, List<String> methodNames) {
        return methodNames.stream()
                .map(methodName -> getValueFromObject(obj, methodName))
                .filter(StringUtils::isNotEmpty)
                .findFirst()
                .orElse(null);
    }

    /**
     * 通过反射获取对象的方法值
     */
    private static String getValueFromObject(Object obj, String methodName) {
        try {
            Method method = obj.getClass().getMethod(methodName);
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            log.debug("Failed to invoke method {} on object {}", methodName, obj.getClass().getSimpleName(), e);
            return null;
        }
    }
}
