syntax = "proto3";

package com.taobao.wireless.orange.common.model;

import "common.proto";

option java_package = "com.taobao.wireless.orange.common.model.proto";
option java_outer_classname = "ReleaseConfigOuterClass";
option java_multiple_files = true;

message ReleaseConfigProto {
  // 协议版本，用于指导解析配置文件
  string schema_version = 1;

  // 命名空间名称
  string namespace = 2;

  // 正式配置存在差量和增量两种情况：FULL/INCREMENTAL
  ConfigStrategyProto strategy = 3;

  // 配置类型
  ConfigTypeProto type = 4;

  // 删除的参数
  repeated string offline_parameters = 5;

  // 条件
  repeated ConditionProto conditions = 6;

  // 参数
  repeated ParameterProto parameters = 7;
}


