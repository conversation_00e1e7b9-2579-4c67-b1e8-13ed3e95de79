package com.taobao.wireless.orange.common.util;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

class SnowflakeTest {

    private Snowflake snowflake;
    private DateTimeFormatter formatter;

    @BeforeEach
    public void setUp() {
        snowflake = new Snowflake();
        formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS").withZone(ZoneId.systemDefault());
    }

    @Test
    public void testNextIdGeneration() {
        // 测试生成的ID不为空且为正数
        long id = snowflake.nextId();
        Assertions.assertThat(id).isGreaterThan(0);
    }

    @Test
    public void testUniqueIdGeneration() {
        // 测试生成的ID唯一性
        Set<Long> ids = new HashSet<>();
        int count = 1000;

        for (int i = 0; i < count; i++) {
            long id = snowflake.nextId();
            Assertions.assertThat(ids.add(id)).isTrue();
        }

        Assertions.assertThat(ids.size()).isEqualTo(count);
    }

    @Test
    public void testParseGenerationTime() {
        // 生成一个ID并解析其时间
        long beforeGeneration = System.currentTimeMillis();
        long id = snowflake.nextId();
        long afterGeneration = System.currentTimeMillis();

        Date parsedTime = snowflake.parseGenerationTime(id);

        // 验证解析出的时间在合理范围内（考虑到毫秒级精度）
        Assertions.assertThat(parsedTime.getTime()).isBetween(beforeGeneration - 1000, afterGeneration + 1000);
    }

    @Test
    public void testParseGenerationTimeAccuracy() {
        // 测试时间解析的准确性
        long id = snowflake.nextId();
        Date parsedTime = snowflake.parseGenerationTime(id);

        // 从ID中提取时间戳字符串
        String idStr = String.valueOf(id);
        String timestampStr = idStr.substring(0, 17);

        // 手动解析时间戳进行对比
        LocalDateTime expectedDateTime = LocalDateTime.parse(timestampStr, formatter);
        Date expectedTime = Date.from(expectedDateTime.atZone(ZoneId.systemDefault()).toInstant());

        Assertions.assertThat(parsedTime).isEqualTo(expectedTime);
    }

    @Test
    public void testParseGenerationTimeWithInvalidLength() {
        // 测试无效长度的ID
        Assertions.assertThatExceptionOfType(IllegalArgumentException.class)
                .isThrownBy(() -> snowflake.parseGenerationTime(123L)).withMessageContaining("Failed to parse Snowflake ID: 123");

        Assertions.assertThatExceptionOfType(IllegalArgumentException.class)
                .isThrownBy(() -> snowflake.parseGenerationTime(1234567890123456789l)).withMessageContaining("Failed to parse Snowflake ID: 1234567890123456789");
    }

    @Test
    public void testParseGenerationTimeWithInvalidFormat() {
        // 测试无效格式的ID（长度正确但格式错误）
        String invalidId = "1234567890123456789"; // 19位但不是有效的时间格式

        Assertions.assertThatExceptionOfType(IllegalArgumentException.class).isThrownBy(() -> snowflake.parseGenerationTime(Long.parseLong(invalidId))).withMessageContaining("Failed to parse Snowflake ID: " + invalidId);
    }

    @Test
    public void testMultipleIdsParseCorrectly() {
        // 测试多个ID的时间解析
        int count = 100;
        long[] ids = new long[count];
        Date[] parsedTimes = new Date[count];

        // 生成多个ID
        for (int i = 0; i < count; i++) {
            ids[i] = snowflake.nextId();
            parsedTimes[i] = snowflake.parseGenerationTime(ids[i]);
        }

        // 验证时间顺序（应该是递增的或相等的）
        for (int i = 1; i < count; i++) {
            Assertions.assertThat(parsedTimes[i]).isAfterOrEqualTo(parsedTimes[i - 1]);
        }
    }

    @Test
    public void testIdFormatConsistency() {
        // 测试ID格式的一致性
        for (int i = 0; i < 10; i++) {
            long id = snowflake.nextId();
            String idStr = String.valueOf(id);

            // 验证ID长度
            Assertions.assertThat(idStr.length()).isEqualTo(19);

            // 验证前17位是有效的时间格式
            String timestampPart = idStr.substring(0, 17);
            Assertions.assertThat(timestampPart.length()).isEqualTo(17);

            // 验证后2位是数字
            String suffixPart = idStr.substring(17);
            Assertions.assertThat(suffixPart.length()).isEqualTo(2);
            Assertions.assertThat(suffixPart).matches("\\d{2}");
        }
    }

    @Test
    public void testRoundTripConsistency() {
        // 测试生成ID和解析时间的往返一致性
        for (int i = 0; i < 50; i++) {
            long originalId = snowflake.nextId();
            Date parsedTime = snowflake.parseGenerationTime(originalId);

            // 从解析出的时间重新构造时间戳字符串
            String reconstructedTimestamp = formatter.format(parsedTime.toInstant());

            // 验证重构的时间戳与原ID的时间戳部分一致
            String originalTimestamp = String.valueOf(originalId).substring(0, 17);
            Assertions.assertThat(originalTimestamp).isEqualTo(reconstructedTimestamp);
        }
    }

}