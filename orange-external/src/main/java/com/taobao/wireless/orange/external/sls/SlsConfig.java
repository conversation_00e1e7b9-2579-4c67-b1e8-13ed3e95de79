package com.taobao.wireless.orange.external.sls;

import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import com.alibaba.normandy.credential.param.ResourceConfigHelper;
import com.alibaba.normandy.credential.param.endpoint.EndpointType;
import com.aliyun.openservices.log.Client;
import com.taobao.wireless.orange.common.util.EnvUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SlsConfig {

    @Autowired
    private CredentialProvider credentialProvider;

    @Bean
    public Client slsClient() {
        if (EnvUtil.isDaily()) {
            // 日常无 SLS 资源
            return new Client("mock.log.aliyuncs.com", "mock", "mock");
        }

        String projectName = "orange";
        String rn = ResourceNames.ofAliyunSlsProjectName(projectName);
        Credential credential = credentialProvider.getCredential(rn);

        String resourceName = ResourceNames.ofAliyunSlsProjectName(projectName);
        String endpoint = ResourceConfigHelper.getEndpoint(resourceName, EndpointType.INTRANET);

        return new Client(
                endpoint,
                credential.getAccessKeyId(),
                credential.getAccessKeySecret()
        );
    }
}
