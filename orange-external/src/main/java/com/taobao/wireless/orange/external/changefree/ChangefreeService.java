package com.taobao.wireless.orange.external.changefree;

import com.alibaba.change.core2.hsf.pojo.ChangeEndReqDTO;
import com.alibaba.change.core2.hsf.pojo.ChangeResult;
import com.alibaba.change.core2.hsf.pojo.ChangeStartReqDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.goc.changefree.ChangeFreeClient;
import com.alibaba.goc.changefree.builder.ChangeCheckReqBuilder;
import com.alibaba.goc.changefree.builder.ChangeEndReqBuilder;
import com.alibaba.goc.changefree.builder.ChangeQueryReqBuilder;
import com.alibaba.goc.changefree.model.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.external.exception.ServiceExceptionHandler;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.common.util.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

/**
 * changefree 服务
 * <p>
 * 接入文档：https://yuque.alibaba-inc.com/changefree/sop/wrh65i
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChangefreeService {
    @Autowired
    private ChangeFreeClient changeFreeClient;

    @Autowired
    private EnvUtil envUtil;

    private static final String CHANGEFREE_CONFIG_PUSH = "CONFIG_PUSH";

    /**
     * 校验本次变更是否需要审批
     *
     * @param sourceOrderId
     * @param changeObject
     * @param title
     * @param detailUrl
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.CHANGEFREE_CHECK_ERROR)
    public ChangeCheckRes check(String sourceOrderId, GeneralChangeObject changeObject,
                                Map<String, Object> extraInfo, String title, String detailUrl, String notifyUrl) {
        EnvEnum env = switch (envUtil.currEnv()) {
            case DAILY -> EnvEnum.DAILY;
            case PRE -> EnvEnum.PRE;
            case PROD -> EnvEnum.PRODUCTION;
        };

        final ChangeCheckReq changeCheckReq = ChangeCheckReqBuilder.aChangeCheckReq()
                .changeStartTime(Long.toString(System.currentTimeMillis()))
                .env(env)
                .changeTypeKey(CHANGEFREE_CONFIG_PUSH)
                .notifyUrl(notifyUrl)
                .changeTitle(title)
                .changeObjectLevel(ChangeLevelEnum.GRADE1)
                .generalChangeObject(changeObject)
                .executorEmpId(ThreadContextUtil.getWorkerId())
                .sourceOrderId(sourceOrderId)
                .hasSelfProcess("false")
                .detailUrl(detailUrl)
                .build();

        if (extraInfo != null) {
            changeCheckReq.setExtraInfo(JSONObject.toJSONString(extraInfo));
        }

        ChangeCheckResWithErrorCode result = changeFreeClient.checkSyncV2(changeCheckReq);
        if (result == null || result.getChangeCheckRes() == null || !result.isSuccessful()) {
            String resultStr = Optional.ofNullable(result).map(JSONObject::toJSONString).orElse("null");
            log.error("Changefree checkSyncV2 fail, req={}, res={}", JSONObject.toJSONString(changeCheckReq), resultStr);
            throw new CommonException(ExceptionEnum.CHANGEFREE_CHECK_ERROR);
        }
        return result.getChangeCheckRes();
    }

    /**
     * 查询 Changefree 单状态
     *
     * @param sourceOrderId
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.CHANGEFREE_QUERY_ERROR)
    public ChangeQueryRes query(String sourceOrderId) {
        final ChangeQueryReq changeQueryReq = ChangeQueryReqBuilder.aChangeQueryReq()
                .sourceOrderId(sourceOrderId)
                .build();

        ChangeQueryRes res = changeFreeClient.query(changeQueryReq);
        if (res == null) {
            throw new CommonException(ExceptionEnum.CHANGEFREE_QUERY_ERROR);
        }
        return res;
    }


    /**
     * 结束变更
     *
     * @param sourceOrderId
     * @param changeResult
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.CHANGEFREE_END_ERROR)
    public ChangeBaseResponse end(String sourceOrderId, ChangeResult changeResult) {
        final ChangeEndReqDTO changeEndReq = ChangeEndReqBuilder.aChangeCheckReq()
                .sourceOrderId(sourceOrderId)
                .changeResult(changeResult)
                .executorEmpId(ThreadContextUtil.getWorkerId())
                .changeEndTime(System.currentTimeMillis())
                .build();

        ChangeBaseResponse result = changeFreeClient.changeEnd(changeEndReq);
        if (result == null || !result.isSuccessful()) {
            String resultStr = Optional.ofNullable(result).map(JSONObject::toJSONString).orElse("null");
            log.error("Changefree changeEnd fail, req={}, res={}", JSONObject.toJSONString(changeEndReq), resultStr);
            throw new CommonException(ExceptionEnum.CHANGEFREE_END_ERROR);
        }
        return result;
    }

    @ServiceExceptionHandler(ExceptionEnum.CHANGEFREE_START_ERROR)
    public ChangeBaseResponse start(ChangeStartReqDTO changeStartReq) {
        // 默认值填充
        changeStartReq.setExecutorEmpId(ThreadContextUtil.getWorkerId());
        changeStartReq.setCreatorEmpId(ThreadContextUtil.getWorkerId());
        changeStartReq.setChangeStartTime(System.currentTimeMillis());
        changeStartReq.setChangeTypeKey(CHANGEFREE_CONFIG_PUSH);

        ChangeBaseResponse result = changeFreeClient.changeStart(changeStartReq);
        if (result == null || !result.isSuccessful()) {
            String resultStr = Optional.ofNullable(result).map(JSONObject::toJSONString).orElse("null");
            log.error("Changefree changeStart fail, req={}, res={}", JSONObject.toJSONString(changeStartReq), resultStr);
            throw new CommonException(ExceptionEnum.CHANGEFREE_START_ERROR);
        }
        return result;
    }
}
