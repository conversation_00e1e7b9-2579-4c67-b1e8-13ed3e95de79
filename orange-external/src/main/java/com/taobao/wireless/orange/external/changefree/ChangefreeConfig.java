package com.taobao.wireless.orange.external.changefree;

import com.alibaba.goc.changefree.ChangeFreeClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ChangefreeConfig {

    @Value("${orange.changefree.key}")
    private String authKey;

    @Value("${orange.changefree.token}")
    private String authToken;

    @Value("${orange.changefree.gateway}")
    private String changefreeGateway;

    @Bean("changeFreeClient")
    public ChangeFreeClient changeFreeClient() {
        return new ChangeFreeClient(authKey, authToken, changefreeGateway);
    }
}
