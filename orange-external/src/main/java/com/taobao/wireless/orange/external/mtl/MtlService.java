package com.taobao.wireless.orange.external.mtl;

import com.alibaba.emas.mtl4.commons.utils.httpclient.HttpMethod;
import com.alibaba.emas.mtl4.commons.utils.httpclient.RequestMessage;
import com.alibaba.emas.mtl4.commons.utils.httpclient.ResponseMessage;
import com.alibaba.fastjson.JSON;
import com.alibaba.mtl.generic.open.sdk.consumer.ApiClient;
import com.alibaba.mtl.generic.open.sdk.consumer.ClientConfiguration;
import com.alibaba.mtl.open.protocol.entry.EntryToken;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.external.mtl.model.MtlBaseResult;
import com.taobao.wireless.orange.external.mtl.model.MtlModule;
import com.taobao.wireless.orange.external.mtl.model.MtlPaginationResult;
import com.taobao.wireless.orange.external.mtl.model.MtlResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MTL服务客户端
 * 提供MTL模块查询相关功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MtlService {

    // API路径常量
    private static final String API_MODULES_SEARCH = "/dev/api/v1/sre/%s/modules";
    private static final String API_MODULES_BATCH = "/dev/api/v1/sre/modules";

    // 参数名常量
    private static final String PARAM_KEYWORD = "keyword";
    private static final String PARAM_PAGE_NUM = "pageNum";
    private static final String PARAM_PAGE_SIZE = "pageSize";
    private static final String PARAM_IDS = "ids";

    // HTTP状态码常量
    private static final int HTTP_OK = 200;

    // 分隔符常量
    private static final String ID_SEPARATOR = ",";

    private final ApiClient apiClient;

    private final Gson gson = new Gson();

    public MtlService(
            @Value("${mtl.endpoint}") String endpoint,
            @Value("${mtl.accessKey}") String accessKey,
            @Value("${mtl.accessSecret}") String accessSecret
    ) {
        try {
            if (StringUtils.isBlank(endpoint)) {
                throw new CommonException(ExceptionEnum.MTL_ENDPOINT_INVALID);
            }
            if (StringUtils.isBlank(accessKey)) {
                throw new CommonException(ExceptionEnum.MTL_ACCESS_KEY_BLANK);
            }
            if (StringUtils.isBlank(accessSecret)) {
                throw new CommonException(ExceptionEnum.MTL_ACCESS_SECRET_BLANK);
            }

            EntryToken entryToken = EntryToken.builder()
                    .identifier(accessKey)
                    .token(accessSecret)
                    .build();

            ClientConfiguration clientConfiguration = new ClientConfiguration();
            // 如需配置超时等，请在后续明确 API 后添加
            this.apiClient = new ApiClient(accessKey, new URI(endpoint), entryToken, clientConfiguration);
            log.info("MTL客户端初始化成功，endpoint: {}", endpoint);
        } catch (URISyntaxException e) {
            log.error("MTL客户端初始化失败，无效的endpoint: {}", endpoint, e);
            throw new CommonException(ExceptionEnum.MTL_ENDPOINT_INVALID, e);
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("MTL客户端初始化失败", e);
            throw new CommonException(ExceptionEnum.MTL_INIT_ERROR, e);
        }
    }

    /**
     * 搜索模块列表
     *
     * @param appKey     应用Key，不能为空
     * @param keyword    搜索关键词，不能为空
     * @param pagination 分页参数，可为空
     * @return 分页结果
     * @throws CommonException 当参数无效或调用失败时抛出
     */
    public PaginationResult<MtlModule> queryModules(String appKey, String keyword, Pagination pagination) {
        // 参数校验
        if (StringUtils.isBlank(appKey) || StringUtils.isBlank(keyword)) {
            throw new CommonException(ExceptionEnum.MTL_SEARCH_PARAMS_INVALID);
        }

        // 构建API路径
        String api = String.format(API_MODULES_SEARCH, appKey);

        // 构建请求参数
        Map<String, String> parameters = buildSearchParameters(keyword, pagination);

        // 构建请求
        RequestMessage request = RequestMessage.builder()
                .method(HttpMethod.GET)
                .path(api)
                .parameters(parameters)
                .build();

        Type type = new TypeToken<MtlPaginationResult<MtlModule>>() {}.getType();

        // 调用MTL接口
        MtlPaginationResult<MtlModule> mtlResult = (MtlPaginationResult<MtlModule>) requestMtl(request, type);

        // 转换结果
        return convertToPaginationResult(mtlResult);
    }

    /**
     * 构建搜索请求参数
     */
    private Map<String, String> buildSearchParameters(String keyword, Pagination pagination) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put(PARAM_KEYWORD, keyword);

        if (pagination != null) {
            if (pagination.getPageNum() > 0) {
                // MTL页码从0开始，需要减1
                parameters.put(PARAM_PAGE_NUM, String.valueOf(pagination.getPageNum() - 1));
            }
            if (pagination.getPageSize() > 0) {
                parameters.put(PARAM_PAGE_SIZE, String.valueOf(pagination.getPageSize()));
            }
        }

        return parameters;
    }

    /**
     * 转换MTL分页结果为通用分页结果
     */
    private PaginationResult<MtlModule> convertToPaginationResult(MtlPaginationResult<MtlModule> mtlResult) {
        PaginationResult<MtlModule> result = new PaginationResult<>();
        if (mtlResult == null) {
            result.setCurrent(0L);
            result.setTotal(0L);
            result.setSize(0L);
            result.setData(Collections.emptyList());
            return result;
        }
        int pageNumZeroBased = mtlResult.getPageNum() == null ? 0 : mtlResult.getPageNum();
        int pageSize = mtlResult.getPageSize() == null ? 0 : mtlResult.getPageSize();
        long totalCount = mtlResult.getTotalCount() == null ? 0L : mtlResult.getTotalCount();
        result.setCurrent((long) pageNumZeroBased + 1);
        result.setTotal(totalCount);
        result.setSize((long) pageSize);
        result.setData(mtlResult.getData() == null ? Collections.emptyList() : mtlResult.getData());
        return result;
    }

    /**
     * 根据模块 ID 批量查询模块详情
     *
     * @param moduleIds 模块ID列表，不能为空
     * @return 模块ID到模块信息的映射
     * @throws CommonException 当调用失败时抛出
     */
    public Map<Long, MtlModule> getModulesByModuleIds(List<Long> moduleIds) {
        if (CollectionUtils.isEmpty(moduleIds)) {
            return new HashMap<>(0);
        }

        // 构建请求参数
        String ids = moduleIds.stream()
                .map(Object::toString)
                .collect(Collectors.joining(ID_SEPARATOR));
        Map<String, String> parameters = Collections.singletonMap(PARAM_IDS, ids);

        // 构建请求
        RequestMessage request = RequestMessage.builder()
                .method(HttpMethod.GET)
                .path(API_MODULES_BATCH)
                .parameters(parameters)
                .build();

        Type type = new TypeToken<MtlResult<List<MtlModule>>>() {}.getType();

        // 调用MTL接口
        MtlResult<List<MtlModule>> result = (MtlResult<List<MtlModule>>) requestMtl(request, type);

        // 转换为Map结构
        List<MtlModule> modules = (result == null || result.getData() == null) ? Collections.emptyList() : result.getData();
        return modules.stream()
                .collect(Collectors.toMap(MtlModule::getModuleId, module -> module, (a, b) -> a));
    }

    /**
     * 调用 MTL 服务，并对异常结果进行处理
     *
     * @param request 请求消息
     * @param type    响应类型
     * @return MTL响应结果
     * @throws CommonException 当调用失败或解析失败时抛出
     */
    private MtlBaseResult requestMtl(RequestMessage request, Type type) {
        try {
            ResponseMessage responseMessage = this.apiClient.sendRequest(request);
            int statusCode = responseMessage.getStatusCode();
            if (statusCode < 200 || statusCode >= 300) {
                log.error("调用 MTL 接口 HTTP 失败，statusCode: {}，path: {}，params: {}", statusCode, request.getPath(), JSON.toJSONString(request.getParameters()));
                throw CommonException.getDynamicException(ExceptionEnum.MTL_HTTP_ERROR, statusCode);
            }
            String resultStr = responseMessage.getJson();
            MtlBaseResult result = gson.fromJson(resultStr, type);
            if (result == null || result.getSuccess() == null || !result.getSuccess()) {
                log.error("调用 MTL 接口业务失败，path: {}，params: {}，结果：{}", request.getPath(), JSON.toJSONString(request.getParameters()), resultStr);
                String errorMsg = (result == null) ? "空响应" : result.getErrorMsg();
                throw CommonException.getDynamicException(ExceptionEnum.MTL_BUSINESS_ERROR, errorMsg);
            }
            return result;
        } catch (JsonSyntaxException e) {
            log.error("解析 MTL 返回结果失败，path: {}，params: {}", request.getPath(), JSON.toJSONString(request.getParameters()), e);
            throw new CommonException(ExceptionEnum.MTL_PARSE_ERROR, e);
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用 MTL 接口异常，path: {}，params: {}", request.getPath(), JSON.toJSONString(request.getParameters()), e);
            throw new CommonException(ExceptionEnum.MTL_ERROR, e);
        }
    }
}
