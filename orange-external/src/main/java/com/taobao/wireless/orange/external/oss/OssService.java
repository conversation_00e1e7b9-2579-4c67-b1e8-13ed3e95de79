package com.taobao.wireless.orange.external.oss;

import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import com.alibaba.normandy.credential.param.ResourceConfigHelper;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;

@Service
@Slf4j
public class OssService {

    private final OSS ossClient;
    private final String bucketName;

    public OssService(
            CredentialProvider credentialProvider,
            @Value("${orange.oss.bucketName}") String bucketName
    ) {
        this.bucketName = bucketName;

        String resourceName = ResourceNames.ofAliyunOssBucketName(bucketName);
        Credential credential = credentialProvider.getCredential(resourceName);
        String accessKeyId = credential.getAccessKeyId();
        String accessKeySecret = credential.getAccessKeySecret();
        String endpoint = ResourceConfigHelper.getEndpoint(resourceName);
        this.ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        log.info("OSS client initialized. bucketName={}, endpoint={}", bucketName, endpoint);
    }

    public byte[] readData(String objectName) {
        validateObjectName(objectName);
        try (var obj = ossClient.getObject(bucketName, objectName);
             var in = obj.getObjectContent()) {
            return in.readAllBytes();
        } catch (Exception e) {
            log.error("OSS readData error, objectName={}", objectName, e);
            throw new CommonException(ExceptionEnum.OSS_READ_ERROR, e);
        }
    }

    public void uploadData(byte[] dataBytes, String objectName) {
        validateObjectName(objectName);
        if (dataBytes == null) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "OSS 文件内容不能为空");
        }
        try (var in = new ByteArrayInputStream(dataBytes)) {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(dataBytes.length);
            ossClient.putObject(bucketName, objectName, in, metadata);
            log.info("OSS upload success, objectName={}, size={}B", objectName, dataBytes.length);
        } catch (Exception e) {
            log.error("OSS uploadData error, objectName=" + objectName, e);
            throw new CommonException(ExceptionEnum.OSS_UPLOAD_ERROR, e);
        }
    }

    private void validateObjectName(String objectName) {
        if (StringUtils.isBlank(objectName)) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "OSS 文件名不能为空");
        }
        if (objectName.startsWith("/")) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "OSS 文件名不能以 '/' 开头");
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            if (ossClient != null) {
                ossClient.shutdown();
                log.info("OSS client shutdown successfully");
            }
        } catch (Exception e) {
            log.warn("OSS client shutdown failed", e);
        }
    }
}