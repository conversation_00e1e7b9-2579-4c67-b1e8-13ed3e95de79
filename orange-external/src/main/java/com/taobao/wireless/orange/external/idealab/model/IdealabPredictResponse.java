package com.taobao.wireless.orange.external.idealab.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Idealab 预测响应对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdealabPredictResponse {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 响应数据
     */
    private IdealabPredictResponseData data;

    @Data
    public static class IdealabPredictResponseData {
        private String content;
    }
}
