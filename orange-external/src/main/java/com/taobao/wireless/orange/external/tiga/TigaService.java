package com.taobao.wireless.orange.external.tiga;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.common.util.UserUtil;
import com.taobao.wireless.orange.external.exception.ServiceExceptionHandler;
import com.taobao.wireless.tiga.base.model.PageResultWrapper;
import com.taobao.wireless.tiga.base.model.ServiceExceptionCode;
import com.taobao.wireless.tiga.base.model.TigaClientInfo;
import com.taobao.wireless.tiga.base.model.TigaResult;
import com.taobao.wireless.tiga.release.common.task.TaskCmd;
import com.taobao.wireless.tiga.release.common.template.TemplateType;
import com.taobao.wireless.tiga.release.console.api.device.DeviceQueryApiService;
import com.taobao.wireless.tiga.release.console.api.device.param.GrayscaleDeviceQueryParam;
import com.taobao.wireless.tiga.release.console.api.task.TaskReadApiService;
import com.taobao.wireless.tiga.release.console.api.task.TaskWriteApiService;
import com.taobao.wireless.tiga.release.console.api.task.model.dto.TaskBriefDTO;
import com.taobao.wireless.tiga.release.console.api.task.model.dto.TaskStageListDTO;
import com.taobao.wireless.tiga.release.console.api.task.model.param.TaskApiQueryParam;
import com.taobao.wireless.tiga.release.console.api.task.model.param.TaskCmdParam;
import com.taobao.wireless.tiga.release.console.api.task.model.param.TaskCreateParam;
import com.taobao.wireless.tiga.release.console.api.template.TemplateReadApiService;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateInstanceDTO;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateMetaDTO;
import com.taobao.wireless.tiga.release.console.api.template.model.param.TemplatePageQueryParam;
import com.taobao.wireless.tiga.release.expression.ExpressionParser;
import com.taobao.wireless.tiga.release.expression.LogicExpression;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * MTL服务客户端
 * 提供MTL模块查询相关功能
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TigaService {

    @HSFConsumer(serviceVersion = "${tiga.hsf.version}", serviceGroup = "${tiga.hsf.group}", clientTimeout = 10000)
    private TaskReadApiService taskReadApiService;

    @HSFConsumer(serviceVersion = "${tiga.hsf.version}", serviceGroup = "${tiga.hsf.group}", clientTimeout = 10000)
    private TemplateReadApiService templateReadApiService;

    @HSFConsumer(serviceVersion = "${tiga.hsf.version}", serviceGroup = "${tiga.hsf.group}", clientTimeout = 10000)
    private DeviceQueryApiService deviceQueryApiService;

    @HSFConsumer(serviceVersion = "${tiga.hsf.version}", serviceGroup = "${tiga.hsf.group}", clientTimeout = 10000)
    private TaskWriteApiService taskWriteApiService;

    // 设备最小活跃时间（毫秒）- 默认1天
    private static final long MIN_LAST_ALIVE_TIME = TimeUnit.DAYS.toMillis(1);

    @Value("${tiga.token}")
    private String token;

    private static final String PLATFORM = "orange";

    /**
     * 获取模板列表
     *
     * @param param
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.TIGA_GET_TEMPLATE_LIST_EXCEPTION)
    private List<TemplateMetaDTO> getTemplateList(@NonNull TemplatePageQueryParam param) {
        param.setPageSize(1000);
        param.setPlatform(PLATFORM);

        TigaResult<PageResultWrapper<TemplateMetaDTO>> result = templateReadApiService.getTemplateList(param, getTigaClientInfo());
        if (!result.isSuccess()) {
            throw CommonException.getDynamicException(ExceptionEnum.TIGA_GET_TEMPLATE_LIST_ERROR, result.getErrorMsg());
        }
        PageResultWrapper<TemplateMetaDTO> page = result.getModel();
        return page == null || page.getItems() == null ? List.of() : page.getItems();
    }

    /**
     * 获取模板实例详情
     *
     * @param templateId
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.TIGA_GET_TEMPLATE_INSTANCE_EXCEPTION)
    public TemplateInstanceDTO getTemplateCurrentInstanceDetail(Long templateId) {
        TigaResult<TemplateInstanceDTO> result = templateReadApiService.getTemplateCurrentInstanceDetail(templateId, getTigaClientInfo());
        if (!result.isSuccess()) {
            throw CommonException.getDynamicException(ExceptionEnum.TIGA_GET_TEMPLATE_INSTANCE_ERROR, result.getErrorMsg());
        }
        return result.getModel();
    }

    /**
     * 获取默认模板示例
     *
     * @return
     */
    public TemplateInstanceDTO getDefaultTemplateInstance(String appKey, String actionType) {
        TemplatePageQueryParam param = new TemplatePageQueryParam();
        param.setActionType(actionType);
        param.setType(TemplateType.DEFAULT);
        param.setAppKey(appKey);

        List<TemplateMetaDTO> result = this.getTemplateList(param);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }

        TemplateMetaDTO first = result.getFirst();
        if (first == null) {
            return null;
        }
        return getTemplateCurrentInstanceDetail(first.getId());
    }

    /**
     * 创建任务
     *
     * @param taskCreateParam
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.TIGA_CREATE_TASK_EXCEPTION)
    public Long createTask(TaskCreateParam taskCreateParam) {
        taskCreateParam.setPlatform(PLATFORM);

        TigaResult<Long> task = taskWriteApiService.createTask(taskCreateParam, getTigaClientInfo());
        if (!task.isSuccess()) {
            if (ServiceExceptionCode.EXCEPTION_ORDER_EXIST.getCode() != task.getErrorCode()) {
                throw CommonException.getDynamicException(ExceptionEnum.TIGA_CREATE_TASK_ERROR, task.getErrorMsg());
            }

            // 幂等兼容（支持"创建发布单成功，但是 START 失败"的场景进行重试恢复）
            Long taskId = getTaskIdBySourceOrderId(taskCreateParam.getSourceOrderId(), taskCreateParam.getActionType());
            if (taskId == null) {
                throw new CommonException(ExceptionEnum.TIGA_GET_TASK_LIST_EMPTY);
            }
            return taskId;
        }
        return task.getModel();
    }

    /**
     * 执行任务
     *
     * @param taskId
     * @param cmd
     */
    @ServiceExceptionHandler(ExceptionEnum.TIGA_DO_TASK_EXCEPTION)
    public void doTask(Long taskId, TaskCmd cmd, Long skipStageId) {
        TaskCmdParam taskCmdParam = new TaskCmdParam();
        taskCmdParam.setTaskId(taskId);
        taskCmdParam.setSkipStageId(skipStageId);
        taskCmdParam.setCmd(cmd);

        TigaResult<Boolean> result = taskWriteApiService.doTask(taskCmdParam, getTigaClientInfo());
        if (!result.isSuccess() && ServiceExceptionCode.NOT_SUPPORT_CMD.getCode() == result.getErrorCode()) {
            log.info("Tiga doTask not support cmd, taskId: {}, cmd: {}", taskId, cmd);
        } else if (!result.isSuccess() || Boolean.FALSE.equals(result.getModel())) {
            throw CommonException.getDynamicException(ExceptionEnum.TIGA_DO_TASK_ERROR, result.getErrorMsg());
        }
    }


    /**
     * 获取任务阶段列表
     *
     * @param taskId
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.TIGA_GET_TASK_STAGE_LIST_EXCEPTION)
    public TaskStageListDTO getTaskStageList(Long taskId) {
        TigaResult<TaskStageListDTO> result = taskReadApiService.getTaskStageList(taskId, getTigaClientInfo());
        if (!result.isSuccess()) {
            throw CommonException.getDynamicException(ExceptionEnum.TIGA_GET_TASK_STAGE_LIST_ERROR, result.getErrorMsg());
        }
        return result.getModel();
    }

    /**
     * 获取设备数
     *
     * @param appKey
     * @param expression
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.TIGA_GET_GRAYSCALE_DEVICE_COUNT_EXCEPTION)
    public Long getGrayscaleDeviceCount(String appKey, LogicExpression expression) {
        GrayscaleDeviceQueryParam param = GrayscaleDeviceQueryParam.builder()
                .appKey(appKey)
                .grayCondition(ExpressionParser.toStandardJson(expression))
                .minLastAliveTime(System.currentTimeMillis() - MIN_LAST_ALIVE_TIME)
                .build();

        TigaResult<Long> result = deviceQueryApiService.getGrayscaleDeviceCount(param, getTigaClientInfo());

        if (!result.isSuccess()) {
            throw CommonException.getDynamicException(ExceptionEnum.TIGA_GET_GRAYSCALE_DEVICE_COUNT_ERROR, result.getErrorMsg());
        }
        Long count = result.getModel();
        return count == null ? 0L : count;
    }

    /**
     * 根据 sourceOrderId 获取任务 ID
     *
     * @param sourceOrderId
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.TIGA_GET_TASK_LIST_EXCEPTION)
    private Long getTaskIdBySourceOrderId(String sourceOrderId, String actionType) {
        TaskApiQueryParam taskApiQueryParam = new TaskApiQueryParam();
        taskApiQueryParam.setSourceOrderId(sourceOrderId);
        taskApiQueryParam.setPlatform(PLATFORM);
        taskApiQueryParam.setActionType(actionType);

        TigaResult<PageResultWrapper<TaskBriefDTO>> result = taskReadApiService.getTaskBriefList(taskApiQueryParam, getTigaClientInfo());

        if (!result.isSuccess()) {
            throw CommonException.getDynamicException(ExceptionEnum.TIGA_GET_TASK_LIST_ERROR, result.getErrorMsg());
        }

        PageResultWrapper<TaskBriefDTO> page = result.getModel();
        if (page == null || CollectionUtils.isEmpty(page.getItems())) {
            return null;
        }

        TaskBriefDTO first = page.getItems().getFirst();
        return first == null ? null : first.getId();
    }

    private TigaClientInfo getTigaClientInfo() {
        TigaClientInfo tigaClientInfo = new TigaClientInfo();
        tigaClientInfo.setPlatform(PLATFORM);
        tigaClientInfo.setToken(token);
        String workerId = ThreadContextUtil.getWorkerId();
        tigaClientInfo.setOperator(UserUtil.formatWorkerIdWithoutZero(workerId));
        return tigaClientInfo;
    }
}
