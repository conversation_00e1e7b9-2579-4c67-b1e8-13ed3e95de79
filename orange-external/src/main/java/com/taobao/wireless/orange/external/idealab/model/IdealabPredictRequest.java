package com.taobao.wireless.orange.external.idealab.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Idealab 预测请求对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdealabPredictRequest {

    /**
     * 员工ID
     */
    private String empId;

    /**
     * 媒体实体列表
     */
    private List<MediaEntity> mediaEntities;

    /**
     * 问题
     */
    private String question;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 是否流式返回
     */
    private Boolean stream;

    /**
     * 变量映射
     */
    private Map<String, Object> variableMap;

    /**
     * 媒体实体
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MediaEntity {
        /**
         * 内容URL
         */
        @JSONField(name = "content")
        private String content;
    }
}
