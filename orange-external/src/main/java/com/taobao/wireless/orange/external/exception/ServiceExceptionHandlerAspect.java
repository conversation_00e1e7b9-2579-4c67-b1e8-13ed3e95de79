package com.taobao.wireless.orange.external.exception;

import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 外部服务层异常处理切面
 * 统一处理带有 @ExceptionHandler 注解的方法异常
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ServiceExceptionHandlerAspect {

    @Around("@annotation(serviceExceptionHandler)")
    public Object handleException(ProceedingJoinPoint joinPoint, ServiceExceptionHandler serviceExceptionHandler) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            ExceptionEnum exceptionEnum = serviceExceptionHandler.value();
            log.error("方法执行异常: " + joinPoint.getSignature().toShortString(), e);
            throw new CommonException(exceptionEnum, e);
        }
    }
}