package com.taobao.wireless.orange.external.exception;

import com.taobao.wireless.orange.common.exception.ExceptionEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 异常处理注解
 * 为方法提供统一的异常处理，将普通异常转换为 CommonException
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ServiceExceptionHandler {

    /**
     * 异常转换时使用的 ExceptionEnum
     * @return ExceptionEnum
     */
    ExceptionEnum value();
}