package com.taobao.wireless.orange.external.keycenter;

import com.alibaba.keycenter.client.properties.KeyCenterProperties;
import com.taobao.common.keycenter.keystore.KeyStore;
import com.taobao.common.keycenter.keystore.KeyStoreImpl;
import com.taobao.common.keycenter.security.Cryptograph;
import com.taobao.common.keycenter.security.CryptographImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class KeyCenterConfig {
    @Value("${key.center.http.service}")
    private String httpServiceAddress;

    @Value("${key.center.app.publish}")
    private String appPublishNum;

    @Bean(name = "keyStore", initMethod = "init")
    public KeyStore initKeyStore() {
        KeyCenterProperties keyCenterProperties = new KeyCenterProperties();
        keyCenterProperties.setAppPublishNum(appPublishNum);
        keyCenterProperties.setHttpServiceAddress(httpServiceAddress);
        keyCenterProperties.setPreferProtocal("http");

        KeyStoreImpl keyStore = new KeyStoreImpl();
        keyStore.setKeyCenterProperties(keyCenterProperties);
        return keyStore;
    }

    /**
     * 加解密服务。按需，若不用加解密服务可以删除。
     *
     * @param keyStore
     * @return Cryptograph
     */
    @Bean(name = "cryptograph")
    public Cryptograph initCryptoGraph(KeyStore keyStore) {
        return new CryptographImpl(keyStore);
    }
}
