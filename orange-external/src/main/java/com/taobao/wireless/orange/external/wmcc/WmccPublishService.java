package com.taobao.wireless.orange.external.wmcc;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.external.exception.ServiceExceptionHandler;
import com.taobao.wmcc.client.Result;
import com.taobao.wmcc.client.constants.BriefTaskStatus;
import com.taobao.wmcc.client.publish.ConfigPublishRequest;
import com.taobao.wmcc.client.publish.ConfigPublishService;
import com.taobao.wmcc.client.publish.PublishTaskInfo;
import com.taobao.wmcc.client.publish.agateware.AgatewareConfigPublishRequest;
import com.taobao.wmcc.client.publish.agateware.AgatewareConfigPublishTaskQueryRequest;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;

import static com.taobao.wireless.orange.common.constant.Common.APP_NAME;

/**
 * WMCC 配置发布服务
 *
 * <AUTHOR>
 */
@Service
public class WmccPublishService {

    @HSFConsumer(serviceVersion = "${wmcc.hsf.version}", serviceGroup = "${wmcc.hsf.group}", clientTimeout = 10000)
    private ConfigPublishService configPublishService;

    public static final String CONFIG_NAMESPACE = "aserver.ali";
    public static final String CONFIG_TYPE = "ORANGE_PROBE";
    private static final String CHANNEL_TYPE = "agateware";
    private static final String TARGET_APP_NAME = "aserver";

    // WMCC 发布人(目前写死系统管理员)
    private static final String WMCC_PUBLISH_WORK_NO = "149016";

    /**
     * 发布探针配置到aserver
     *
     * @param configs
     * @return
     */
    public Long publishProbeToAserver(Collection<ConfigPublishRequest.Pair<String, String>> configs,
            Emergent emergent) {
        AgatewareConfigPublishRequest request = new AgatewareConfigPublishRequest();
        request.setPublishChannelType(CHANNEL_TYPE);
        request.setTargetAppName(TARGET_APP_NAME);
        request.setConfigs(configs);
        request.setEmergencyPublish(Emergent.y.equals(emergent));
        Result<Long> result = publish(request);
        return result.getData();
    }

    /**
     * 发布配置
     *
     * @param request 发布请求
     * @return 发布任务ID
     */
    @ServiceExceptionHandler(ExceptionEnum.WMCC_PUBLISH_ERROR)
    private Result<Long> publish(AgatewareConfigPublishRequest request) {
        request.setSrcAppName(APP_NAME);
        request.setConfigNamespace(CONFIG_NAMESPACE);
        request.setConfigType(CONFIG_TYPE);
        request.setBucId(WMCC_PUBLISH_WORK_NO);
        Result<Long> result = configPublishService.publish(request);
        if (!result.isSuccess()) {
            throw CommonException.getDynamicException(ExceptionEnum.WMCC_PUBLISH_ERROR,
                    StringUtils.isNotBlank(result.getMessage()) ? result.getMessage() : result.getResultCode());
        }
        return result;
    }

    /**
     * 获取正在发布的任务信息
     *
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.WMCC_QUERY_RUNNING_TASK_ERROR)
    public PublishTaskInfo getRunningPublishTaskInfo(String appKey, String indexType) {
        AgatewareConfigPublishTaskQueryRequest request = new AgatewareConfigPublishTaskQueryRequest();
        request.setConfigKey(String.format("%s_%s", appKey, indexType));
        request.setConfigNamespace(CONFIG_NAMESPACE);
        request.setConfigType(CONFIG_TYPE);
        request.setPublishChannelType(CHANNEL_TYPE);
        Result<PublishTaskInfo> result = configPublishService.getRunningPublishTaskInfo(request);
        if (!result.isSuccess()) {
            throw new CommonException(ExceptionEnum.WMCC_QUERY_RUNNING_TASK_ERROR, result.getMessage());
        }
        return result.getData();
    }

    /**
     * 获取任务发布状态
     *
     * @param taskId
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.WMCC_QUERY_TASK_STATUS_ERROR)
    public BriefTaskStatus getPublishTaskStatus(long taskId) {
        Result<BriefTaskStatus> result = configPublishService.getPublishTaskStatus(taskId);
        if (!result.isSuccess()) {
            throw new CommonException(ExceptionEnum.WMCC_QUERY_TASK_STATUS_ERROR, result.getMessage());
        }
        return result.getData();
    }

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     */
    @ServiceExceptionHandler(ExceptionEnum.WMCC_CANCEL_TASK_ERROR)
    public void cancelTask(long taskId) {
        Result<Boolean> result = configPublishService.cancelTask(taskId);
        if (!result.isSuccess()) {
            throw new CommonException(ExceptionEnum.WMCC_CANCEL_TASK_ERROR, result.getMessage());
        }
        if (BooleanUtils.isNotTrue(result.getData())) {
            throw CommonException.getDynamicException(ExceptionEnum.WMCC_CANCEL_TASK_FAIL, taskId);
        }
    }

    /**
     * 获取任务信息
     *
     * @param taskId
     * @return
     */
    @ServiceExceptionHandler(ExceptionEnum.WMCC_QUERY_TASK_INFO_ERROR)
    public PublishTaskInfo getPublishTaskInfo(long taskId) {
        Result<PublishTaskInfo> result = configPublishService.getPublishTaskInfo(taskId);
        if (!result.isSuccess()) {
            throw new CommonException(ExceptionEnum.WMCC_QUERY_TASK_INFO_ERROR, result.getMessage());
        }
        return result.getData();
    }
}
