package com.taobao.wireless.orange.external.acl;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.buc.acl.api.input.check.CheckPermissionsParam;
import com.alibaba.buc.acl.api.output.check.CheckPermissionsResult;
import com.alibaba.buc.acl.api.service.AccessControlService;
import com.alibaba.buc.acl.api.service.DataAccessControlService;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
@Slf4j
public class AclService {

    @Value("${acl.access.key}")
    private String accessKey;

    @HSFConsumer(serviceVersion = "${acl.hsf.version}", serviceGroup = "${acl.hsf.group}", clientTimeout = 10000)
    private AccessControlService accessControlService;

    @HSFConsumer(serviceVersion = "${acl.hsf.version}", serviceGroup = "${acl.hsf.group}", clientTimeout = 10000)
    private DataAccessControlService dataAccessControlService;

    // 应用管理权限
    public static final String ADMIN_PERMISSION = "orange_manage";

    public boolean checkPermission(String permissionName, Integer bucId) {
        CheckPermissionsParam checkPermissionsParam = new CheckPermissionsParam();
        checkPermissionsParam.setAccessKey(accessKey);
        checkPermissionsParam.setUserId(bucId);
        checkPermissionsParam.setPermissionNames(Arrays.asList(permissionName));
        CheckPermissionsResult result = accessControlService.checkPermissions(checkPermissionsParam);
        if (!result.isSuccess()) {
            throw CommonException.getDynamicException(ExceptionEnum.ACL_CHECK_PERMISSION_ERROR, result.getMsg());
        }
        if (CollectionUtils.isEmpty(result.getCheckPermissionResults())) {
            return false;
        }
        return result.getCheckPermissionResults().getFirst().isAccessible();
    }
}
