package com.taobao.wireless.orange.external.switchcenter;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;

import java.util.List;


/**
 * <AUTHOR>
 */
public class SwitchConfig {
    @AppSwitch(des = "新版控制台前端版本", level = Switch.Level.p1)
    public static String feVersion = "0.0.11";

    @AppSwitch(des = "combo 策略", level = Switch.Level.p1)
    public static String comboPolicy = "{\"enable\":false,\"url\":\"er.orange.tbcdn.cn\",\"minComboCount\":5,\"maxComboCount\":100,\"batchSize\":102400}";

    @AppSwitch(des = "系统公告", level = Switch.Level.p1)
    public static String announcement = "";

    @AppSwitch(des = "差异索引拉取间隔", level = Switch.Level.p1)
    public static List<Integer> diffProbeGapMinutes = List.of(60, 360, 1440);

    /**
     * 一期检测到老探针不同步，走该链路进行强制同步
     */
    @AppSwitch(des = "强制发布探针应用列表", level = Switch.Level.p1)
    public static List<String> forcePublishTextProbeAppKeys = List.of();

    /**
     * 正常 WMCC 发布完成分钟数
     * 用于新老链路探针一致性检测时兼容新老链路发布时间差
     */
    @AppSwitch(des = "正常 WMCC 发布完成分钟数", level = Switch.Level.p4)
    public static int maxWmccPublishMinutes = 10;
}
