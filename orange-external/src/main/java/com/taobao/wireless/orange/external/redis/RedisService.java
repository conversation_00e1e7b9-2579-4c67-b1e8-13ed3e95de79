package com.taobao.wireless.orange.external.redis;

import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.CredentialProviderFactory;
import com.alibaba.normandy.credential.ResourceNames;
import com.taobao.eagleeye.redis.clients.jedis.Jedis;
import com.taobao.eagleeye.redis.clients.jedis.JedisPool;
import com.taobao.eagleeye.redis.clients.jedis.JedisPoolConfig;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.time.Duration;

/**
 * Redis服务类，提供Redis的增删改查操作
 */
@Service
@Slf4j
public class RedisService {

    private final JedisPool jedisPool;
    private static final int DEFAULT_PORT = 6379;
    private static final int DEFAULT_TIMEOUT_MS = 3000;

    public RedisService(
            @Value("${redis.instance.id}") String instanceId,
            @Value("${redis.host}") String host
    ) {
        try {
            if (StringUtils.isBlank(instanceId)) {
                throw new IllegalArgumentException("redis.instance.id 不能为空");
            }
            if (StringUtils.isBlank(host)) {
                throw new IllegalArgumentException("redis.host 不能为空");
            }

            CredentialProvider credentialProvider = CredentialProviderFactory.getDefaultCredentialProvider();
            String resourceName = ResourceNames.ofAliyunKvStoreInstanceId(instanceId);
            Credential credential = credentialProvider.getCredential(resourceName);
            if (credential == null) {
                throw new IllegalStateException("获取 Tair 凭证失败");
            }
            String password = credential.getUsername() + ":" + credential.getPassword();

            JedisPoolConfig config = getJedisPoolConfig();

            String redisHost = instanceId + "." + host;
            this.jedisPool = new JedisPool(config, redisHost, DEFAULT_PORT, DEFAULT_TIMEOUT_MS, password);
            log.info("TairService initialized. host={}, instanceId={}", redisHost, instanceId);
        } catch (Exception e) {
            log.error("Failed to initialize TairService. instanceId={}, host={}", instanceId, host, e);
            throw new RuntimeException("Failed to initialize TairService", e);
        }
    }

    @NotNull
    private static JedisPoolConfig getJedisPoolConfig() {
        JedisPoolConfig config = new JedisPoolConfig();
        // 最大连接数（包含空闲与使用中的连接）。过小会导致排队等待，过大浪费资源
        config.setMaxTotal(50);
        // 最大空闲连接数。上限过大将长期占用连接，过小会频繁创建/销毁连接
        config.setMaxIdle(16);
        // 最小空闲连接数。连接回收线程尽量维持该空闲量，缓解突发借用延迟
        config.setMinIdle(4);
        // 连接池耗尽时是否阻塞等待。true 阻塞直到有可用连接或超时；false 直接抛异常
        config.setBlockWhenExhausted(true);
        // 阻塞等待的最大时长。仅在 blockWhenExhausted=true 时生效
        config.setMaxWait(Duration.ofSeconds(2));
        // 借用连接时进行健康检测（PING），避免拿到已失效连接
        config.setTestOnBorrow(true);
        // 归还连接时进行健康检测。通常关闭以降低开销
        config.setTestOnReturn(false);
        // 空闲检测线程周期性检测连接健康，配合时间间隔一起使用
        config.setTestWhileIdle(true);
        // 连接最小可驱逐的空闲存活时长。超过则可能被驱逐
        config.setMinEvictableIdleDuration(Duration.ofSeconds(60));
        // 空闲检测/驱逐线程运行的时间间隔。0 或负数表示不运行
        config.setTimeBetweenEvictionRuns(Duration.ofSeconds(30));
        // 每次空闲检测时检查的连接数。过大增加检测开销
        config.setNumTestsPerEvictionRun(3);
        return config;
    }

    @PreDestroy
    public void destroy() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.destroy();
            log.info("TairService destroyed successfully");
        }
    }

    /**
     * 设置键值对
     *
     * @param key   键
     * @param value 值
     * @return 操作结果
     */
    public String set(String key, String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.set(key, value);
            log.debug("SET key={}, result={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to SET key={}", key, e);
            throw new RuntimeException("Failed to set key-value", e);
        }
    }

    /**
     * 设置键值对并指定过期时间，如果键已存在则不操作
     *
     * @param key           键
     * @param value         值
     * @param expireSeconds 过期时间（秒）
     * @return 操作结果
     */
    public Long setnx(String key, String value, int expireSeconds) {
        try (Jedis jedis = jedisPool.getResource()) {
            Long result = jedis.setnx(key, value);
            if (result == 1) {
                jedis.expire(key, expireSeconds);
            }
            log.debug("SETNX key={}, expire={}s, result={}", key, expireSeconds, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to SETNX key={}, expire={}s", key, expireSeconds, e);
            throw new RuntimeException("Failed to set key-value with expiration", e);
        }
    }

    /**
     * 获取键对应的值
     *
     * @param key 键
     * @return 值
     */
    public String get(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            String value = jedis.get(key);
            log.debug("GET key={}, hit={}", key, value != null);
            return value;
        } catch (Exception e) {
            log.error("Failed to GET key={}", key, e);
            throw new RuntimeException("Failed to get value", e);
        }
    }

    /**
     * 删除键
     *
     * @param key 键
     * @return 删除的键数量
     */
    public Long delete(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            Long result = jedis.del(key);
            log.debug("DEL key={}, result={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to DEL key={}", key, e);
            throw new RuntimeException("Failed to delete key", e);
        }
    }

    /**
     * 批量删除键
     *
     * @param keys 键数组
     * @return 删除的键数量
     */
    public Long delete(String... keys) {
        try (Jedis jedis = jedisPool.getResource()) {
            Long result = jedis.del(keys);
            log.debug("DEL keysCount={}, result={}", keys == null ? 0 : keys.length, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to DEL multiple keys. count={}", keys == null ? 0 : keys.length, e);
            throw new RuntimeException("Failed to delete keys", e);
        }
    }

    /**
     * 设置键的过期时间
     *
     * @param key     键
     * @param seconds 过期时间（秒）
     * @return 操作结果
     */
    public Long expire(String key, int seconds) {
        try (Jedis jedis = jedisPool.getResource()) {
            Long result = jedis.expire(key, seconds);
            log.debug("EXPIRE key={}, seconds={}, result={}", key, seconds, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to EXPIRE key={}, seconds={}", key, seconds, e);
            throw new RuntimeException("Failed to set key expiration", e);
        }
    }

    /**
     * 增加有序集合成员的分数
     *
     * @param key       键
     * @param increment 增加的分数
     * @param member    成员
     * @return 增加后的分数
     */
    public Double zincrby(String key, double increment, String member) {
        try (Jedis jedis = jedisPool.getResource()) {
            Double result = jedis.zincrby(key, increment, member);
            log.debug("ZINCRBY key={}, increment={}, member={}, result={}", key, increment, member, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to ZINCRBY key={}, increment={}, member={}", key, increment, member, e);
            throw new RuntimeException("Failed to increment member score in sorted set", e);
        }
    }

    /**
     * 获取有序集合指定范围的成员（按分数从大到小）
     *
     * @param key   键
     * @param start 开始位置
     * @param end   结束位置
     * @return 成员集合
     */
    public Set<String> zrevrange(String key, long start, long end) {
        try (Jedis jedis = jedisPool.getResource()) {
            Set<String> result = jedis.zrevrange(key, start, end);
            log.debug("ZREVRANGE key={}, start={}, end={}, size={}", key, start, end, result == null ? 0 : result.size());
            return result;
        } catch (Exception e) {
            log.error("Failed to ZREVRANGE key={}, start={}, end={}", key, start, end, e);
            throw new RuntimeException("Failed to get reverse range from sorted set", e);
        }
    }

    /**
     * 将元素推入列表头部
     *
     * @param key    键
     * @param values 值数组
     * @return 推入后列表的长度
     */
    public Long lpush(String key, String... values) {
        try (Jedis jedis = jedisPool.getResource()) {
            Long result = jedis.lpush(key, values);
            log.debug("LPUSH key={}, valuesCount={}, result={}", key, values == null ? 0 : values.length, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to LPUSH key={}, valuesCount={}", key, values == null ? 0 : values.length, e);
            throw new RuntimeException("Failed to push to list", e);
        }
    }

    /**
     * 获取列表指定范围的元素
     *
     * @param key   键
     * @param start 开始位置
     * @param end   结束位置
     * @return 元素列表
     */
    public List<String> lrange(String key, long start, long end) {
        try (Jedis jedis = jedisPool.getResource()) {
            List<String> result = jedis.lrange(key, start, end);
            log.debug("LRANGE key={}, start={}, end={}, size={}", key, start, end, result == null ? 0 : result.size());
            return result;
        } catch (Exception e) {
            log.error("Failed to LRANGE key={}, start={}, end={}", key, start, end, e);
            throw new RuntimeException("Failed to get range from list", e);
        }
    }

    /**
     * 修剪列表，只保留指定范围的元素
     *
     * @param key   键
     * @param start 开始位置
     * @param end   结束位置
     * @return 操作结果
     */
    public String ltrim(String key, long start, long end) {
        try (Jedis jedis = jedisPool.getResource()) {
            String result = jedis.ltrim(key, start, end);
            log.debug("LTRIM key={}, start={}, end={}, result={}", key, start, end, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to LTRIM key={}, start={}, end={}", key, start, end, e);
            throw new RuntimeException("Failed to trim list", e);
        }
    }

    /**
     * 移除列表中指定的元素
     *
     * @param key   键
     * @param count 移除数量，0表示移除所有匹配的元素
     * @param value 要移除的值
     * @return 移除的元素数量
     */
    public Long lrem(String key, long count, String value) {
        try (Jedis jedis = jedisPool.getResource()) {
            Long result = jedis.lrem(key, count, value);
            log.debug("LREM key={}, count={}, result={}", key, count, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to LREM key={}, count={} value length={} ", key, count, value == null ? 0 : value.length(), e);
            throw new RuntimeException("Failed to remove from list", e);
        }
    }
}
