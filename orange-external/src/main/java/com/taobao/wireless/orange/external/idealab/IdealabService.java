package com.taobao.wireless.orange.external.idealab;

import com.alibaba.fastjson.JSON;
import com.taobao.common.keycenter.security.Cryptograph;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.external.idealab.model.IdealabPredictRequest;
import com.taobao.wireless.orange.external.idealab.model.IdealabPredictResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

import java.util.Collections;
import java.util.Map;


@Slf4j
@Service
public class IdealabService {

    // https://kc.alibaba-inc.com
    private final static String KEY_NAME = "orange_aone_key";

    // API路径常量
    private static final String API_PATH_TEMPLATE = "/api/aiapp/run/%s/latest";
    private static final String API_BASE_URL = "https://aistudio.alibaba-inc.com";

    // HTTP头常量
    private static final String HEADER_X_AK = "X-AK";

    private final String decryptedAccessKey;

    private final RestTemplate restTemplate;

    public IdealabService(
            @Value("${idealab.access.key.encrypt}") String accessKeyEncrypt,
            Cryptograph cryptograph
    ) {

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(10000);
        this.restTemplate = new RestTemplate(requestFactory);

        String ak = cryptograph.decrypt(accessKeyEncrypt, KEY_NAME);
        if (StringUtils.isBlank(ak)) {
            log.error("Idealab AccessKey 解密失败或为空");
            throw new CommonException(ExceptionEnum.IDEALAB_PARAMS_INVALID, "accessKey 解密失败");
        }
        this.decryptedAccessKey = ak;
    }

    /**
     * 调用 Idealab 预测接口
     *
     * @param appCode 应用代码，不能为空
     * @param request 预测请求对象，不能为空
     * @return 预测结果
     * @throws CommonException 当参数无效或调用失败时抛出
     */
    public String predict(String appCode, IdealabPredictRequest request) {
        // 参数校验
        if (StringUtils.isBlank(appCode)) {
            throw new CommonException(ExceptionEnum.IDEALAB_PARAMS_INVALID, "appCode 不能为空");
        }
        if (request == null) {
            throw new CommonException(ExceptionEnum.IDEALAB_PARAMS_INVALID, "request 不能为空");
        }

        // 构建请求URL
        String url = API_BASE_URL + String.format(API_PATH_TEMPLATE, appCode);

        try {
            // 构建HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.set(HEADER_X_AK, decryptedAccessKey);
            headers.setAccept(Collections.singletonList(MediaType.ALL));
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            String requestBody = JSON.toJSONString(request);
            HttpEntity<String> httpEntity = new HttpEntity<>(requestBody, headers);

            log.info("调用 Idealab 预测接口，URL: {}", url);

            // 发送HTTP请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    httpEntity,
                    String.class
            );

            // 检查HTTP状态码
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("调用 Idealab 接口失败，状态码: {}, 响应: {}", response.getStatusCode(), response.getBody());
                throw CommonException.getDynamicException(ExceptionEnum.IDEALAB_HTTP_ERROR, response.getStatusCode().value());
            }

            String responseBody = response.getBody();
            log.debug("调用 Idealab 预测接口成功，响应: {}", responseBody);

            // 解析响应
            IdealabPredictResponse idealabResponse = JSON.parseObject(responseBody, IdealabPredictResponse.class);
            if (idealabResponse == null) {
                log.error("解析 Idealab 返回结果失败，响应为空");
                throw new CommonException(ExceptionEnum.IDEALAB_PARSE_ERROR);
            }

            // 检查业务状态
            if (idealabResponse.getSuccess() == null || !idealabResponse.getSuccess() || idealabResponse.getData() == null) {
                String errorMsg = String.format("错误码: %s, 错误信息: %s",
                        idealabResponse.getErrorCode(), idealabResponse.getErrorMsg());
                log.error("调用 Idealab 接口业务失败: {}", errorMsg);
                throw CommonException.getDynamicException(ExceptionEnum.IDEALAB_BUSINESS_ERROR, errorMsg);
            }

            String content = idealabResponse.getData().getContent();
            return content == null ? "" : content;
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用 Idealab 接口异常，URL: {}", url, e);
            throw new CommonException(ExceptionEnum.IDEALAB_ERROR, e);
        }
    }

    /**
     * 调用 Idealab 预测接口（兼容原有方法签名）
     *
     * @param appCode     应用代码，不能为空
     * @param variableMap 变量映射，可为空
     * @return 预测结果
     * @throws CommonException 当参数无效或调用失败时抛出
     */
    public String predict(String appCode, Map<String, Object> variableMap) {
        // 构建默认请求对象
        IdealabPredictRequest.IdealabPredictRequestBuilder builder = IdealabPredictRequest.builder()
                .variableMap(variableMap)
                // 默认非流式
                .stream(false);

        IdealabPredictRequest request = builder.build();
        return predict(appCode, request);
    }
}
