<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.taobao.wireless</groupId>
        <artifactId>orange</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>orange-external</artifactId>
    <name>orange-external</name>
    <dependencies>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>orange-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>wmcc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.tair</groupId>
            <artifactId>tairjedis-sdk-singlepath</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-metaq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.ihr</groupId>
            <artifactId>amdplatform-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-diamond-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-notify-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.goc</groupId>
            <artifactId>changefree-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>tiga-release-console-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>tiga-release-expression-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-tair-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>acl.api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.mtl</groupId>
            <artifactId>mtl-open-sdk-consumer</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>securitysdk-logging-api</artifactId>
                    <groupId>com.alibaba.security</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.normandy.credential</groupId>
            <artifactId>normandy-credential-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.keycenter</groupId>
            <artifactId>keycenter-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>
</project>
